# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    locale: '%env(DEFAULT_LOCALE)%'
    server_env: '%env(SERVER_ENV)%'
    hasura_webhook_secret: '%env(HASURA_WEBHOOK_SECRET)%'
    env(MACHINE_NAME): 'not.specified'
    messenger_routing:
        App\Message\ErpProductMessage: erp-product
        App\Message\ErpPriceMessage: erp-price
        App\Message\ErpProductStockMessage: erp-product-stock
        App\Message\ErpCategoryMessage: erp-category
services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        public: false # Allows optimizing the container by removing unused services; this also means
        # fetching services directly from the container via $container->get() won't work.
        # The best practice is to be explicit about your dependencies anyway.
        bind:
            # Config for string autowiring
            $assets_cdn_images: '%env(string:ASSETS_CDN_IMAGES)%'
            $envoidunet_ws_url: '%env(ENVOIDUNET_WEBSERVICE_URL)%'
            $envoidunet_ws_account: '%env(ENVOIDUNET_WEBSERVICE_ACCOUNT)%'
            $envoidunet_ws_key: '%env(ENVOIDUNET_WEBSERVICE_KEY)%'
            $chronopost_ws_url: '%env(CHRONOPOST_WEBSERVICE_URL)%'
            $chronopost_ws_account: '%env(CHRONOPOST_WEBSERVICE_ACCOUNT)%'
            $chronopost_ws_password: '%env(CHRONOPOST_WEBSERVICE_PASSWORD)%'
            $mondial_relay_ws_url: '%env(MONDIAL_RELAY_WEBSERVICE_URL)%'
            $mondial_relay_ws_shop_identifier: '%env(MONDIAL_RELAY_WEBSERVICE_SHOP_IDENTIFIER)%'
            $mondial_relay_ws_private_key: '%env(MONDIAL_RELAY_WEBSERVICE_PRIVATE_KEY)%'
            $erpv1_api_end_point: '%env(ERPV1_API_ENDPOINT)%'
            $erpv1_payment_webservice_url: '%env(ERPV1_PAYMENT_WEBSERVICE_URL)%'
            $fo_cms_url: '%env(FO_ROOT_WEB_SSL)%'
            $erp_client_root_url: '%env(ERP_CLIENT_ROOT_URL)%'
            $erp_client_legacy_url: '%env(ERP_CLIENT_LEGACY_URL)%'
            $server_env: '%env(SERVER_ENV)%'
            $mailjet_transactional_key: '%env(string:MAILJET_TRANSACTIONAL_API_KEY)%'
            $mailjet_transactional_secret: '%env(string:MAILJET_TRANSACTIONAL_API_SECRET)%'
            $mailjet_call: '%env(bool:MAILJET_USE_API)%'
            $erp_gotenberg_endpoint: '%env(string:ERP_GOTENBERG_ENDPOINT)%'
            $payment_v2_url: '%env(string:PAYMENT_V2_API_URL)%'
            $payment_v2_internal_api_secret: '%env(string:PAYMENT_V2_INTERNAL_API_SECRET)%'
            $active_features: '%env(json:ACTIVE_FEATURES)%'

    app.s3_client:
        class: Aws\S3\S3Client
        arguments:
            -   version: '%env(S3_CLIENT_VERSION)%'
                region: '%env(S3_CLIENT_REGION)%'
                credentials:
                    key: '%env(S3_CLIENT_CREDENTIALS_KEY)%'
                    secret: '%env(S3_CLIENT_CREDENTIALS_SECRET)%'
                suppress_php_deprecation_warning: true
                
    app.s3_client.statics:
        class: Aws\S3\S3Client
        arguments:
            -   version: '%env(S3_CLIENT_VERSION)%'
                region: 'eu-west-2'
                credentials:
                    key: '%env(S3_CLIENT_CREDENTIALS_KEY)%'
                    secret: '%env(S3_CLIENT_CREDENTIALS_SECRET)%'
                suppress_php_deprecation_warning: true

    Aws\Sqs\SqsClient:
        class: Aws\Sqs\SqsClient
        arguments:
            -   region: '%env(SNSQS_CLIENT_REGION)%'
                endpoint: '%env(SNSQS_CLIENT_ENDPOINT)%'
                credentials:
                    key: '%env(SNSQS_CLIENT_CREDENTIALS_KEY)%'
                    secret: '%env(SNSQS_CLIENT_CREDENTIALS_SECRET)%'
                suppress_php_deprecation_warning: true

    Aws\Sns\SnsClient:
        class: Aws\Sns\SnsClient
        arguments:
            -   region: '%env(SNSQS_CLIENT_REGION)%'
                endpoint: '%env(SNSQS_CLIENT_ENDPOINT)%'
                credentials:
                    key: '%env(SNSQS_CLIENT_CREDENTIALS_KEY)%'
                    secret: '%env(SNSQS_CLIENT_CREDENTIALS_SECRET)%'
                suppress_php_deprecation_warning: true

    App\Messenger\Retry\NoRetryStrategy:
        class: App\Messenger\Retry\NoRetryStrategy
        public: true

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/App/*'
        exclude: '../src/App/{Rpc,DependencyInjection,Migrations,Tests,Kernel.php}'

    SonVideo\:
        resource: '../src/SonVideo/*'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    logger:
        alias: 'monolog.logger'
        public: true

    # Override Pomm configuratr - fix model connection override
    pomm.session_builder.configurator:
        class: 'App\Database\PgConfigurator'
        arguments: [ null ]
        public: true

    #
    # Autowiring
    #
    _instanceof:
        # setters
        Psr\Log\LoggerAwareInterface:
            calls:
                - [ setLogger, [ '@logger' ] ]
        App\Adapter\Serializer\SerializerAwareInterface:
            calls:
                - [ setSerializer, [ '@App\Adapter\Serializer\SerializerInterface' ] ]
        App\Contract\RouterAwareInterface:
            calls:
                - [ setRouter, [ '@router' ] ]
        App\Contract\DataLoaderAwareInterface:
            calls:
                - [ setDataLoader, [ '@App\DataLoader\EntityDataLoader' ] ]
        App\Contract\ServerEnvAwareInterface:
            calls:
                - [ setServerEnv, [ '%env(SERVER_ENV)%' ] ]
        SonVideo\Synapps\Client\RpcClientAwareInterface:
            calls:
                - [ setRpcClient, [ '@SonVideo\Synapps\Client\RpcClientService' ] ]
        App\Contract\ParameterModelAwareInterface:
            calls:
                - [ setParameterModel, [ '@App\Database\PgErpServer\SystemSchema\ParameterModel' ] ]
        App\Contract\LegacyPdoAwareInterface:
            calls:
                - [ setConnections, [ '@App\Sql\LegacyPdo', '@App\Sql\LegacyReadonlyPdo' ] ]
        # taggers
        PommProject\ModelManager\Model\Model:
            public: true
            tags: [ 'pomm.model' ]
        App\Database\PgDataWarehouse\PgDataWarehouseModel:
            public: true
            tags:
                - { name: 'pomm.model', session: 'pomm.session.pg_data_warehouse' }

        App\Contract\QueryConditionInterface:
            public: true
            tags: [ 'app.query_condition' ]
        App\Contract\QueryOperatorInterface:
            public: true
            tags: [ 'app.query_operator' ]
        App\Contract\DocumentInterface:
            public: true
            tags: [ 'app.document' ]
        SonVideo\Erp\Carrier\Contract\StickerGeneratorInterface:
            public: true
            tags: [ 'app.carrier.sticker_generator' ]
        SonVideo\Erp\Carrier\Contract\CarrierClientInterface:
            public: true
            tags: [ 'app.carrier.client' ]
        SonVideo\Erp\Carrier\Contract\ParcelTrackingRetrieverInterface:
            public: true
            tags: [ 'app.carrier.parcel_tracking_retriever' ]
        SonVideo\Erp\Webhook\Collection\Hasura\WebhookHandlerInterface:
            public: true
            tags: [ 'app.hasura.webhook_handler' ]
        SonVideo\Erp\MagicSearch\Contract\MagicSearchQueryProviderInterface:
            public: true
            tags: [ 'app.elastic_search.magic_search_query_provider' ]
        SonVideo\Erp\MagicSearch\Contract\MagicSearchIndexDataProviderInterface:
            public: true
            tags: [ 'app.elastic_search.magic_search_data_provider' ]
        SonVideo\Erp\Mailing\Contract\EmailDispatcherInterface:
            public: true
            tags: [ 'app.mailing.dispatcher' ]
        SonVideo\Erp\Document\Contract\DocumentGeneratorInterface:
            public: true
            tags: [ 'app.document.generator' ]
        SonVideo\Erp\CustomerOrder\Contract\CustomerOrderCreationDataMapperInterface:
            public: true
            tags: [ 'app.customer_order.creation_data_mapper' ]
        SonVideo\Erp\Spooler\Contract\SpoolerEventHandlerInterface:
            public: true
            tags: [ 'app.spooler.event_handler' ]
        SonVideo\Erp\DataWarehouse\Contract\TopicSynchronizerInterface:
            public: true
            tags: [ 'app.data_warehouse_spooler.topic_handler' ]
        SonVideo\Synapps\Client\SynappsObserverInterface:
            tags: [ 'synapps.observer' ]

    # Normalizer - Auto format dates
    serializer.normalizer.datetime:
        class: 'Symfony\Component\Serializer\Normalizer\DateTimeNormalizer'
        arguments:
            -   !php/const Symfony\Component\Serializer\Normalizer\DateTimeNormalizer::FORMAT_KEY: 'Y-m-d H:i:s'
        tags:
            - { name: serializer.normalizer, priority: -910 }

    # Normalizer - Hack (override pomm bridge)
    # As per usual, Pomm f*ck everything up ! It runs by default running its own denormalizer for flexible entities BEFORE everything else (priority: 10)
    # But since it does not support the class-string[] notation, it breaks all the ERP once we're not using our own limited implementation of the serializer
    # It's about time we get rid of it seriously... We just by-passing it for the time being
    pomm.serializer.denormalizer:
        class: 'PommProject\SymfonyBridge\Serializer\Normalizer\FlexibleEntityDenormalizer'
        arguments: [ '@pomm' ]
        tags:
            - { name: serializer.normalizer, priority: -1001 }

    #
    # PDO
    #
    App\Sql\LegacyPdo:
        class: App\Sql\LegacyPdo
        arguments:
            - '@doctrine.dbal.legacy_connection'

    App\Sql\LegacyReadonlyPdo:
        class: App\Sql\LegacyReadonlyPdo
        arguments:
            - '@doctrine.dbal.legacy_readonly_connection'

    App\Sql\Query\Where\WhereQueryBuilder:
        class: App\Sql\Query\Where\WhereQueryBuilder
        public: true
        arguments: [ !tagged app.query_condition, !tagged app.query_operator ]

    #
    # Collections
    #
    SonVideo\Erp\Carrier\Collection\StickerGeneratorCollection:
        class: SonVideo\Erp\Carrier\Collection\StickerGeneratorCollection
        public: true
        arguments: [ !tagged app.carrier.sticker_generator ]

    SonVideo\Erp\Carrier\Collection\CarrierClientCollection:
        class: SonVideo\Erp\Carrier\Collection\CarrierClientCollection
        public: true
        arguments: [ !tagged app.carrier.client ]

    SonVideo\Erp\Carrier\Collection\ParcelTrackingHandlerCollection:
        class: SonVideo\Erp\Carrier\Collection\ParcelTrackingHandlerCollection
        public: true
        arguments: [ !tagged app.carrier.parcel_tracking_retriever ]

    SonVideo\Erp\Webhook\Collection\Hasura\WebhookHandlerCollection:
        class: SonVideo\Erp\Webhook\Collection\Hasura\WebhookHandlerCollection
        public: true
        arguments: [ !tagged app.hasura.webhook_handler ]

    SonVideo\Erp\MagicSearch\Collection\MagicSearchQueryProviderCollection:
        class: SonVideo\Erp\MagicSearch\Collection\MagicSearchQueryProviderCollection
        public: true
        arguments: [ !tagged app.elastic_search.magic_search_query_provider ]

    SonVideo\Erp\MagicSearch\Collection\MagicSearchDataProviderCollection:
        class: SonVideo\Erp\MagicSearch\Collection\MagicSearchDataProviderCollection
        public: true
        arguments: [ !tagged app.elastic_search.magic_search_data_provider ]

    SonVideo\Erp\Mailing\Collection\EmailDispatcherCollection:
        class: SonVideo\Erp\Mailing\Collection\EmailDispatcherCollection
        public: true
        arguments: [ !tagged app.mailing.dispatcher ]

    SonVideo\Erp\Document\Collection\DocumentGeneratorCollection:
        class: SonVideo\Erp\Document\Collection\DocumentGeneratorCollection
        public: true
        arguments: [ !tagged app.document.generator ]

    SonVideo\Erp\CustomerOrder\Collection\CustomerOrderCreationDataMapperCollection:
        class: SonVideo\Erp\CustomerOrder\Collection\CustomerOrderCreationDataMapperCollection
        public: true
        arguments: [ !tagged app.customer_order.creation_data_mapper ]

    SonVideo\Erp\Spooler\Collection\SpoolerEventHandlerCollection:
        class: SonVideo\Erp\Spooler\Collection\SpoolerEventHandlerCollection
        public: true
        arguments: [ !tagged app.spooler.event_handler ]

    SonVideo\Erp\DataWarehouse\Collection\TopicSynchronizerCollection:
        class: SonVideo\Erp\DataWarehouse\Collection\TopicSynchronizerCollection
        public: true
        arguments: [ !tagged app.data_warehouse_spooler.topic_handler ]

    #
    # Miscellaneous Services
    #
    League\Flysystem\MountManager: '@oneup_flysystem.mount_manager'

    Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor: ~

    App\Contract\DataLoaderInterface:
        class: App\DataLoader\EntityDataLoader

    SonVideo\HalMiddlewareBundle\Domain\Menu\BuildMenu\MenuBuilderInterface:
        class: App\Menu\MenuBuilder

    #
    # Public services
    # This is considered as bad practice since Symfony 3.4
    # But we must do that for RPC controller which can't use proper dependency injection
    # https://symfony.com/blog/new-in-symfony-3-4-services-are-private-by-default
    #
    App\Adapter\Serializer\SerializerInterface:
        class: App\Adapter\Serializer\Serializer
        public: true

    SonVideo\Erp\AntiFraud\Manager\ArticleAntiFraudStatusChecker:
        public: true

    SonVideo\Erp\Article\Manager\ArticleMediaCloner:
        public: true

    SonVideo\Erp\Customer\Mysql\Repository\CustomerRepository:
        public: true

    SonVideo\Eav\EavRetriever:
        public: true

    SonVideo\Erp\System\Manager\SystemEventLogger:
        public: true

    SonVideo\Erp\Quote\Manager\QuoteForRpcRetriever:
        public: true

    SonVideo\Erp\CustomerOrder\Manager\CustomerOrderStatusUpdater:
        public: true

    SonVideo\Erp\CustomerOrder\Manager\CustomerOrderCreator:
        public: true

    SonVideo\Erp\Cms\Manager\CustomerOrderForCmsManager:
        public: true

    SonVideo\Erp\CustomerOrderPayment\Manager\BankRedirectionHandler:
        public: true

    SonVideo\Erp\CustomerOrderPayment\Manager\CustomerOrderPaymentCanceller:
        public: true

    SonVideo\Erp\CustomerOrderPayment\Manager\CustomerOrderPaymentCreator:
        public: true

    SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentWriteRepository:
        public: true

    SonVideo\Erp\Document\Manager\HtmlToPdfGenerator:
        class: SonVideo\Erp\Document\Manager\HtmlToPdfGenerator
        calls:
            - [ setProjectPath, [ '%kernel.project_dir%' ] ]

    SonVideo\Erp\Utility\Printer\CupsPrinter:
        class: SonVideo\Erp\Utility\Printer\CupsPrinter
        calls:
            - [ setCupsHost, [ '%env(CUPS_IP)%', '%env(CUPS_PORT)%' ] ]

    SonVideo\Erp\Metabase\Manager\MetabaseUrlGenerator:
        class: SonVideo\Erp\Metabase\Manager\MetabaseUrlGenerator
        arguments:
            - '%env(METABASE_URL)%'
            - '%env(METABASE_SECRET_KEY)%'

    #
    # Clients
    #
    App\Client\GraphQLClient:
        class: App\Client\GraphQLClient
        arguments:
            - '%env(ERP_GRAPHQL_URL)%'
            - '%env(ERP_GRAPHQL_ADMIN_API_KEY)%'

    SonVideo\Erp\Client\CarrierAWSClient:
        class: SonVideo\Erp\Client\CarrierAWSClient
        arguments:
            - '%env(CARRIER_AWS_ENDPOINT)%'

    SonVideo\Erp\Client\CarrierV2Client:
        class: SonVideo\Erp\Client\CarrierV2Client
        arguments:
            $base_url: '%env(CARRIER_V2_BASE_URL)%'

    SonVideo\Erp\Client\EslApiClient:
        class: SonVideo\Erp\Client\EslApiClient
        arguments:
            $base_url: '%env(ESL_BASE_URL)%'
            $login: '%env(ESL_LOGIN)%'
            $password: '%env(ESL_PASSWORD)%'

    App\Contract\ErpV1ApiClientInterface: '@SonVideo\Erp\Client\Erpv1ApiClient'
    SonVideo\Erp\Client\EasyLoungeApiClient:
        class: SonVideo\Erp\Client\EasyLoungeApiClient
        arguments:
            - '%env(EASYLOUNGE_API_ENDPOINT)%'
            - '%env(EASYLOUNGE_API_KEY)%'

    SonVideo\Erp\Client\GroupDigitalApiClient:
        arguments:
            $gd_login: '%env(GROUP_DIGITAL_API_LOGIN)%'
            $gd_password: '%env(GROUP_DIGITAL_API_PASSWORD)%'

    SonVideo\Erp\Client\OgoneApiClient:
        class: SonVideo\Erp\Client\OgoneApiClient
        arguments:
            - '%env(OGONE_STATUS_API_ENDPOINT)%'
            - '%env(OGONE_STATUS_API_USERID)%'
            - '%env(OGONE_STATUS_API_ACCOUNT1_PSPID)%'
            - '%env(OGONE_STATUS_API_ACCOUNT1_PSWD)%'
            - '%env(OGONE_STATUS_API_ACCOUNT2_PSPID)%'
            - '%env(OGONE_STATUS_API_ACCOUNT2_PSWD)%'

    SonVideo\Erp\ReleaseNote\Client\ReleaseNotesIoApiClient:
        class: SonVideo\Erp\ReleaseNote\Client\ReleaseNotesIoApiClient
        arguments:
            - '%env(RELEASE_NOTES_ENDPOINT)%'
            - '%env(RELEASE_NOTES_TOKEN)%'

    #
    # Elastic search
    #
    Elasticsearch\ClientBuilder:
        class: Elasticsearch\ClientBuilder
        calls:
            - [ 'setHosts', [ '%env(json:ELASTIC_SEARCH_URLS)%' ] ]

    Elasticsearch\Client:
        class: Elasticsearch\Client
        factory: [ '@Elasticsearch\ClientBuilder', 'build' ]

    App\Twig\CustomExtension:
        class: App\Twig\CustomExtension
        tags:
            - { name: twig.extension }

    XMLReader:
        class: XMLReader

    DateTime:
        class: DateTime

        
    App\Messenger\Routing\RoutingConfiguration:
        arguments:
            $routing: '%messenger_routing%'
        autowire: true
