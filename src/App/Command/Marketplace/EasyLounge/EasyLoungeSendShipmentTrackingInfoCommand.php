<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Command\Marketplace\EasyLounge;

use App\Command\MonitorizedCommand;
use App\Exception\NotFoundException;
use SonVideo\Erp\Client\EasyLoungeApiClient;
use SonVideo\Erp\CustomerOrder\Referential\CustomerOrderOrigin;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteProductRepository;
use SonVideo\Erp\Marketplace\EasyLounge\Mysql\Repository\EasyLoungeParcelTrackingReadRepository;
use SonVideo\Erp\Marketplace\EasyLounge\Mysql\Repository\EasyLoungeParcelTrackingWriteRepository;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class EasyLoungeSendShipmentTrackingInfoCommand.
 */
class EasyLoungeSendShipmentTrackingInfoCommand extends MonitorizedCommand
{
    protected static $defaultName = 'easylounge:send:shipment:tracking-info';

    private const CHECK_UUID = '31a2ae23-41fe-4ba1-8e84-63eae280a50a';

    private EasyLoungeParcelTrackingReadRepository $easy_lounge_parcel_tracking_read_repository;

    private EasyLoungeParcelTrackingWriteRepository $easy_lounge_parcel_tracking_write_repository;

    private EasyLoungeApiClient $easylounge_api_client;

    private DeliveryNoteProductRepository $delivery_note_product_repository;

    /** EasyLongeSendShipmentTrackingInfoCommand constructor. */
    public function __construct(
        DeliveryNoteProductRepository $delivery_note_product_repository,
        EasyLoungeParcelTrackingReadRepository $easy_lounge_parcel_tracking_read_repository,
        EasyLoungeParcelTrackingWriteRepository $easy_lounge_parcel_tracking_write_repository,
        EasyLoungeApiClient $easylounge_api_client,
        string $server_env,
        string $name = null
    ) {
        parent::__construct($server_env, $name);
        $this->delivery_note_product_repository = $delivery_note_product_repository;
        $this->easy_lounge_parcel_tracking_read_repository = $easy_lounge_parcel_tracking_read_repository;
        $this->easy_lounge_parcel_tracking_write_repository = $easy_lounge_parcel_tracking_write_repository;
        $this->easylounge_api_client = $easylounge_api_client;
    }

    /** {@inheritDoc} */
    protected function configure()
    {
        $this->setDescription('Send shipment tracking information\'s to easylounge via their webservice .');
    }

    /**
     * {@inheritDoc}
     *
     * @return int|void|null
     *
     * @throws NotFoundException
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->pingMonitoring(self::CHECK_UUID, 'start');
            $time_start = time();
            $output->writeln('Job starts...');

            $delivery_notes = $this->easy_lounge_parcel_tracking_read_repository->getParcelsNotSent();

            foreach ($delivery_notes as $delivery_note) {
                $delivery_ticket_id = $delivery_note->delivery_ticket_id;
                unset($delivery_note->delivery_ticket_id);

                $ezl_order_id = $delivery_note->ezl_order_id;
                unset($delivery_note->ezl_order_id);

                $creation_origine = $delivery_note->creation_origine;
                unset($delivery_note->creation_origine);

                $delivery_note_products = $this->delivery_note_product_repository->findAllForOneId($delivery_ticket_id);

                $delivery_note->Skus = array_map(static fn (array $product) => $product['sku'], $delivery_note_products);

                try {
                    if (CustomerOrderOrigin::EASY_LOUNGE === $creation_origine) {
                        $this->easylounge_api_client->post(
                            sprintf('/order-api/order/%s/tracking', $ezl_order_id),
                            $delivery_note
                        );
                    }

                    $this->easy_lounge_parcel_tracking_write_repository->markParcelAsSent($delivery_ticket_id);
                } catch (\Exception $exception) {
                    $wrapped_exception = new \Exception(
                        sprintf(
                            '[EASYLOUNGE] An error occurred while sending the parcel tracking information with BL %s',
                            $delivery_ticket_id
                        ),
                        0,
                        $exception
                    );

                    $this->logger->error($exception->getMessage(), ['exception' => $wrapped_exception]);

                    $output->writeln('Continue...');
                }
            }

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));
            $this->pingMonitoring(self::CHECK_UUID);
        } catch (\Exception $e) {
            $this->pingMonitoring(self::CHECK_UUID, 'fail', $e->getMessage());

            throw $e;
        }

        return 0;
    }
}
