<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Command\Marketplace\EasyLounge;

use App\Command\MonitorizedCommand;
use App\Exception\SqlErrorMessageException;
use SonVideo\Erp\Client\EasyLoungeApiClient;
use SonVideo\Erp\Marketplace\EasyLounge\Entity\ProductToExportEntity;
use SonVideo\Erp\Marketplace\EasyLounge\Mysql\Repository\ExportedProductWriteRepository;
use SonVideo\Erp\Marketplace\EasyLounge\Mysql\Repository\ProductToExportReadRepository;
use SonVideo\Erp\Referential\Marketplace\EasyLounge;
use SonVideo\Erp\System\Manager\SystemDatetimeRangeUpdater;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class EasyLoungeProductsPushLastUpdatesCommand.
 */
class EasyLoungeProductsPushLastUpdatesCommand extends MonitorizedCommand
{
    protected static $defaultName = 'easylounge:products:push-last-updates';
    private const CHECK_UUID = '0262046e-b31a-4ff3-8343-1d4d1df93434';

    protected ProductToExportReadRepository $product_to_export_read_repository;
    protected ExportedProductWriteRepository $exported_product_write_repository;
    protected EasyLoungeApiClient $easy_lounge_api_client;
    protected SystemDatetimeRangeUpdater $system_datetime_range_updater;

    private int $product_sent_failures = 0;

    public function __construct(
        ProductToExportReadRepository $product_to_export_read_repository,
        ExportedProductWriteRepository $exported_product_write_repository,
        EasyLoungeApiClient $easy_lounge_api_client,
        SystemDatetimeRangeUpdater $system_datetime_range_updater,
        string $server_env,
        string $name = null
    ) {
        parent::__construct($server_env, $name);
        $this->product_to_export_read_repository = $product_to_export_read_repository;
        $this->exported_product_write_repository = $exported_product_write_repository;
        $this->easy_lounge_api_client = $easy_lounge_api_client;
        $this->system_datetime_range_updater = $system_datetime_range_updater;
    }

    /** {@inheritDoc} */
    protected function configure()
    {
        $this->setDescription('Push last updated products data to EasyLounge');
    }

    /**
     * {@inheritDoc}
     *
     * @throws SqlErrorMessageException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->pingMonitoring(self::CHECK_UUID, 'start');
            $time_start = time();
            $output->writeln('Job starts...');

            $this->system_datetime_range_updater->setFromLastStopToNow(
                EasyLounge::SYSTEM_VARIABLE_START_AT,
                EasyLounge::SYSTEM_VARIABLE_STOP_AT
            );

            // We check if the start and stop time has been updated
            // if not the cron may be in error and will process the same time interval
            $last_stop_time = new \DateTime(
                $this->system_datetime_range_updater->getLastVariableStopTime(EasyLounge::SYSTEM_VARIABLE_STOP_AT)
            );
            $diff = $time_start - $last_stop_time->getTimestamp();
            // 900sec is 15min
            if ($diff >= 900) {
                throw new \Exception("Last command finished more than 15 minutes ago ($diff sec ago)");
            }

            $this->product_sent_failures = 0;
            foreach ($this->product_to_export_read_repository->findAll() as $product_to_export) {
                $output->writeln(sprintf('Export product %s...', $product_to_export->sku));
                $this->exportProductToEasylounge($product_to_export);
                $this->exported_product_write_repository->upsertProductChecksum($product_to_export);
            }

            if ($this->product_sent_failures > 0) {
                $this->logger->warning(sprintf('%d product(s) failed to be sent.', $this->product_sent_failures));
                $message = '[EASYLOUNGE] One or more error occurred while pushing products update.';

                $this->logger->error($message, ['exception' => new \Exception($message)]);
            }

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));
            $this->pingMonitoring(self::CHECK_UUID);
        } catch (\Exception $e) {
            $this->pingMonitoring(self::CHECK_UUID, 'fail', $e->getMessage());

            throw $e;
        }

        return 0;
    }

    protected function exportProductToEasylounge(ProductToExportEntity $product)
    {
        try {
            $this->easy_lounge_api_client->post(sprintf('/product-api/product/%s', $product->sku), $product);
        } catch (\Exception $exception) {
            ++$this->product_sent_failures;
            $this->logger->warning(sprintf('Failed to send product %s.', $product->sku));
            $this->logger->debug($exception->getMessage());
        }
    }
}
