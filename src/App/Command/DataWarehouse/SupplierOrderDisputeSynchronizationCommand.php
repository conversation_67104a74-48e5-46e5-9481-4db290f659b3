<?php

namespace App\Command\DataWarehouse;

use SonVideo\Erp\DataWarehouse\Manager\SupplierOrderDisputeSynchronizer;

class SupplierOrderDisputeSynchronizationCommand extends AbstractSynchronizationCommand
{
    protected static $defaultName = 'data-warehouse:synchronization:supplier-order-dispute';

    private const DESCRIPTION = 'Synchronization of supplier order disputes to data_warehouse';

    public function __construct(SupplierOrderDisputeSynchronizer $manager, string $name = null)
    {
        parent::__construct($manager, self::DESCRIPTION, $name);
    }
}
