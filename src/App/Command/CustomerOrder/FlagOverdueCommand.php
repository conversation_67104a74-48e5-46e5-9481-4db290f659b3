<?php

namespace App\Command\CustomerOrder;

use App\Sentry\SentryCronJobMonitor;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrderOverdueFlagger;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class FlagOverdueCommand extends Command
{
    protected static $defaultName = 'customer-order:flag-overdue';

    private CustomerOrderOverdueFlagger $overdue_flagger;
    private SentryCronJobMonitor $sentry_cron_job_monitor;

    public function __construct(
        string $server_env,
        SentryCronJobMonitor $sentry_cron_job_monitor,
        CustomerOrderOverdueFlagger $overdue_flagger,
        string $name = null
    ) {
        parent::__construct($server_env, $name);

        $this->overdue_flagger = $overdue_flagger;
        $this->sentry_cron_job_monitor = $sentry_cron_job_monitor;
    }

    /** {@inheritDoc} */
    protected function configure()
    {
        $this->setDescription(
            'Flag customer orders whose estimated delivery date is greater than the one displayed at creation'
        );
    }

    /** {@inheritDoc} */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->sentry_cron_job_monitor->start('customer-order-flag-overdue');

        try {
            $time_start = time();
            $output->writeln('Job starts...');

            $this->overdue_flagger->flagOverdue();
            $this->overdue_flagger->updateOutdated();

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));

            $this->sentry_cron_job_monitor->end();
        } catch (\Exception $exception) {
            $this->sentry_cron_job_monitor->fail();

            throw $exception;
        }

        return 0;
    }
}
