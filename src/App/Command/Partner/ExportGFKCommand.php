<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Command\Partner;

use App\Command\MonitorizedCommand;
use App\Exception\NotFoundException;
use League\Csv\Writer;
use SonVideo\Erp\Filesystem\Manager\ExportedFile;
use SonVideo\Erp\Filesystem\Manager\GFKFtp;
use SonVideo\Erp\Partner\Mysql\Repository\PartnerRepository;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class ExportGFKCommand.
 */
class ExportGFKCommand extends MonitorizedCommand
{
    protected static $defaultName = 'partner:export-gfk';

    private const CHECK_UUID = 'd1ca6066-e4c0-4d00-afff-f601fe5dd795';

    private PartnerRepository $partner_repository;
    private GFKFtp $gfk_ftp;

    /** ExportGFKCommand constructor. */
    public function __construct(
        string $server_env,
        PartnerRepository $partner_repository,
        ExportedFile $exported_file,
        GFKFtp $gfk_ftp,
        string $name = null
    ) {
        parent::__construct($server_env, $name);
        $this->partner_repository = $partner_repository;
        $this->gfk_ftp = $gfk_ftp;
    }

    /** configure */
    protected function configure()
    {
        $this->setDescription('Export GFK.');
    }

    /**
     * execute.
     *
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->pingMonitoring(self::CHECK_UUID, 'start');

            $time_start = time();
            $output->writeln('Job starts...');

            $output->writeln('Fetch data from database...');

            $records = $this->partner_repository->fetchGFKExport();

            if ([] === $records) {
                throw new NotFoundException('No record found for GFK export');
            }

            $output->writeln('Create CSV structure from records...');

            $csv = Writer::createFromString('');
            $csv->insertOne(array_keys($records[0]));
            $csv->insertAll($records);

            // free some memory, otherwise it fails on prod (out of memory)
            unset($records);

            $output->writeln('Write result in CSV file...');

            $content = $csv->getContent();

            // free more memory, otherwise it fails on prod (our of memory)
            unset($csv);

            $file_name = date('W.Y', strtotime('-1 week')) . '.csv';

            $file_path = $this->setLocalFile($file_name, $content);

            $output->writeln('FTP transfer...');

            $this->gfk_ftp->createOrOverwriteStream('', $file_name, $file_path);

            $output->writeln(sprintf('Job done in %d second(s).', time() - $time_start));

            $this->pingMonitoring(self::CHECK_UUID);
        } catch (\Exception $e) {
            $this->pingMonitoring(self::CHECK_UUID, 'fail', $e->getMessage());

            throw $e;
        }

        return 0;
    }

    private function setLocalFile(string $file_name, string $content): string
    {
        $file_path = sprintf('%s/%s', sys_get_temp_dir(), $file_name);
        if (false === file_put_contents($file_path, $content)) {
            throw new \UnexpectedValueException('Failed to write XML local file.');
        }

        return $file_path;
    }
}
