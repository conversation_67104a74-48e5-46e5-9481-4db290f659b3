<?php

namespace App\Command\Messaging\ErpProduct;

use SonVideo\Erp\Messaging\Manager\ErpProduct\ErpProductStockManager;

class ErpProductStockMessagingCommand extends AbstractErpProductScopeMessagingCommand
{
    protected static $defaultName = 'message:publish:erp-product-stock';
    protected static $description = 'Publishes erp product stock messages to message-oriented middleware';
    protected string $check_uuid = 'fabb1c24-04e1-4e9b-bab1-d8e12202dc3c';

    public function __construct(ErpProductStockManager $manager, string $server_env, string $name = null)
    {
        $this->manager = $manager;
        parent::__construct($server_env, $name);
    }

    protected function getParameterName(): string
    {
        return ErpProductStockManager::PARAMETER;
    }
}
