<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Shipment;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Shipment\Manager\ShipmentCloseManager;
use SonVideo\Erp\Shipment\Mysql\Repository\ShipmentRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class PutCloseShipmentController extends AbstractApiController
{
    /**
     * Close a shipment.
     *
     * @param ShipmentRepository $repository
     *
     * @Rest\Put(
     *      "/api/v1/shipment/{shipment_id}/close",
     *      name="put_close_shipmenet",
     *      requirements={"shipment_id"="^\d+$"}
     * )
     *
     * @OA\Tag(name="Shipment")
     * @OA\Response(response=200, description="Close the shipment with the ID {shipment_id}")
     * @Security(name="Bearer")
     */
    public function __invoke(int $shipment_id, ShipmentCloseManager $manager, Request $request): JsonResponse
    {
        $this->checkAuthorization([UserPermission::SHIPMENT_READ]);

        try {
            $manager->closeShipment($shipment_id, $this->getUserAuthorizationBearer($request));
            $response = JSendResponse::success([]);
        } catch (NotFoundException $e) {
            $response = JSendResponse::error($e->getMessage(), 404);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            $response = JSendResponse::error($exception->getMessage());
        }

        return $response;
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'put_close_shipmenet';
    }
}
