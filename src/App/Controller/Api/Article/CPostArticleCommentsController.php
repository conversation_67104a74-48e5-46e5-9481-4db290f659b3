<?php

namespace App\Controller\Api\Article;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Entity\ColumnHelper;
use App\Formatter\Http\JSendResponse;
use App\Params\FiltersParams;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\ArticleCommentManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

final class CPostArticleCommentsController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * Retrieve a filtered list.
     *
     * @Rest\Post("/api/v1/article-comments", name="cpost_article_comments")
     *
     * @Rest\RequestParam(
     *     name="fields",
     *     nullable=true,
     *     description="Only returns the specified fields per row, for returning a lighter response")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="created_at DESC",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="50",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Operation(
     *     tags={"article_comment"},
     *     summary="Retrieve a filtered list",
     *     description="Can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a filtered list"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(ParamFetcher $param_fetcher, ArticleCommentManager $manager): JsonResponse
    {
        try {
            $pager = $manager->getFilteredCollection(FiltersParams::fromParamFetcher($param_fetcher));

            $response = JSendResponse::success([
                'article_comments' => ColumnHelper::intersect(
                    $pager->getResults(),
                    $param_fetcher->get('fields') ?? []
                ),
                '_request' => $param_fetcher->all(),
                '_pager' => $pager,
            ]);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            $response = JSendResponse::error($exception->getMessage());
        }

        return $response;
    }
}
