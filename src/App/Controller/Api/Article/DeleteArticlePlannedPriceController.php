<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\ArticlePlannedPriceManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class DeleteArticlePlannedPriceController extends AbstractApiController
{
    /**
     * @Rest\Delete("/api/v1/article/planned-price/{article_planned_price_id}",
     * requirements={"article_planned_price_id"="^\d+$"},
     * name="delete_article_planned_price")
     *
     * @Operation(
     *     tags={"Article"},
     *     summary="Remove an article planned price",
     *     @OA\Response(
     *         response="200",
     *         description="OK - if the article planned price has been deleted successfully"
     *     )
     * )
     */
    public function __invoke(int $article_planned_price_id, ArticlePlannedPriceManager $manager): JsonResponse
    {
        $this->checkAuthorization([UserPermission::ARTICLE_PRICES_WRITE]);

        try {
            $deleted = $manager->delete($article_planned_price_id);

            return JSendResponse::success(['deleted' => $deleted]);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }
}
