<?php

namespace App\Controller\Api\Article;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Article\Manager\ArticleSalesChannelManager;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\SalesChannel\Exception\MarginValidationException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class PutArticleSalesChannelController extends AbstractApiController
{
    /**
     * @Rest\Put("/api/v1/article/{article_id}/sales-channel/{sales_channel_id}",
     *  requirements={"article_id"="^\d+$"},
     *  requirements={"sales_channel_id"="^\d+$"},
     * name="put_article_sales_channel")
     *
     * @Rest\RequestParam(
     *     name="selling_price",
     *     nullable=true,
     *     requirements=@Assert\Type("numeric"),
     *     description="new price for the sales channel for the given article")
     *
     * @Rest\RequestParam(
     *     name="is_active",
     *     nullable=true,
     *     requirements=@Assert\Type("bool"),
     *     description="new price for the sales channel for the given article")
     *
     * @Operation(
     *     tags={"Article"},
     *     summary="Update the sales channel related to an article",
     *     description="Update the price and visibility of the sales channel related to an article",
     *     @OA\Response(
     *         response="204",
     * *         description="ok - if the article sales channel has been updated successfully"
     *     )
     * )
     */
    public function __invoke(
        int $article_id,
        int $sales_channel_id,
        ParamFetcher $param_fetcher,
        ArticleSalesChannelManager $article_sales_channel_manager
    ): JsonResponse {
        /** @var int $sales_channel_id */
        $selling_price = $param_fetcher->get('selling_price');
        is_null($selling_price) ?: $this->checkAuthorization([UserPermission::ARTICLE_BUYERS_WRITE]);
        $is_active = $param_fetcher->get('is_active');
        is_null($is_active) ?: $this->checkAuthorization([UserPermission::ARTICLE_SALES_CHANNEL_WRITE]);

        try {
            if (null !== $is_active) {
                $article_sales_channel_manager->setIsActive($article_id, $sales_channel_id, $is_active);
            }
            if (null !== $selling_price) {
                $article_sales_channel_manager->updateSellingPriceWithTaxes(
                    $article_id,
                    $sales_channel_id,
                    $selling_price
                );
            }

            return JSendResponse::noContent();
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (MarginValidationException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (\Throwable $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
