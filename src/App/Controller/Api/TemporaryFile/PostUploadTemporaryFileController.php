<?php
/*
 * This file is part of sisyphus package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\TemporaryFile;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Filesystem\Manager\TemporaryFile;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostUploadTemporaryFileController extends AbstractApiController
{
    /**
     * @Rest\Post("/api/v1/temporary-file/upload")
     *
     * @Operation(
     *     tags={"Temporary file"},
     *     summary="Upload a temporary file",
     *     description="Upload a temporary file",
     *     @OA\Response(
     *         response="200",
     *         description="The path to the newly created temporary file"
     *     )
     * )
     */
    public function __invoke(Request $request, TemporaryFile $temporary_file): JsonResponse
    {
        $this->checkAuthorization([UserPermission::TEMPORARY_FILE_UPLOAD]);

        try {
            if (!$request->files->has('file')) {
                throw new \InvalidArgumentException('"file" key name was not found on the request payload');
            }

            $uploaded_file = $request->files->get('file');

            if (false === $uploaded_file->isValid()) {
                throw new \InvalidArgumentException('You must supply a file to upload');
            }

            $target_path = $temporary_file->upload($uploaded_file);

            // Send back the path of the temporary file for further usage
            return JSendResponse::success(['target_path' => $target_path]);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e]);

            return JSendResponse::error($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_temporary_file_upload';
    }
}
