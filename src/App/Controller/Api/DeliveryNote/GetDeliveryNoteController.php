<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\DeliveryNote;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteRepository;
use SonVideo\Erp\Repository\Wms\ProductLocationReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class GetDeliveryNoteController.
 */
class GetDeliveryNoteController extends AbstractApiController
{
    /**
     * Get information of a delivery note (This is not the route used in the PWA)
     * The WMS version is kept in order to not break the external dependencies.
     *
     * @Rest\Get("/api/v1/delivery-note/{delivery_note_id}",
     * requirements={"delivery_note_id"="^\d+$"},
     * name="get_delivery_note")
     *
     * @OA\Tag(name="Delivery note")
     * @OA\Response(response=200, description="Get information of a delivery note (This is not the route used in the
     *                             PWA)")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $delivery_note_id,
        DeliveryNoteRepository $delivery_note_repository,
        ProductLocationReadRepository $product_location_repository,
        QueryBuilder $query_builder
    ): JsonResponse {
        try {
            $delivery_note = $this->addProductLocations(
                $delivery_note_repository->findOneById($delivery_note_id)->toArray(),
                $query_builder,
                $product_location_repository
            );
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['delivery_note' => $delivery_note]);
    }

    /**
     * addProductLocations.
     *
     * @throws \Exception
     */
    protected function addProductLocations(
        array $delivery_note,
        QueryBuilder $query_builder,
        ProductLocationReadRepository $repository
    ): array {
        $product_ids = array_column($delivery_note['delivery_note_products'], 'product_id');

        // No product ids extracted (empty delivery note ?)
        if ([] === $product_ids) {
            return $delivery_note;
        }

        $where = [
            'warehouse_id' => ['_eq' => $delivery_note['warehouse_id']],
            'product_id' => ['_in' => $product_ids],
            'quantity' => ['_gt' => 0],
        ];

        $query_builder
            ->setWhere($where, ProductLocationReadRepository::COLUMNS_MAPPING)
            ->setPage(1, 999)
            ->setOrderBy('warehouse_display_order ASC, warehouse_name ASC, quantity DESC');

        $collected_products = $repository->findAllPaginated($query_builder)->getResults();

        foreach ($delivery_note['delivery_note_products'] as &$product) {
            $product_id = $product['product_id'];

            $product['locations'] = array_values(
                array_filter(
                    $collected_products,
                    fn ($collected): bool => (int) $collected->product_id === (int) $product_id
                )
            );
        }

        return $delivery_note;
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'get_delivery_note';
    }
}
