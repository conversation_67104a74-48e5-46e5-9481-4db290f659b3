<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\CustomerOrder;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Partner\Manager\AffiliationConsolidation;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class PostCustomerOrderAffiliationConsolidation.
 */
class PostCustomerOrderAffiliationConsolidation extends AbstractApiController
{
    /**
     * Get consolidated affiliated orders.
     *
     * @Rest\Post("/api/v1/customer-order/affiliation-consolidation",
     *     name="post_system_administration_affiliation_consolidation"
     * )
     *
     * @Rest\RequestParam(
     *     name="file_path",
     *     requirements=@Assert\Type("string"),
     *     description="CSV file path"
     * )
     * @OA\Tag(name="Customer Order")
     * @OA\Response(response=200, description="Consilidate affiliation file")
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        AffiliationConsolidation $affiliation_consolidation
    ): JsonResponse {
        try {
            $csv = $affiliation_consolidation->getCsvString($param_fetcher->get('file_path'));
            $csv_string = $csv->toString();
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_BAD_REQUEST);
        }

        return JSendResponse::success($csv_string);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_system_administration_affiliation_consolidation';
    }
}
