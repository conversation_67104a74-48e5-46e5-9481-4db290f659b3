<?php

namespace App\Controller\Api\AfterSaleService;

use App\Contract\DataLoaderAwareInterface;
use App\Controller\Api\AbstractApiController;
use App\DataLoader\MapToEntityTrait;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\AfterSaleService\Manager\ReturnNoteStickerUrlBuilder;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class GetReturnNoteStickerUrlController extends AbstractApiController implements DataLoaderAwareInterface
{
    use MapToEntityTrait;

    /**
     * @Rest\Get("/api/v1/product-return-note/{product_return_note_id}/sticker-url",
     *     requirements={"product_return_note_id"="^\d+$"},
     *     name="get_return_notes_sticker_url")
     *
     * @Operation(
     *     tags={"Product Retun Notes"},
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve url for sticker for product return note"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $product_return_note_id,
        ReturnNoteStickerUrlBuilder $return_note_sticker_url_builder
    ): JsonResponse {
        try {
            return JSendResponse::success([
                'url' => $return_note_sticker_url_builder->getUrl($product_return_note_id),
            ]);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }
}
