<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Document;

use App\Controller\Api\AbstractApiController;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Document\Collection\DocumentGeneratorCollection;
use SonVideo\Erp\Document\Contract\DocumentGeneratorInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints as Assert;

class GetDocumentGenerateInlinePdfController extends AbstractApiController
{
    /**
     * Generate an inline PDF document.
     *
     * @Rest\Get("/api/v1/document/generate-inline-pdf/{key}",
     *     requirements={"key"="^\w+$"},
     *     name="get_document_generate_inline_pdf"
     * )
     *
     * @Rest\QueryParam(
     *     name="options",
     *     default={},
     *     description="Options to pass to the document generator"
     * )
     *
     * @Rest\QueryParam(
     *     name="filename",
     *     default="document",
     *     description="Filename of the generated document without the .PDF extension",
     *     requirements=@Assert\Type("string"),
     * )
     *
     * @OA\Tag(name="Document")
     * @OA\Response(response=200, description="Generate an inline PDF document")
     * @Security(name="Bearer")
     */
    public function __invoke(
        string $key,
        ParamFetcher $param_fetcher,
        DocumentGeneratorCollection $document_generator_collection
    ): Response {
        $document = $document_generator_collection->getHandler($key);

        if (!$document instanceof DocumentGeneratorInterface) {
            throw new \InvalidArgumentException(sprintf('Document generator with key "%s" seems not to be implemented in the application', $key));
        }

        return new Response($document->generate($param_fetcher->get('options')), Response::HTTP_OK, [
            'Content-Type' => 'application/pdf',
            'Pragma' => 'public',
            'Cache-Control' => 'public, must-revalidate',
            'Content-Disposition' => sprintf('inline; filename="%s.pdf"', $param_fetcher->get('filename')),
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'get_document_generate_inline_pdf';
    }
}
