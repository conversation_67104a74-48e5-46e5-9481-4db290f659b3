<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Customer;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Customer\Manager\CustomerManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;

class PutAnonymizeCustomerController extends AbstractApiController
{
    /**
     * @Rest\Put("/api/v1/customer/anonymize/{customer_id}",
     * requirements={"customer_id"="^\d+$"},
     * name="put_anonymize_customer")
     * @OA\Tag(name="Customer")
     * @OA\Response(response=204, description="Anonymize Customer")
     * @Security(name="Bearer")
     */
    public function __invoke(int $customer_id, CustomerManager $customer_manager): JsonResponse
    {
        $this->checkAuthorization([UserPermission::CUSTOMER_ANONYMIZE]);

        try {
            $customer_manager->anonymize($customer_id);

            return JSendResponse::noContent();
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }
    }

    protected function getRouteName(): string
    {
        return 'put_anonymize_customer';
    }
}
