<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\PricingStrategy;

use App\Controller\Api\AbstractApiController;
use App\Entity\ColumnHelper;
use App\Formatter\Http\JSendResponse;
use App\Params\FiltersParams;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\PricingStrategy\Manager\PricingStrategyManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

class CPostPricingStrategiesController extends AbstractApiController
{
    /**
     * Retrieve a filtered list of pricing strategies.
     *
     * @Rest\Post("/api/v1/pricing-strategies", name="cpost_pricing_strategies")
     *
     * @Rest\RequestParam(
     *      name="fields",
     *      nullable=true,
     *      description="Only returns the specified fields per row, for returning a lighter response")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="pricing_strategy_id ASC",
     *     description="Sort field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements="asc|desc",
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     *
     * @Rest\RequestParam(
     *     name="limit",
     *     default="50",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Operation(
     *     tags={"Pricing Strategy"},
     *     summary="Retrieve a filtered list of pricing strategies",
     *     description="The strategies can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a filtered list of pricing strategies"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        PricingStrategyManager $manager
    ): JsonResponse {
        try {
            $pager = $manager->getFilteredCollection(FiltersParams::fromParamFetcher($param_fetcher));
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'pricing_strategies' => ColumnHelper::intersect($pager->getResults(), $param_fetcher->get('fields') ?? []),
            '_request' => $param_fetcher->all(),
            '_pager' => $pager,
        ]);
    }
}
