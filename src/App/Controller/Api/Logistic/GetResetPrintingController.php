<?php

namespace App\Controller\Api\Logistic;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Repository\PrintingRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

class GetResetPrintingController extends AbstractApiController
{
    /**
     * @Rest\Get("/api/v1/reset_printing", name="get_reset_printing")
     *
     * @Rest\QueryParam(name="id_transporteur",default="0", description="Reset printing")
     *
     * @OA\Tag(name="Reset Printing")
     * @OA\Response(response=200, description="Reset Printing")
     * @Security(name="Bearer")
     */
    public function __invoke(ParamFetcher $param_fetcher, PrintingRepository $printingRepository): JsonResponse
    {
        $this->checkAuthorization([UserPermission::RESET_PRINTING]);

        try {
            $nb_printing_reset = $printingRepository->callResetPrinting($param_fetcher->get('id_transporteur'));
        } catch (\Exception $e) {
            return JSendResponse::error('Error while resetting printing');
        }

        return JSendResponse::success($nb_printing_reset);
    }
}
