<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Inventory;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Inventory\Manager\InventoryCollectActivator;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class PutInventoryCollectController.
 */
class PutInventoryCollectActivateController extends AbstractApiController
{
    /**
     * Activate an inventory collect by its id.
     *
     * @Rest\Put("/api/v1/wms/inventory/{inventory_id}/collect/{inventory_collect_id}/activate",
     *     requirements={"inventory_id"="^\d+$", "inventory_collect_id"="^\d+$"},
     *     name="put_wms_inventory_collect_activate"
     * )
     *
     * @Operation(
     *     tags={"WMS"},
     *     summary="Activate an inventory collect by its id",
     *     @OA\Response(
     *         response="200",
     *         description="ok if the collect was activated successfully"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $inventory_id,
        int $inventory_collect_id,
        InventoryCollectActivator $inventory_collect_activer
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::INVENTORY_ADMINISTRATE]);

        try {
            $inventory_collect_activer->activate($inventory_id, $inventory_collect_id);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['ok']);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'put_wms_inventory_collect_activate';
    }
}
