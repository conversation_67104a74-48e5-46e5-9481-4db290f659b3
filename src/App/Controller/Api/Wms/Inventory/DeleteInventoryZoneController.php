<?php

namespace App\Controller\Api\Wms\Inventory;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Inventory\Manager\InventoryZoneManager;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class DeleteInventoryZoneController extends AbstractApiController
{
    /**
     * Delete a zone on an inventory.
     *
     * @Rest\Delete("/api/v1/wms/inventory/{inventory_id}/zone/{zone_id}",
     *     requirements={"inventory_id"="^\d+$", "zone_id"="^\d+$"},
     *     name="delete_wms_inventory_zone"
     * )
     *
     * @Operation(
     *     tags={"WMS"},
     *     summary="Delete a zone on a non closed inventory",
     *     @OA\Response(
     *         response="204",
     *         description="ok - if the inventory zone has been deleted successfully"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $inventory_id,
        int $zone_id,
        InventoryZoneManager $inventory_zone_manager
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::INVENTORY_ADMINISTRATE]);

        try {
            $inventory_zone_manager->delete($inventory_id, $zone_id);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::noContent();
    }
}
