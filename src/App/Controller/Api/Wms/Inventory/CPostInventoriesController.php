<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Inventory;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendFormatter;
use App\Formatter\Http\JSendResponse;
use App\Sql\Query\QueryBuilder;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryReadRepository;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Class CPostInventoriesController.
 */
class CPostInventoriesController extends AbstractApiController
{
    /**
     * Retrieve a list of inventories.
     *
     * @Rest\Post("/api/v1/wms/inventories", name="cpost_wms_inventories")
     *
     * @Rest\RequestParam(
     *     name="where",
     *     nullable=true,
     *     description="Filters")
     *
     * @Rest\RequestParam(
     *     name="order_by",
     *     requirements=@Assert\Type("string"),
     *     default="created_at DESC",
     *     description="Sort by field.")
     *
     * @Rest\RequestParam(
     *     name="order_direction",
     *     requirements=@Assert\Type("string"),
     *     nullable=true,
     *     description="Sort direction.")
     *
     * @Rest\RequestParam(
     *     name="page",
     *     default="1",
     *     requirements=@Assert\Type("integer"),
     *     description="Page offset")
     * @Rest\RequestParam(
     *     name="limit",
     *     default="25",
     *     requirements=@Assert\Type("integer"),
     *     description="Limit per page")
     *
     * @Operation(
     *     tags={"WMS"},
     *     summary="Retrieve a list of inventories",
     *     description="Inventories can be filtered on several criterion",
     *     @OA\Response(
     *         response="200",
     *         description="Retrieve a list of inventories"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        ParamFetcher $param_fetcher,
        QueryBuilder $query_builder,
        InventoryReadRepository $repository
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::INVENTORY_READ]);

        try {
            $params = $param_fetcher->all();
            $query_builder
                ->setWhere($param_fetcher->get('where') ?? [], InventoryReadRepository::COLUMNS_MAPPING)
                ->setPage($param_fetcher->get('page'), $param_fetcher->get('limit'))
                ->setOrderBy($param_fetcher->get('order_by'), $param_fetcher->get('order_direction'));

            $pager = $repository->findAllPaginated($query_builder);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'inventories' => JSendFormatter::convertNumeric($pager->getResults()),
            '_request' => JSendFormatter::convertNumeric($params),
            '_pager' => $pager,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cpost_wms_inventories';
    }
}
