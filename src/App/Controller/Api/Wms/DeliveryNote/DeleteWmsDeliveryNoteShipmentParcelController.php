<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\DeliveryNote;

use App\Entity\OptionalColumnsLoader;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Operation;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteRepository;
use SonVideo\Erp\Wms\Mysql\Repository\DeliveryNotePreparationReadRepository;
use SonVideo\Erp\Wms\Mysql\Repository\DeliveryNotePreparationWriteRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class DeleteWmsDeliveryNoteShipmentParcelController extends AbstractWmsDeliveryNoteShipmentParcelsController
{
    /**
     * Delete a parcel for a delivery note in preparation.
     *
     * @Rest\Delete("/api/v1/wms/delivery-note/{delivery_note_id}/parcel/{parcel_id}",
     *     requirements={"delivery_note_id"="^\d+$","parcel_id"="^\d+$"},
     *     name="delete_wms_delivery_note_shipment_parcel"
     * )
     *
     * @Operation(
     *     tags={"WMS delivery note"},
     *     summary="Delete a parcel for a delivery note in preparation",
     *     @OA\Response(
     *         response="200",
     *         description="A representation on the parcels after the deletion"
     *     )
     * )
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $delivery_note_id,
        int $parcel_id,
        DeliveryNoteRepository $delivery_note_repository,
        DeliveryNotePreparationWriteRepository $write_repository,
        DeliveryNotePreparationReadRepository $repository,
        OptionalColumnsLoader $optional_columns_loader
    ): JsonResponse {
        try {
            // will throw an exception if the delivery note does not exist
            $delivery_note_repository->findOneById($delivery_note_id);

            $write_repository->deleteParcel($parcel_id);

            $parcels = $this->getShipmentParcels($delivery_note_id, $optional_columns_loader, $repository);
        } catch (NotFoundException $exception) {
            return JSendResponse::error(
                sprintf('Le bon de livraison %s n\'existe pas', $delivery_note_id),
                Response::HTTP_NOT_FOUND
            );
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['parcels' => $parcels]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'delete_wms_delivery_note_shipment_parcel';
    }
}
