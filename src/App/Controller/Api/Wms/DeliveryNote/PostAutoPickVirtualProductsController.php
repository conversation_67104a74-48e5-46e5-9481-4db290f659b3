<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\DeliveryNote;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteRepository;
use SonVideo\Erp\Referential\UserPermission;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class PostAutoPickVirtualProductsController.
 */
class PostAutoPickVirtualProductsController extends AbstractApiController
{
    /**
     * To auto pick virtual products from a delivery note.
     *
     * @param int delivery_note_id
     *
     * @Rest\Post("/api/v1/wms/auto-pick/virtual-products/{delivery_note_id}",
     * requirements={"delivery_note_id"="^\d+$"},
     * name="post_wms_auto_pick_virtual_products")
     *
     * @OA\Tag(name="WMS - Delivery note")
     * @OA\Response(response=200, description="Auto pick virtual products from delivery note")
     * @Security(name="Bearer")
     */
    public function __invoke(int $delivery_note_id, DeliveryNoteRepository $delivery_note_repository): JsonResponse
    {
        $this->checkAuthorization([UserPermission::PICKING_CREATE]);

        try {
            $delivery_note_repository->autoPickVirtualProducts($delivery_note_id);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);

            return JSendResponse::error($exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return JSendResponse::success([]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_wms_auto_pick_virtual_products';
    }
}
