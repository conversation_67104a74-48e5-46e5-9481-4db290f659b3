<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\DeliveryNote;

use App\Entity\OptionalColumnsLoader;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteRepository;
use SonVideo\Erp\Wms\Mysql\Repository\DeliveryNotePreparationReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class GetWmsDeliveryNoteShipmentParcelsController extends AbstractWmsDeliveryNoteShipmentParcelsController
{
    /**
     * Get parcels for given delivery note, prepared or not.
     *
     * @Rest\Get("/api/v1/wms/delivery-note/{delivery_note_id}/shipment-parcels",
     * requirements={"delivery_note_id"="^\d+$"},
     * name="get_wms_delivery_note_shipment_parcels")
     *
     * @OA\Tag(name="WMS delivery note")
     * @OA\Response(response=200, description="Get parcels for given delivery note, prepared or not")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $delivery_note_id,
        DeliveryNoteRepository $delivery_note_repository,
        DeliveryNotePreparationReadRepository $repository,
        OptionalColumnsLoader $optional_columns_loader
    ): JsonResponse {
        try {
            // will throw an exception if the delivery note does not exist
            $delivery_note_repository->findOneById($delivery_note_id);

            $parcels = $this->getShipmentParcels($delivery_note_id, $optional_columns_loader, $repository);
        } catch (NotFoundException $exception) {
            return JSendResponse::error(
                sprintf('Le bon de livraison %s n\'existe pas', $delivery_note_id),
                Response::HTTP_NOT_FOUND
            );
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success(['parcels' => $parcels]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'get_wms_delivery_note_shipment_parcels';
    }
}
