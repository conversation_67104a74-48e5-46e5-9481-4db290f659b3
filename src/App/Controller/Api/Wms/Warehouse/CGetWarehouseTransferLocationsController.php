<?php
/*
 * This file is part of erp package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Warehouse;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Repository\Wms\TransferLocationReadRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class CGetWarehouseTransferLocationsController.
 */
class CGetWarehouseTransferLocationsController extends AbstractApiController
{
    /**
     * Get all active transfer locations for a warehouse.
     *
     * @Rest\Get("/api/v1/wms/warehouse/{warehouse_id}/transfer-locations",
     * requirements={"warehouse_id"="^\d+$"},
     * name="cget_warehouse_transfer_locations")
     * @OA\Tag(name="Warehouse transfer locations")
     * @OA\Response(response=200, description="All warehouse active transfer locations")
     * @Security(name="Bearer")
     */
    public function __invoke(int $warehouse_id, TransferLocationReadRepository $repository): JsonResponse
    {
        try {
            $response = $repository->findActiveByWarehouseId($warehouse_id);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), JsonResponse::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'transfer_locations' => $response,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'cget_warehouse_transfer_locations';
    }
}
