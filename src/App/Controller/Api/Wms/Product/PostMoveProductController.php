<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Product;

use App\Controller\Api\AbstractApiController;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use FOS\RestBundle\Request\ParamFetcher;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Referential\UserPermission;
use SonVideo\Erp\Repository\MoveRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class PostMoveProductController.
 */
class PostMoveProductController extends AbstractApiController
{
    /**
     * Post move product.
     *
     * @Rest\Post("/api/v1/wms/product/{product_id}/move",
     * requirements={"product_id"="^\d+$"},
     * name="post_move_product")
     *
     * @Rest\RequestParam(
     *  name="origin_location",
     * requirements={"origin_location"="^\d+$"},
     *  description="Origin location"
     * )
     * @Rest\RequestParam(
     *  name="destination_location",
     * requirements={"destination_location"="^\d+$"},
     *  description="Destination location"
     * )
     * @Rest\RequestParam(
     *  name="delivery_ticket_id",
     *  nullable=true,
     * requirements={"delivery_ticket_id"="^\d+$"},
     *  description="Delivery ticket id"
     * )
     * @Rest\RequestParam(
     *  name="move_mission_id",
     *  nullable=true,
     * requirements={"move_mission_id"="^\d+$"},
     *  description="Move mission id"
     * )
     * @Rest\RequestParam(
     *  name="quantity",
     * requirements={"quantity"="^\d+$"},
     *  description="Quantity of product"
     * )
     *
     * @OA\Tag(name="Product move")
     * @OA\Response(response=200, description="Move product")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $product_id,
        ParamFetcher $param_fetcher,
        MoveRepository $move_repository
    ): JsonResponse {
        $this->checkAuthorization([UserPermission::PRODUCT_MOVE_CREATE]);

        $params = $param_fetcher->all();
        $user = $this->getUser()->get('utilisateur');

        try {
            $delivery_ticket_id = empty($params['delivery_ticket_id']) ? null : (int) $params['delivery_ticket_id'];
            $move_mission_id = empty($params['move_mission_id']) ? null : (int) $params['move_mission_id'];

            $move_repository->createInternalProductMove(
                (int) $params['origin_location'],
                (int) $params['destination_location'],
                $product_id,
                $delivery_ticket_id,
                $move_mission_id,
                (int) $params['quantity'],
                $user
            );
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'post_move_product';
    }
}
