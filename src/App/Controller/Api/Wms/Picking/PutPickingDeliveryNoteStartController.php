<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Picking;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteRepository;
use SonVideo\Erp\Wms\Mysql\Repository\DeliveryNotePickingWriteRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class PutPickingDeliveryNoteStartController extends AbstractApiController
{
    /**
     * Move a parcel product in specified parcel number.
     *
     * @Rest\Put("/api/v1/wms/picking/{delivery_note_id}/start",
     *     requirements={"delivery_note_id"="^\d+$"},
     *     name="put_wms_picking_delivery_note_start"
     * )
     *
     * @OA\Tag(name="WMS Picking")
     * @OA\Response(response=204, description="Mark a delivery note has picking started")
     * @Security(name="Bearer")
     */
    public function __invoke(
        int $delivery_note_id,
        DeliveryNoteRepository $delivery_note_repository,
        DeliveryNotePickingWriteRepository $write_repository
    ): JsonResponse {
        try {
            // will throw an exception if the delivery note does exist
            $delivery_note_repository->findOneById($delivery_note_id);

            $write_repository->start(
                $delivery_note_id,
                $this->getUser()->get('utilisateur'),
                $this->getUser()->get('id_utilisateur')
            );
        } catch (NotFoundException $exception) {
            return JSendResponse::error(
                sprintf('Le bon de livraison %s n\'existe pas', $delivery_note_id),
                Response::HTTP_NOT_FOUND
            );
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::noContent();
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'put_wms_picking_delivery_note_start';
    }
}
