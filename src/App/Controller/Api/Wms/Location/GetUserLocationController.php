<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api\Wms\Location;

use App\Controller\Api\AbstractApiController;
use App\Exception\NotFoundException;
use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\Annotations as Rest;
use Nelmio\ApiDocBundle\Annotation\Security;
use OpenApi\Annotations as OA;
use SonVideo\Erp\Repository\LocationRepository;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class GetUserLocationController.
 */
class GetUserLocationController extends AbstractApiController
{
    /**
     * Get or create a user location (= chariot).
     *
     * @Rest\Get("/api/v1/wms/location/user/{user_id}/{warehouse_id}",
     * requirements={"user_id"="^\d+$", "warehouse_id"="^\d+$"},
     * name="get_user_location")
     *
     * @OA\Tag(name="User location")
     * @OA\Response(response=200, description="Details of the user location")
     * @Security(name="Bearer")
     */
    public function __invoke(int $user_id, int $warehouse_id, LocationRepository $location_repository): JsonResponse
    {
        try {
            $response = $location_repository->findOneByUserLocationCode($user_id, $warehouse_id);
        } catch (NotFoundException $exception) {
            return JSendResponse::error($exception->getMessage(), JsonResponse::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            return JSendResponse::error($exception->getMessage());
        }

        return JSendResponse::success([
            'location' => $response,
        ]);
    }

    /** getRouteName */
    protected function getRouteName(): string
    {
        return 'get_user_location';
    }
}
