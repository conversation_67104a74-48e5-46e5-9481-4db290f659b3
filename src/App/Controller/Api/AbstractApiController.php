<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Api;

use App\Formatter\Http\JSendResponse;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use PommProject\Foundation\Exception\SqlException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\User\Entity\UserEntity;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

/**
 * Class AbstractApiController.
 */
abstract class AbstractApiController extends AbstractFOSRestController implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    private CurrentUser $current_user;

    public function __construct(CurrentUser $current_user)
    {
        $this->current_user = $current_user;
    }

    /**
     * checkAuthorization.
     *
     * Check authorization for all given permissions.
     * If one is not authorized, throw an exception.
     */
    protected function checkAuthorization(array $permissions): self
    {
        $this->current_user->hasPermissions($permissions, CurrentUser::MODE_THROW_EXCEPTION);

        return $this;
    }

    /** Check authorization for given permission. */
    protected function hasAuthorization(string $permission): bool
    {
        return $this->current_user->hasPermissions([$permission]);
    }

    /** getUserAuthorizationBearer */
    protected function getUserAuthorizationBearer(Request $request): string
    {
        $authorization_token =
            $request->headers->has('Authorization') && strlen($request->headers->get('Authorization')) > 0
                ? $request->headers->get('Authorization')
                : $request->get('access_token');

        return trim(str_replace('Bearer', '', $authorization_token)) ?? '';
    }

    /** getUser */
    protected function getUser(): UserEntity
    {
        return $this->current_user->entity();
    }

    /** getAccountId */
    protected function getAccountId(): UuidInterface
    {
        return Uuid::fromString($this->getUser()->getUsername());
    }

    /**
     * handleException.
     *
     * @param \Exception $exception
     *
     * @throws AccessDeniedException
     */
    protected function handleException(\Throwable $exception): JsonResponse
    {
        $this->handleAccessDeniedException($exception);
        if ($exception instanceof NotFoundHttpException) {
            return JSendResponse::error('Resource not found.', JsonResponse::HTTP_NOT_FOUND);
        }

        if ($exception instanceof SqlException) {
            $this->logger->warning($exception->getMessage(), [
                'exception' => $exception,
            ]);

            return JSendResponse::failWithSqlException($this->logger, $exception);
        }

        if ($exception instanceof \InvalidArgumentException) {
            $this->logger->warning($exception->getMessage(), [
                'exception' => $exception,
            ]);

            return JSendResponse::fail(['message' => $exception->getMessage()], JsonResponse::HTTP_BAD_REQUEST);
        }

        if ($exception instanceof \LogicException) {
            $this->logger->warning($exception->getMessage(), [
                'exception' => $exception,
            ]);

            return JSendResponse::fail(
                ['message' => $exception->getMessage()],
                0 !== $exception->getCode() ? $exception->getCode() : JsonResponse::HTTP_CONFLICT
            );
        }

        $this->logger->error($exception->getMessage(), [
            'exception' => $exception,
        ]);

        return JSendResponse::error('An error occurred.');
    }

    /**
     * handleAccessDeniedException.
     *
     * @param \Exception $exception
     *
     * @throws AccessDeniedException
     */
    protected function handleAccessDeniedException(\Throwable $exception): self
    {
        if ($exception instanceof AccessDeniedException) {
            throw $exception;
        }

        return $this;
    }
}
