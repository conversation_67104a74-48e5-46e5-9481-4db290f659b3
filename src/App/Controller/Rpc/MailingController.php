<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Controller\Rpc;

use SonVideo\Erp\Mailing\Collection\EmailDispatcherCollection;
use SonVideo\Erp\Mailing\Contract\EmailDispatcherInterface;
use SonVideo\Erp\System\Manager\SystemEventLogger;
use SonVideo\RpcBundle\Controller\ControllerTrait;

class MailingController
{
    use ControllerTrait;

    /**
     * getClassMethods.
     *
     * Return list of rpc method(s) implemented in this class
     */
    public static function getClassMethods(): array
    {
        $domain = 'mailing:';

        return [
            $domain . 'handler' => ['key' => true, 'data' => true],
        ];
    }

    /** @throws \Exception */
    public function executeHandler(array $args): array
    {
        $params = $this->extractParameters($args, ['key', 'data']);

        try {
            /** @var EmailDispatcherInterface $email_dispatcher */
            $email_dispatcher = $this->getContainer()
                ->get(EmailDispatcherCollection::class)
                ->getHandler($params['key']);

            if (null === $email_dispatcher) {
                throw new \Exception(sprintf('No email dispatcher found for key "%s"', $params['key']));
            }

            $this->getContainer()
                ->get(SystemEventLogger::class)
                ->log($email_dispatcher->dispatch($params['data']));
        } catch (\Exception $exception) {
            // Log in Sentry
            $this->getContainer()
                ->get('logger')
                ->error($exception->getMessage(), ['exception' => $exception]);

            // fails silently
            return [
                'success' => false,
                'error_message' => 'Internal server error. Details can be found in the application error logs',
            ];
        }

        return ['success' => true];
    }
}
