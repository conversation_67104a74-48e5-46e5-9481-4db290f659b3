<?php

namespace App\Database\PgDataWarehouse\DataSchema;

use App\Database\BufferedQueries;
use App\Database\PgDataWarehouse\DataSchema\AutoStructure\CustomerOrderLine as CustomerOrderLineStructure;
use App\Database\PgDataWarehouse\PgDataWarehouseModel;

/**
 * CustomerOrderLineModel.
 *
 * Model class for table customer_order_line.
 *
 * @see Model
 */
class CustomerOrderLineModel extends PgDataWarehouseModel
{
    use BufferedQueries;

    /**
     * __construct().
     *
     * Model constructor
     */
    public function __construct()
    {
        $this->structure = new CustomerOrderLineStructure();
        $this->flexible_entity_class = CustomerOrderLine::class;
    }
}
