<?php

namespace App\Database\PgErpServer;

use PommProject\Foundation\Converter\ConverterHolder;
use PommProject\Foundation\Exception\ConverterException;
use PommProject\Foundation\Session\Session;
use PommProject\ModelManager\SessionBuilder as ModelManagerSessionBuilder;

class SessionBuilder extends ModelManagerSessionBuilder
{
    /** @throws ConverterException */
    protected function initializeConverterHolder(ConverterHolder $converter_holder)
    {
        parent::initializeConverterHolder($converter_holder);
        $converter_holder
            ->addTypeToConverter('String', 'public.citext')
            ->addTypeToConverter('String', 'task.task_status')
            ->addTypeToConverter('String', 'citext')
            ->addTypeToConverter('String', 'public.eav_filter_status')
            ->addTypeToConverter('String', 'public.ltree')
            ->addTypeToConverter('String', 'public.permission_owner_type');

        return $this;
    }

    protected function postConfigure(Session $session)
    {
        parent::postConfigure($session);
        $session->getListener('query')->attachAction(function ($name, array $data, $session): void {
            if ($session->hasLogger()) {
                if ('query:pre' == $name) {
                    // log query...
                    $session
                        ->getLogger()
                        ->debug(sprintf('[%s] -- %s', $data['session_stamp'], $data['sql']), ['_channel' => 'pomm']);

                    // ... on separated line from the parameters
                    $data['parameters']['_channel'] = 'pomm';
                    $session->getLogger()->debug('Parameters', $data['parameters']);
                } elseif ('query:post' == $name) {
                    $session
                        ->getLogger()
                        ->debug(sprintf('Result count : %d - Time : %s ms', $data['result_count'], $data['time_ms']), [
                            '_channel' => 'pomm',
                        ]);
                }
            }
        });

        return $this;
    }
}
