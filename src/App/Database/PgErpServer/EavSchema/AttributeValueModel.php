<?php

namespace App\Database\PgErpServer\EavSchema;

use App\Database\PgErpServer\EavSchema\AutoStructure\AttributeValue as AttributeValueStructure;
use PommProject\Foundation\Where;
use PommProject\ModelManager\Exception\ModelException;
use PommProject\ModelManager\Model\CollectionIterator;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\ModelTrait\WriteQueries;

/**
 * AttributeValueModel.
 *
 * Model class for table attribute_value.
 *
 * @see Model
 */
class AttributeValueModel extends Model
{
    use WriteQueries;

    /**
     * __construct().
     *
     * Model constructor
     */
    public function __construct()
    {
        $this->structure = new AttributeValueStructure();
        $this->flexible_entity_class = '\\' . AttributeValue::class;
    }

    /**
     * Retrieve attribute value data along with a linked subcategory attribute fields.
     *
     * @return CollectionIterator
     *
     * @throws ModelException
     */
    public function findByIdWithRelatedSubcategoryData(int $attribute_value_id, int $subcategory_id)
    {
        $sql = <<<SQL
        SELECT {projection}
        FROM {attribute_value} av
            LEFT JOIN {subcategory_attribute} sa USING (attribute_id)
        WHERE {condition}
        SQL;

        $projection = $this->createProjection()
            ->setField('filter_display_order', 'sa.display_order', 'int')
            ->setField('filter_status', 'sa.filter_status', 'public.eav_filter_status');
        $where = Where::create('av.attribute_value_id = $*', [$attribute_value_id])->andWhere(
            'sa.subcategory_id = $*',
            [$subcategory_id]
        );
        $subcategory_attribute_model = $this->getSession()->getModel(SubcategoryAttributeModel::class);

        $sql = strtr($sql, [
            '{projection}' => $projection->formatFieldsWithFieldAlias('av'),
            '{attribute_value}' => $this->structure->getRelation(),
            '{subcategory_attribute}' => $subcategory_attribute_model->getStructure()->getRelation(),
            '{condition}' => $where,
        ]);

        return $this->query($sql, $where->getValues(), $projection)->current();
    }
}
