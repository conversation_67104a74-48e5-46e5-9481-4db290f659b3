<?php

namespace App\Database\Orm\MysqlErp\Repository\Model;

use SonVideo\Orm\Definition as ORM;

/**
 * @ORM\Model(name="backOffice.competitor_pricing_tmp", engine="mysql")
 */
final class CompetitorPricingTmpModel
{
    /** @ORM\Column */
    public string $sku;

    /** @ORM\Column(primary_key=true) */
    public string $ean;

    /** @ORM\Column(primary_key=true) */
    public string $competitor_code;

    /** @ORM\Column */
    public float $selling_price_with_taxes;

    /** @ORM\Column */
    public float $shipping_price_with_taxes;

    /** @ORM\Column */
    public float $shipping_delay_in_days;

    /** @ORM\Column */
    public bool $is_available;

    /** @ORM\Column(primary_key=true) */
    public string $site;

    /** @ORM\Column */
    public string $url;

    /** @ORM\Column */
    public \DateTimeInterface $created_at;

    /** @ORM\Column */
    public \DateTimeInterface $updated_at;

    /** @ORM\Column */
    public \DateTimeInterface $crawled_at;
}
