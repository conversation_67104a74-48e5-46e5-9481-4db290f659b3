<?php

declare(strict_types=1);

namespace App\Database\Orm\MysqlErp\Repository;

use App\Database\ConnectionProvider\AbstractMysqlErpRepository;
use App\Database\Orm\MysqlErp\Repository\Entity\SynchronizableTopic;
use App\Database\Orm\MysqlErp\Repository\Model\SynchronizableTopicModel;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopicFeedback;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class SynchronizableTopicReadRepository extends AbstractMysqlErpRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = SynchronizableTopicModel::class;
    public const ENTITY_NAME = SynchronizableTopic::class;

    /** @return SynchronizableTopicFeedback[] */
    public function findHowManyRowsAreLocked(): int
    {
        $sql = <<<'MYSQL'
        SELECT COUNT(synchronizable_topic_id) AS locked_count
          FROM backOffice.synchronizable_topic
        WHERE status = 'LOCKED'
        MYSQL;

        return (int) $this->getConnection()->fetchOne($sql);
    }

    /** @return SynchronizableTopicFeedback[] */
    public function findAllTopicsTopicForFeedback(): array
    {
        $sql = <<<'MYSQL'
        SELECT
          topic, COUNT(synchronizable_topic_id) AS total
          FROM backOffice.synchronizable_topic
          WHERE status = 'LOCKED'
          GROUP BY topic;
        MYSQL;

        /** @var SynchronizableTopicFeedback[] $results */
        $results = $this->serializer->denormalize(
            $this->getConnection()->fetchAllAssociative($sql),
            SynchronizableTopicFeedback::class . '[]'
        );
        $remapped_by_key = [];

        foreach ($results as $result) {
            $remapped_by_key[$result->getTopic()] = $result;
        }

        return $remapped_by_key;
    }

    public function findAllLockedToSynchronize(): \Generator
    {
        $sql = <<<'MYSQL'
        SELECT
          synchronizable_topic_id,
          topic,
          content
          FROM backOffice.synchronizable_topic
          WHERE status = 'LOCKED'
        ORDER BY wait_until;
        MYSQL;

        $rows = $this->getConnection()->fetchAllAssociative($sql);
        foreach ($rows as $row) {
            yield $this->serializer->denormalize(
                $this->getDataMapper()->convertToPhpValues([$row], $this->getModel())[0],
                $this->getEntity()
            );
        }
    }
}
