<?php

declare(strict_types=1);

namespace App\Database\Orm\PgErp\SystemSchema\Repository\Model;

use SonVideo\Orm\Converter\Type\PgTimestampConverter;
use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation system.event.
 *
 * @ORM\Model(name="system.event", engine="postgresql")
 */
class EventModel
{
    /** @ORM\Column(primary_key=true) */
    public string $event_id;

    /** @ORM\Column(type=PgTimestampConverter::class) */
    public ?\DateTimeInterface $created_at = null;

    /** @ORM\Column */
    public ?string $name = null;

    /** @ORM\Column */
    public ?array $payload = null;
}
