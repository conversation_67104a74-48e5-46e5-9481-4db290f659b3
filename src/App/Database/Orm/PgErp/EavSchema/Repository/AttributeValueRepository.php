<?php

declare(strict_types=1);

namespace App\Database\Orm\PgErp\EavSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgErpRepository;
use App\Database\Orm\PgErp\EavSchema\Repository\Entity\AttributeValue;
use App\Database\Orm\PgErp\EavSchema\Repository\Model\AttributeValueModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class AttributeValueRepository extends AbstractPgErpRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = AttributeValueModel::class;
    public const ENTITY_NAME = AttributeValue::class;
}
