<?php

declare(strict_types=1);

namespace App\Database\Orm\PgErp\EavSchema\Repository;

use App\Database\ConnectionProvider\AbstractPgErpRepository;
use App\Database\Orm\PgErp\EavSchema\Repository\Entity\ProductValue;
use App\Database\Orm\PgErp\EavSchema\Repository\Model\ProductValueModel;
use SonVideo\Orm\Adapter\DBAL\ReadQueriesTrait;
use SonVideo\Orm\Adapter\DBAL\WriteQueriesTrait;

final class ProductValueRepository extends AbstractPgErpRepository
{
    use ReadQueriesTrait;
    use WriteQueriesTrait;

    public const MODEL_NAME = ProductValueModel::class;
    public const ENTITY_NAME = ProductValue::class;
}
