<?php

declare(strict_types=1);
/**
 * SafetyStock Definition.
 *
 * Structure class for relation data.safety_stock.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 */

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model;

use SonVideo\Orm\Converter\Type\PgTimestampConverter;
use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation data.safety_stock.
 *
 * @ORM\Model(name="data.safety_stock", engine="postgresql")
 */
class SafetyStockModel
{
    /** @ORM\Column(type=PgTimestampConverter::class, primary_key=true) */
    public \DateTimeInterface $snapshotted_at;

    /** @ORM\Column */
    public int $brand_id;

    /** @ORM\Column(primary_key=true) */
    public int $product_id;

    /** @ORM\Column */
    public int $subcategory_id;

    /** @ORM\Column */
    public int $supplier_id;

    /** @ORM\Column */
    public ?int $safety_stock = null;

    /** @ORM\Column */
    public ?float $shipping_delay = null;
}
