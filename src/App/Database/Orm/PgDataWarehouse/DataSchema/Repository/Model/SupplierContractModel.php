<?php

declare(strict_types=1);
/**
 * SupplierContract Definition.
 *
 * Structure class for relation data.supplier_contract.
 *
 * Class and fields comments are inspected from table and fields comments.
 * Just add comments in your database and they will appear here.
 */

namespace App\Database\Orm\PgDataWarehouse\DataSchema\Repository\Model;

use SonVideo\Orm\Converter\Type\JsonConverter;
use SonVideo\Orm\Definition as ORM;

/**
 * Structure class for relation data.supplier_contract.
 *
 * @ORM\Model(name="data.supplier_contract", engine="postgresql")
 */
class SupplierContractModel
{
    /** @ORM\Column(primary_key=true) */
    public int $supplier_contract_id;

    /** @ORM\Column() */
    public int $supplier_id;

    /** @ORM\Column() */
    public int $year;

    /** @ORM\Column */
    public ?int $brand_id = null;

    /** @ORM\Column */
    public ?string $discount_description = null;

    /** @ORM\Column(type=JsonConverter::class) */
    public array $pam = [];

    /** @ORM\Column(type=JsonConverter::class) */
    public array $rfa = [];

    /** @ORM\Column(type=JsonConverter::class) */
    public array $additional_rewards = [];

    /** @ORM\Column */
    public float $unconditional_discount;
}
