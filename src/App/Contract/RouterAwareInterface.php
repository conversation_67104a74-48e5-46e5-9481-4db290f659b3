<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Contract;

use Symfony\Component\Routing\Router;

/**
 * Interface RouterAwareInterface.
 */
interface RouterAwareInterface
{
    /**
     * setRouter.
     *
     * @return mixed
     */
    public function setRouter(Router $router);
}
