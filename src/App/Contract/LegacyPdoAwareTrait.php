<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Contract;

use App\Sql\LegacyPdo;
use App\Sql\LegacyReadonlyPdo;

trait LegacyPdoAwareTrait
{
    protected LegacyPdo $legacy_pdo;

    /** @var LegacyReadonlyPdo */
    protected $legacy_readonly_pdo;

    public function setConnections(LegacyPdo $main, LegacyReadonlyPdo $slave): LegacyPdoAwareInterface
    {
        $this->legacy_pdo = $main;
        $this->legacy_readonly_pdo = $slave;

        return $this;
    }
}
