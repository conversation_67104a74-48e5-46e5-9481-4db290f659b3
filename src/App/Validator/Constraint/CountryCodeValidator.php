<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Validator\Constraint;

use SonVideo\Erp\Country\Entity\CountryEntity;
use SonVideo\Erp\Country\Mysql\Repository\CountryRepository;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class CountryCodeValidator extends ConstraintValidator
{
    private CountryRepository $country_repository;

    /**
     * https://symfony.com/doc/current/service_container/calls.html.
     *
     * @required
     */
    public function __construct(CountryRepository $country_repository)
    {
        $this->country_repository = $country_repository;
    }

    /** {@inheritdoc} */
    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof CountryCode) {
            throw new UnexpectedTypeException($constraint, CountryCode::class);
        }

        if (null === $value) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ given_country_code }}', 'null')
                ->addViolation();

            return;
        }

        $result = $this->country_repository->getFrom2LettersCode($value);

        if (!$result instanceof CountryEntity) {
            $this->context
                ->buildViolation($constraint->message)
                ->setParameter('{{ given_country_code }}', $value)
                ->addViolation();
        }
    }
}
