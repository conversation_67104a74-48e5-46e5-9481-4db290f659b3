<?php

namespace App\Menu;

use <PERSON>\Uuid\UuidInterface;
use Son<PERSON>ideo\Erp\Referential\UserPermission;
use SonVideo\HalMiddlewareBundle\Application\Gateway\Owner\OwnerPermissionQueryGatewayInterface;
use SonVideo\HalMiddlewareBundle\Domain\Menu\BuildMenu\Menu;
use SonVideo\HalMiddlewareBundle\Domain\Menu\BuildMenu\MenuBuilderInterface;
use SonVideo\HalMiddlewareBundle\Domain\Menu\BuildMenu\NormalizableMenuInterface;
use SonVideo\HalMiddlewareBundle\Domain\Menu\BuildMenu\Route;
use SonVideo\HalMiddlewareBundle\Domain\Menu\BuildMenu\SubMenu;
use SonVideo\HalMiddlewareBundle\Domain\Permission\ScopedPermission;

class MenuBuilder implements MenuBuilderInterface
{
    private OwnerPermissionQueryGatewayInterface $gateway;

    /** @var ScopedPermission[] */
    private array $permissions = [];

    public function __construct(OwnerPermissionQueryGatewayInterface $gateway)
    {
        $this->gateway = $gateway;
    }

    public function build(string $profile, string $username, UuidInterface $account_id): NormalizableMenuInterface
    {
        $menu = new Menu();

        $this->permissions = array_map(
            static fn ($permission): string => $permission->getValue(),
            $this->gateway->getAssignedScopedPermissionsFor($account_id)->extract()
        );

        if ([] !== $this->permissions) {
            $menu = $this->addMenuItem($menu);
        }

        return $menu;
    }

    protected function addMenuItem(Menu $menu): Menu
    {
        $sub_menu = new SubMenu('Commandes');
        $this->addCustomerOrderMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Clients');
        $this->addCustomerMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Wiki');
        $this->addWikiMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Paiements');
        $this->addPaymentMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Produits');
        $this->addProductMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Logistique');
        $this->addStockMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Statistiques');
        $this->addStatisticsMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Administration');
        $this->addAdministrationMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Comptabilité');
        $this->addAccountingMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Magasins');
        $this->addRetailStoreMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Achats');
        $this->addPurchaseMenuItems($sub_menu);

        $menu->addSubMenu($sub_menu);

        return $menu;
    }

    protected function addCustomerOrderMenuItems(SubMenu $menu): MenuBuilder
    {
        $this->addRouteIfPermitted(
            $menu,
            new Route('Traçabilité BL', '/erp/delivery-note/history', [
                '^/erp/delivery-note/history?',
                '^/erp/delivery-note/history/(\d+)?',
            ]),
            []
        );

        $this->addRouteIfPermitted(
            $menu,
            new Route('Emports dépot', '/customer-order/store-pickups', ['^/customer-order/store-pickups?']),
            []
        );

        $sub_menu = new SubMenu('Par statut');

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Urgentes', '/customer-order/urgents', ['^/customer-order/urgents?']),
            []
        );

        $this->addRouteIfPermitted(
            $menu,
            new Route('Consolidation des affiliations', '/customer-order/affiliation', [
                '^/customer-order/affiliation',
            ]),
            []
        );

        $this->addRouteIfPermitted(
            $menu,
            new Route('Retards de livraison', '/customer-order/overdue', ['^/customer-order/overdue']),
            []
        );

        $this->addRouteIfPermitted(
            $menu,
            new Route('Suivi', '/customer-order/followup', ['^/customer-order/followup']),
            []
        );

        $this->addRouteIfPermitted($menu, new Route('Édition commande (v2)', null, ['^/customer-orders/(\d+)']), []);

        $menu->addSubMenu($sub_menu);

        return $this;
    }

    protected function addCustomerMenuItems(SubMenu $menu): MenuBuilder
    {
        $sub_menu = new SubMenu('Chercher');

        $this->addRouteIfPermitted($sub_menu, new Route('Fiche client 360°', null, ['^/customer/(\d+)']), []);

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Offre / Devis');

        $this->addRouteIfPermitted($sub_menu, new Route('Recherche', '/quotes', ['^/quotes']), []);

        $this->addRouteIfPermitted($sub_menu, new Route('Édition', null, ['^/quote/(\d+)']), []);

        $menu->addSubMenu($sub_menu);

        return $this;
    }

    protected function addWikiMenuItems(SubMenu $menu): MenuBuilder
    {
        $this->addRouteIfPermitted(
            $menu,
            new Route('Base de connaissance', '/wiki/knowledge-base', ['^/wiki/knowledge-base']),
            []
        );

        return $this;
    }

    protected function addPaymentMenuItems(SubMenu $menu): MenuBuilder
    {
        $sub_menu = new SubMenu('Remises');

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Paiements', '/customer-order/payment', ['^/customer-order/payment'])
        );

        $menu->addSubMenu($sub_menu);

        return $this;
    }

    protected function addProductMenuItems(SubMenu $menu): MenuBuilder
    {
        $sub_menu = new SubMenu('Article');

        // Article V2
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Article', null, ['^/articles/[A-Z0-9]{1}[\-_A-Z0-9]{0,39}/.*']),
            []
        );

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Stratégie de prix', '/pricing-strategy/pricing-strategies', [
                '^/pricing-strategy/pricing-strategies',
            ]),
            [UserPermission::ATTRIBUTE_READ]
        );
        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Catégories');
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Liste des catégories', '/category/categories', ['^/category/categories']),
            [UserPermission::ATTRIBUTE_READ]
        );
        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('EAV');
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Gestion des attributs', '/eav/attributes', ['^/eav/attributes']),
            [UserPermission::ATTRIBUTE_READ]
        );
        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Sous-catégories');
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Liste des sous-catégories', '/category/subcategories', ['^/category/subcategories']),
            []
        );
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Edition de la sous-catégorie', null, ['^/category/subcategory/(\d+)/edit']),
            []
        );
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Attributs Associés (EAV)', null, ['^/category/subcategory/(\d+)/attributes']),
            [UserPermission::ATTRIBUTE_READ]
        );
        $menu->addSubMenu($sub_menu);

        return $this;
    }

    protected function addStockMenuItems(SubMenu $menu): MenuBuilder
    {
        $sub_menu = new SubMenu('Expéditions');
        $this->addRouteIfPermitted($sub_menu, new Route('Liste des Expéditions', '/shipments', ['^/shipments']), [
            UserPermission::SHIPMENT_READ,
        ]);
        $this->addRouteIfPermitted($sub_menu, new Route('Transferts', '/transfers/stalled', ['^/transfers/.*']), []);

        $this->addRouteIfPermitted($sub_menu, new Route('Stock de securite', '/safety-stock', ['^/safety-stock/.*']), []);
        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('WMS');
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Missions de rangements', '/erp/wms/move-missions', ['^/erp/wms/move-missions']),
            [UserPermission::MOVE_MISSION_READ]
        );
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Inventaires', '/erp/wms/inventories', ['^/erp/wms/inventories']),
            [UserPermission::INVENTORY_CREATE]
        );
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Détail de l\'inventaire', null, ['^/erp/wms/inventory/(\d+)']),
            [UserPermission::INVENTORY_CREATE]
        );
        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Emport');
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Prépa emport Champigny', '/full-dashboard/store-pickup/21/stock-dashboard', [
                '^/full-dashboard/store-pickup/21/stock-dashboard',
            ]),
            []
        );
        $menu->addSubMenu($sub_menu);

        return $this;
    }

    protected function addStatisticsMenuItems(SubMenu $menu): MenuBuilder
    {
        $sub_menu = new SubMenu('Interne');

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Tableau de bord (new)', '/statistics/dashboard', ['^/statistics/dashboard']),
            [UserPermission::STATISTICS_GLOBAL_READ]
        );

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Commissions vendeurs magasin', '/retail-store/commission-dashboard', [
                '^/retail-store/commission-dashboard',
            ]),
            [UserPermission::RETAIL_STORE_COMMISSIONS_READ]
        );

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Commissions vendeurs call-center', '/call-center/commission-dashboard', [
                '^/call-center/commission-dashboard',
            ]),
            [UserPermission::CALL_CENTER_COMMISSIONS_READ]
        );

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Commissions employés', '/statistics/commissions-dashboard', [
                '^/statistics/commissions-dashboard',
            ]),
            [UserPermission::ALL_COMMISSIONS_READ]
        );

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Statistiques des usages', '/statistics/kibana/Usage', ['^/statistics/kibana/Usage']),
            [UserPermission::STATISTICS_REPORT_KIBANA_READ]
        );

        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Rapports');

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Rapports (new)', '/statistics/reports', ['^/statistics/reports']),
            [UserPermission::STATISTICS_REPORT_READ]
        );

        $menu->addSubMenu($sub_menu);

        return $this;
    }

    protected function addAdministrationMenuItems(SubMenu $menu): MenuBuilder
    {
        $sub_menu = new SubMenu('Système');

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Config. marketplaces', '/administration/system/marketplace-configuration', [
                '^/administration/system/marketplace-configuration',
            ])
        );

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Config. relance panier', '/administration/system/abandoned-cart', [
                '^/administration/system/abandoned-cart',
            ]),
            [UserPermission::SYSTEM_ADMINISTRATION_READ]
        );

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Accueil téléphonique', '/administration/system/telephone-assistance', [
                '^/administration/system/telephone-assistance',
            ]),
            []
        );

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Gestion des périodes de soldes', '/administration/system/sales-period-manager', [
                '^/administration/system/sales-period-manager',
            ]),
            []
        );

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Gestion des emails transactionnels', '/administration/system/transactional-email', [
                '^/administration/system/transactional-email',
            ]),
            []
        );

        $this->addRouteIfPermitted($sub_menu, new Route('Documentation API', '/api-docs', ['^/api-docs']), [
            UserPermission::CAN_SEE_API_DOCS,
            UserPermission::CAN_SEE_EZL_API_DOCS,
        ]);

        $menu->addSubMenu($sub_menu);

        return $this;
    }

    protected function addRetailStoreMenuItems(SubMenu $menu): MenuBuilder
    {
        $this->addRouteIfPermitted(
            $menu,
            new Route('Liste des magasins', '/company-directory/retail-stores', ['^/company-directory/retail-stores']),
            []
        );

        $sub_menu = new SubMenu('Fond de caisses');
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Ouverture / fermeture', '/retail-store/checkout/cashier-shift-operations', [
                '^/retail-store/checkout/cashier-shift-operations',
            ]),
            []
        );
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Détails du fond de caisse', null, ['^/retail-store/checkout/balance/(\d+)']),
            []
        );
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Dépot bancaire', null, [
                '^/retail-store/checkout/bank-deposit?',
                '^/retail-store/checkout/bank-deposit/(\d+)?',
            ]),
            []
        );
        $menu->addSubMenu($sub_menu);

        $sub_menu = new SubMenu('Emport');
        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Retrait client Champigny', '/full-dashboard/store-pickup/21/client-dashboard', [
                '^/full-dashboard/store-pickup/21/client-dashboard',
            ]),
            []
        );
        $menu->addSubMenu($sub_menu);

        $this->addRouteIfPermitted(
            $menu,
            new Route('Mon commissionnement', '/retail-store/my-commissions', ['^/retail-store/my-commissions']),
            []
        );

        return $this;
    }

    protected function addAccountingMenuItems(SubMenu $menu): MenuBuilder
    {
        $this->addRouteIfPermitted(
            $menu,
            new Route('Récapitulatif fond de caisse', '/accounting/checkout/balances', [
                '^/accounting/checkout/balances',
            ]),
            []
        );

        return $this;
    }

    protected function addPurchaseMenuItems(SubMenu $menu): MenuBuilder
    {
        $sub_menu = new SubMenu('Commandes');

        $this->addRouteIfPermitted(
            $sub_menu,
            new Route('Tableau de bord commandes fournisseur', '/supplier-order/list', ['^/supplier-order/list'])
        );

        $menu->addSubMenu($sub_menu);

        return $this;
    }

    protected function hasAtLeastOnePermission(array $permissions): bool
    {
        return [] !== array_intersect($this->permissions, $permissions);
    }

    protected function addRouteIfPermitted(SubMenu $menu, Route $route, array $permissions = []): self
    {
        if ([] === $permissions) {
            $menu->addRoute($route);

            return $this;
        }

        if ($this->hasAtLeastOnePermission($permissions)) {
            $menu->addRoute($route);
        }

        return $this;
    }
}
