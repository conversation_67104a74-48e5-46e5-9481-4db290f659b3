<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Utility;

use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationListInterface;

class ConstraintMessageFormatter
{
    /** ' */
    public static function prettify(ConstraintViolationListInterface $errors): string
    {
        $output = [];

        /** @var ConstraintViolation $error */
        foreach ($errors as $error) {
            $output[] = sprintf('%s: %s', $error->getPropertyPath(), $error->getMessage());
        }

        return implode("\n ", $output);
    }

    /** ' */
    public static function extract(ConstraintViolationListInterface $errors): array
    {
        $output = [];

        /** @var ConstraintViolation $error */
        foreach ($errors as $error) {
            $output[$error->getPropertyPath()] = $error->getMessage();
        }

        return $output;
    }
}
