<?php

namespace SonVideo\Erp\System\Manager;

use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyRepository;

class SystemEventDecorator
{
    private PricingStrategyRepository $pricing_strategy_repository;

    public function __construct(PricingStrategyRepository $pricing_strategy_repository)
    {
        $this->pricing_strategy_repository = $pricing_strategy_repository;
    }

    public function decorate(array $system_events): array
    {
        if ([] === $system_events) {
            return $system_events;
        }

        $pricing_strategy_names = $this->pricing_strategy_repository->getPricingStrategyNames();

        foreach ($system_events as $system_event) {
            if ('article.update.sales_channel_product.prices' !== $system_event->type) {
                continue;
            }
            if (array_key_exists('pricing_strategy', $system_event->payload['meta']) && isset($pricing_strategy_names[$system_event->payload['meta']['pricing_strategy']['id']])) {
                $system_event->payload['meta']['pricing_strategy']['name'] = $pricing_strategy_names[$system_event->payload['meta']['pricing_strategy']['id']]['name'];
            }
        }

        return $system_events;
    }
}
