<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Wms\Entity;

use App\Entity\AbstractEntity;

class ExpeditionParcelEntity extends AbstractEntity
{
    public int $parcel_id;

    public int $parcel_number;

    public ?string $parcel_tracking_number = null;

    public int $delivery_note_id;

    public int $expedition_delivery_note_id;

    /** @var ExpeditionParcelProductEntity[]|null */
    public ?array $articles = null;
}
