<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Wms\Mysql\Repository;

use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;

class DeliveryNotePreparationWriteRepository extends AbstractLegacyRepository
{
    /**
     * @return $this
     *
     * @throws SqlErrorMessageException
     */
    public function moveProductInParcel(int $parcel_product_id, int $parcel_number): self
    {
        $sql = <<<SQL
        CALL backOffice.WMS_ETQ_actualise_no_colis(:parcel_product_id, :parcel_number);
        SQL;

        $result = $this->legacy_pdo->fetchCollection($sql, [
            'parcel_product_id' => $parcel_product_id,
            'parcel_number' => $parcel_number,
        ]);

        $this->legacy_pdo->checkForFormattedResponse($result[0][0]['resultat']);

        return $this;
    }

    /**
     * @return $this
     *
     * @throws SqlErrorMessageException
     */
    public function deleteParcel(int $parcel_id): self
    {
        $sql = <<<SQL
        CALL backOffice.WMS_ETQ_delete_parcel(:parcel_id);
        SQL;

        $result = $this->legacy_pdo->fetchCollection($sql, ['parcel_id' => $parcel_id]);

        $this->legacy_pdo->checkForFormattedResponse($result[0][0]['resultat']);

        return $this;
    }
}
