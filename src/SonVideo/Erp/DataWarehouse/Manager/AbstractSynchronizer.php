<?php

namespace SonVideo\Erp\DataWarehouse\Manager;

use App\Contract\LoggerSubjectAwareInterface;
use App\Contract\LoggerSubjectAwareTrait;
use App\Database\PgDataWarehouse\DataSchema\DataParameterModel;
use Psr\Log\LogLevel;

abstract class AbstractSynchronizer implements LoggerSubjectAwareInterface
{
    use LoggerSubjectAwareTrait;

    protected const LAST_UPDATE = null;

    private DataParameterModel $data_parameter_model;

    /** @throws \Exception */
    public function __construct(DataParameterModel $data_parameter_model)
    {
        $this->data_parameter_model = $data_parameter_model;
        if (null === static::LAST_UPDATE) {
            throw new \Exception(static::class . '::LAST_UPDATE must be defined');
        }
    }

    /** Synchronize all data to data warehouse */
    public function synchronizeAll(int $buffer_size = 1000): void
    {
        $this->notify(LogLevel::INFO, 'Synchronize all data');
        $this->synchronizeSince(\DateTime::createFromFormat('U', 0), $buffer_size);
    }

    /**
     * Synchronize all data to warehouse since the last synchronize.
     *
     * @throws \Exception
     */
    public function synchronizeLatest(int $buffer_size = 1000): void
    {
        $this->notify(LogLevel::INFO, 'Retrieve the last synchronize date');

        $this->synchronizeSince(
            new \DateTime($this->data_parameter_model->findByPK(['key' => static::LAST_UPDATE])->get('value')),
            $buffer_size
        );
    }

    /** synchronize data on a period to data warehouse */
    public function synchronizeSince(\DateTime $start_date, int $buffer_size = 1000): void
    {
        $this->synchronize($start_date, new \DateTime(), $buffer_size);
    }

    /** synchronize data on a period to data warehouse */
    public function synchronizeBetween(\DateTime $start_date, \DateTime $end_date, int $buffer_size = 1000): void
    {
        $format = 'Y/m/d H:i:s';

        $this->notify(
            LogLevel::NOTICE,
            'START: Extracting data between ' . $start_date->format($format) . ' and ' . $end_date->format($format)
        );
        $this->synchronize($start_date, $end_date, $buffer_size);
    }

    abstract public function synchronize(\DateTime $start_date, \DateTime $end_date, int $buffer_size = 1000): void;

    /**
     * synchronize one data by key to data warehouse.
     *
     * @param string|int $key
     */
    abstract public function synchronizeOne($key, int $buffer_size = 1000): void;
}
