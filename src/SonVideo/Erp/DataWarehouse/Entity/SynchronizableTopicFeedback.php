<?php

namespace SonVideo\Erp\DataWarehouse\Entity;

class SynchronizableTopicFeedback
{
    private string $topic;

    private int $handled = 0;

    private int $errors = 0;

    private int $total;

    public function __construct(string $topic, int $total, int $handled = 0, int $errors = 0)
    {
        $this->topic = $topic;
        $this->total = $total;

        // Validate that handled + errors doesn't exceed total
        if ($handled + $errors > $total) {
            throw new \InvalidArgumentException('Sum of handled and errors cannot exceed total');
        }

        $this->handled = $handled;
        $this->errors = $errors;
    }

    public function getTopic(): string
    {
        return $this->topic;
    }

    public function getHandled(): int
    {
        return $this->handled;
    }

    public function getErrors(): int
    {
        return $this->errors;
    }

    public function getTotal(): int
    {
        return $this->total;
    }

    public function ok(): self
    {
        return new self($this->topic, $this->total, $this->handled + 1, $this->errors);
    }

    public function failure(): self
    {
        return new self($this->topic, $this->total, $this->handled, $this->errors + 1);
    }
}
