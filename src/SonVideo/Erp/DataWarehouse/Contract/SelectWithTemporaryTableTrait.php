<?php

namespace SonVideo\Erp\DataWarehouse\Contract;

use App\Contract\LegacyPdoAwareTrait;
use Doctrine\DBAL\ParameterType;

trait SelectWithTemporaryTableTrait
{
    use LegacyPdoAwareTrait;

    /**
     * Yield a query split into sub statement.
     *
     * @param string              $sql        Main sql to execute without LIMIT statemnt
     * @param array<string,mixed> $params     List of parameters
     * @param int|null            $limit_size Size of packets, null for no split
     *
     * @return \Generator<int,array<string,mixed>>
     */
    protected function yieldAllPackets(string $sql, array $params, int $limit_size = null): \Generator
    {
        $table = uniqid();
        $pk_name = $table . '_id';

        $this->createTemporaryTable($table, $pk_name, $sql, $params);

        $select = strtr(
            <<<SQL
                SELECT * FROM `{table}`
                WHERE `{pk_name}` > :pk_name
                ORDER BY `{pk_name}`
            SQL
            ,
            ['{table}' => $table, '{pk_name}' => $pk_name]
        );

        if ($limit_size) {
            $select .= ' LIMIT 0, ' . $limit_size;
        }

        $stmt = $this->legacy_pdo->prepare($select);
        $id_value = 0;
        $stmt->bindParam('pk_name', $id_value, ParameterType::INTEGER);
        do {
            $result = $stmt->executeQuery();

            $index = 0;
            while ($row = $result->fetchAssociative()) {
                $id_value = $row[$pk_name];
                unset($row[$pk_name]);
                ++$index;

                yield $row;
            }
            $result->free();
        } while ($index === $limit_size);
    }

    /**
     * Create a temporary table from a select query.
     *
     * @param array<string,mixed> $params
     */
    protected function createTemporaryTable(string $tablename, string $pk_name, string $select, array $params): void
    {
        $create_table = <<<'SQL'
            CREATE TEMPORARY TABLE `{table}` (
                `{pk_name}` INT NOT NULL AUTO_INCREMENT,
                PRIMARY KEY (`{pk_name}`)
            ) {select}
        SQL;
        $create_table = strtr($create_table, ['{table}' => $tablename, '{pk_name}' => $pk_name, '{select}' => $select]);

        $this->legacy_pdo->fetchAffected($create_table, $params);
    }
}
