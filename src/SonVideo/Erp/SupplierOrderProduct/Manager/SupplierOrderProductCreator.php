<?php

namespace SonVideo\Erp\SupplierOrderProduct\Manager;

use App\Exception\SqlErrorMessageException;
use SonVideo\Erp\SupplierOrder\Exception\SupplierOrderNotFoundException;
use SonVideo\Erp\SupplierOrder\Mysql\Repository\SupplierOrderRepository;
use SonVideo\Erp\SupplierOrderProduct\Dto\SupplierOrderProductCreationContextDto;
use SonVideo\Erp\SupplierOrderProduct\Exception\SupplierOrderProductRequestPayloadException;
use SonVideo\Erp\SupplierOrderProduct\Mysql\Repository\SupplierOrderProductRepository;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class SupplierOrderProductCreator
{
    private ValidatorInterface $validator;

    private SupplierOrderProductRepository $supplier_order_product_repository;
    private SupplierOrderRepository $supplier_order_repository;

    public function __construct(
        ValidatorInterface $validator_interface,
        SupplierOrderRepository $supplier_order_repository,
        SupplierOrderProductRepository $supplier_order_product_repository
    ) {
        $this->validator = $validator_interface;
        $this->supplier_order_repository = $supplier_order_repository;
        $this->supplier_order_product_repository = $supplier_order_product_repository;
    }

    /**
     * @throws SupplierOrderNotFoundException
     * @throws SupplierOrderProductRequestPayloadException
     * @throws SqlErrorMessageException
     */
    public function create(
        int $supplier_order_id,
        SupplierOrderProductCreationContextDto $supplier_order_product_creation_context_dto
    ): int {
        $errors = $this->validator->validate($supplier_order_product_creation_context_dto);

        if (count($errors) > 0) {
            throw new SupplierOrderProductRequestPayloadException(sprintf('Invalid request parameters : %s', ConstraintMessageFormatter::prettify($errors)));
        }

        if (!$this->supplier_order_repository->exists($supplier_order_id)) {
            throw new SupplierOrderNotFoundException(sprintf('Supplier order [%s] not found', $supplier_order_id));
        }

        return $this->supplier_order_product_repository->upsertSupplierOrderProduct(
            $supplier_order_id,
            $supplier_order_product_creation_context_dto->product_id,
            $supplier_order_product_creation_context_dto->quantity,
            $supplier_order_product_creation_context_dto->price
        );
    }
}
