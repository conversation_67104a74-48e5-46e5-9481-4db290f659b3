<?php

namespace SonVideo\Erp\SupplierOrderProduct\Manager;

use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\CustomerOrderProduct\Mysql\Repository\CustomerOrderProductRepository;
use SonVideo\Erp\SupplierOrderProduct\Mysql\Repository\SupplierOrderProductRepository;

class SupplierOrderProductManager
{
    private SupplierOrderProductRepository $supplier_order_product_repository;
    private ArticleRepository $article_repository;
    private CustomerOrderProductRepository $customer_order_product_repository;

    public function __construct(
        SupplierOrderProductRepository $supplier_order_product_repository,
        ArticleRepository $article_repository,
        CustomerOrderProductRepository $customer_order_product_repository
    ) {
        $this->supplier_order_product_repository = $supplier_order_product_repository;
        $this->article_repository = $article_repository;
        $this->customer_order_product_repository = $customer_order_product_repository;
    }

    /** @throws \Exception */
    public function makeStockEntry(
        int $user_id,
        ?int $supplier_order_id,
        ?int $transfer_id,
        int $product_id,
        int $quantity,
        int $warehouse_id
    ): void {
        try {
            $this->supplier_order_product_repository->makeStockEntry(
                $user_id,
                $supplier_order_id,
                $transfer_id,
                $product_id,
                $quantity,
                $warehouse_id
            );
        } catch (\Exception $e) {
            throw new \LogicException('An error occurred while making stock entry', $e->getCode(), $e);
        }

        $this->updateCustomerOrderProductLine($product_id);
    }

    /** @throws \Exception */
    private function updateCustomerOrderProductLine(int $product_id): void
    {
        $product = $this->article_repository->getOneById($product_id, ['stock']);

        $customer_order_product_lines = $this->customer_order_product_repository->findAllAwaitingSupplierOrderDeliveryByProductId(
            $product_id
        );

        $quantity_needed = 0;
        $customer_order_product_lines_to_update = [];
        $line = array_shift($customer_order_product_lines);
        while (!is_null($line) && $quantity_needed + $line['quantity'] <= $product['stock']) {
            $quantity_needed += $line['quantity'];
            $customer_order_product_lines_to_update[] = $line['customer_order_product_id'];
            $line = array_shift($customer_order_product_lines);
        }

        if ([] !== $customer_order_product_lines_to_update) {
            $this->customer_order_product_repository->updateEffectiveSupplierOrderDeliveryDate(
                $customer_order_product_lines_to_update
            );
        }
    }
}
