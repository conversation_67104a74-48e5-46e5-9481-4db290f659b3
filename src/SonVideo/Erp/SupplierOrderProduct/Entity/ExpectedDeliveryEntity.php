<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\SupplierOrderProduct\Entity;

use App\Adapter\Serializer\Type\JsonDenormalizableInterface;
use App\Entity\AbstractEntity;

class ExpectedDeliveryEntity extends AbstractEntity implements JsonDenormalizableInterface
{
    public int $expected_delivery_id;

    public int $supplier_order_product_id;

    public \DateTimeInterface $expected_delivery_date;

    public int $expected_quantity;
}
