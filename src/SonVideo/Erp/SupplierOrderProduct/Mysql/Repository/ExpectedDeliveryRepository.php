<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\SupplierOrderProduct\Mysql\Repository;

use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\InsertValues;
use Doctrine\DBAL\Exception;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\SupplierOrderProduct\Entity\ExpectedDeliveryEntity;

class ExpectedDeliveryRepository extends AbstractLegacyRepository
{
    /**
     * Create multiple expected deliveries in a row.
     *
     * @param array<ExpectedDeliveryEntity> $expected_deliveries
     *
     * @return int Affected rows number
     *
     * @throws InternalErrorException
     */
    public function createAll(array $expected_deliveries): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.livraison_produit_commande_fournisseur ({column_list}) VALUES {values}
        SQL;
        $insert_values = new InsertValues([
            'id_produit_commande_fournisseur',
            'date_livraison_prevue',
            'quantite_livraison_prevue',
        ]);
        foreach ($expected_deliveries as $expected_delivery) {
            $insert_values->addValues([
                'id_produit_commande_fournisseur' => $expected_delivery->supplier_order_product_id,
                'date_livraison_prevue' => $expected_delivery->expected_delivery_date->format('Y-m-d'),
                'quantite_livraison_prevue' => $expected_delivery->expected_quantity,
            ]);
        }

        $sql = strtr($sql, [
            '{column_list}' => $insert_values->getColumnList(),
            '{values}' => $insert_values,
        ]);

        try {
            $affected = $this->legacy_pdo->fetchAffected($sql, $insert_values->getParams());
        } catch (Exception $exception) {
            $message = $exception->getMessage();
            if (preg_match('/REFERENCES `produit_commande_fournisseur`/i', $message)) {
                $message = 'Unknown supplier product line';
            }
            if (preg_match('/Duplicate entry/i', $message)) {
                $message = 'Date already exists for at least one supplier product line';
            }

            throw new InternalErrorException(InternalError::GENERIC, new Exception($message));
        }

        return $affected ?? 0;
    }

    /**
     * Delete an expected delivery by its id.
     *
     * @return int Number of deleted rows
     *
     * @throws NotFoundException
     * @throws InternalErrorException
     */
    public function delete(int $expected_delivery_id): int
    {
        $sql = <<<SQL
        DELETE FROM backOffice.livraison_produit_commande_fournisseur WHERE id = :expected_delivery_id
        SQL;
        try {
            $result = $this->legacy_pdo->fetchAffected($sql, ['expected_delivery_id' => $expected_delivery_id]);

            if (0 === $result) {
                throw new NotFoundException('Expected delivery not found.');
            }
        } catch (Exception $exception) {
            $message = $exception->getMessage();

            throw new InternalErrorException(InternalError::GENERIC, new Exception($message));
        }

        return $result;
    }

    /**
     * Update an expected delivery.
     *
     * @return int Number of updated rows
     *
     * @throws InternalErrorException
     * @throws NotFoundException
     */
    public function update(ExpectedDeliveryEntity $expected_delivery): int
    {
        $sql = <<<SQL
        UPDATE backOffice.livraison_produit_commande_fournisseur
        SET quantite_livraison_prevue = :expected_quantity
        WHERE id = :expected_delivery_id
        SQL;
        try {
            $result = $this->legacy_pdo->fetchAffected($sql, [
                'expected_delivery_id' => $expected_delivery->expected_delivery_id,
                'expected_quantity' => $expected_delivery->expected_quantity,
            ]);

            if (0 === $result) {
                throw new NotFoundException('Expected delivery not found.');
            }
        } catch (Exception $exception) {
            $message = $exception->getMessage();

            throw new InternalErrorException(InternalError::GENERIC, new Exception($message));
        }

        return $result;
    }
}
