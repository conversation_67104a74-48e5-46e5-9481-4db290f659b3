<?php

namespace SonVideo\Erp\SvdGiftCard\Manager;

use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderPaymentCreationContextEntity;
use SonVideo\Erp\Repository\PaymentWriteRepository;
use SonVideo\Erp\SvdGiftCard\Entity\SvdGiftCard;
use SonVideo\Erp\SvdGiftCard\Mysql\Repository\SvdGiftCardRepository;

class SvdGiftCardManager
{
    private SvdGiftCardRepository $repository;

    private PaymentWriteRepository $payment_repository;

    public function __construct(SvdGiftCardRepository $repository, PaymentWriteRepository $payment_repository)
    {
        $this->repository = $repository;
        $this->payment_repository = $payment_repository;
    }

    public function handlePayment(
        CustomerOrderPaymentCreationContextEntity $gift_card_context
    ): CustomerOrderPaymentCreationContextEntity {
        $gift_card = $this->repository->findOneByCardNumber($gift_card_context->extra_data['number']);

        if (!$gift_card instanceof SvdGiftCard) {
            throw new \Exception('SVD Gift Card nb %s not found', $gift_card_context->extra_data['number']);
        }

        $transaction_id = sprintf(
            '%s-%s',
            $gift_card_context->customer_order_id,
            $gift_card_context->extra_data['payment_line_nb']
        );

        $gift_card_context->extra_data = array_merge($gift_card_context->extra_data, [
            'payment_id' => $this->payment_repository->upsert($transaction_id),
            'gift_card' => $gift_card,
        ]);

        if (null !== $gift_card_context->extra_data['payment_id']) {
            $this->repository->insertAutoResponse($gift_card_context->extra_data['payment_id'], $gift_card, 'refuse');
        }

        return $gift_card_context;
    }

    public function burn(CustomerOrderPaymentCreationContextEntity $gift_card_context): void
    {
        $this->repository->burn($gift_card_context->extra_data['gift_card'], [
            'order_id' => $gift_card_context->customer_order_id,
            'payment_line_nb' => $gift_card_context->extra_data['payment_line_nb'],
            'payment_id' => $gift_card_context->extra_data['payment_id'],
        ]);
    }
}
