<?php

namespace SonVideo\Erp\Messaging\Repository\ErpProduct;

/**
 * Repository for reading ERP product delivery information.
 */
class ErpProductDeliveryReadRepository
{
    /** Generate SQL query for retrieving ERP product delivery information. */
    public static function getErpProductDeliverySql(): string
    {
        $sql = <<<SQL
        -- ErpProductDeliveryReadRepository
        SELECT
            a.id_produit as erp_product_id,
            CASE IF(a.compose = 1, c.V_delai_lvr, a.V_delai_lvr)
                WHEN 0   THEN 'AVAILABILITY_1_2_DAYS'
                WHEN 4   THEN 'AVAILABILITY_3_5_DAYS'
                WHEN 9   THEN 'AVAILABILITY_6_10_DAYS'
                WHEN 15  THEN 'AVAILABILITY_11_15_DAYS'
                WHEN 30  THEN 'AVAILABILITY_16_30_DAYS'
                WHEN 60  THEN 'AVAILABILITY_1_2_MONTHS'
                WHEN 90  THEN 'AVAILABILITY_2_3_MONTHS'
                WHEN 180 THEN 'AVAILABILITY_3_6_MONTHS'
                WHEN 270 THEN 'AVAILABILITY_6_9_MONTHS'
            END AS availability,
            a.modif_date AS updated_at,
            backOffice.has_an_ongoing_supplier_order_for_article_id(a.id_produit) AS has_supplier_order
        FROM {selection_table} selection
        INNER JOIN backOffice.article a ON a.id_produit = selection.erp_product_id
        LEFT JOIN backOffice.BO_PDT_ART_V_compose c ON c.id = a.id_produit
        WHERE selection.selection_id = :selection_id
        GROUP BY a.id_produit
        SQL;

        return strtr($sql, [
            '{selection_table}' => ErpProductMessageRepositoryInterface::SELECTION_TABLE,
        ]);
    }
}
