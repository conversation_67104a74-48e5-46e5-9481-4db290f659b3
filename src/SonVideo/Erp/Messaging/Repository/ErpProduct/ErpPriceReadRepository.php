<?php

namespace SonVideo\Erp\Messaging\Repository\ErpProduct;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Message\ErpPriceMessage;
use App\Sql\AbstractLegacyRepository;

/**
 * Repository responsible for retrieving ERP product pricing information.
 */
class ErpPriceReadRepository extends AbstractLegacyRepository implements SerializerAwareInterface, ErpProductMessageRepositoryInterface
{
    use SerializerAwareTrait;
    use ErpProductMessageSelectorTrait;

    private const MINIMUM_REDUCTION_TO_APPLY_OFFER = 0.05;

    /**
     * Generate SQL query to retrieve aggregated pricing data by product ID.
     *
     * This method creates a query that groups all pricing information for each product
     * and returns it as a JSON array structure for efficient data handling.
     *
     * @return string The complete SQL query for product pricing aggregation
     */
    public static function getErpPriceByProductSql(): string
    {
        $sql = <<<SQL
        -- ErpPriceReadRepository
        SELECT
            erp_product_id,
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    'ecotax', ecotax,
                    'erp_product_id', erp_product_id,
                    'is_active', is_active,
                    'margin', margin,
                    'margin_rate', margin_rate,
                    'reference_price', reference_price,
                    'sales_channel_id', sales_channel_id,
                    'selling_price', selling_price,
                    'selling_price_excl_vat', selling_price_excl_vat,
                    'sorecop', sorecop,
                    'tags', tags,
                    'updated_at', updated_at,
                    'vat_rate', vat_rate
                )
            ) AS data
        FROM (
            {erp_price_sql}
        ) price
        GROUP BY erp_product_id
        SQL;

        return strtr($sql, [
            '{erp_price_sql}' => self::getErpPriceSql(),
            '{selection_table}' => self::SELECTION_TABLE,
        ]);
    }

    /** Generate core SQL query for ERP price calculation with all related fees and margins. */
    public static function getErpPriceSql(): string
    {
        $reduction_to_apply_offer = self::MINIMUM_REDUCTION_TO_APPLY_OFFER;

        $sql = <<<SQL
        SELECT
        *,
        selling_price_excl_vat - (purchase_cost + ecotax + sorecop) AS margin,
        (selling_price_excl_vat - (purchase_cost + ecotax + sorecop)) / selling_price_excl_vat AS margin_rate
        FROM (
            SELECT
                scp.is_active AS is_active,
                a.prix_ecotaxe AS ecotax,
                scp.product_id AS erp_product_id,
                ROUND(backOffice.PDT_ART_px_achat(a.id_produit), 2) AS purchase_cost,
                scp.reference_price AS reference_price,
                scp.sales_channel_id AS sales_channel_id,
                scp.selling_price AS selling_price,
                ROUND(scp.selling_price / (1 + p.tva) , 2) AS selling_price_excl_vat,
                a.prix_sorecop AS sorecop,
                JSON_MERGE_PRESERVE(
                    IF(scp.sales_channel_id = 1 AND backOffice.PDT_ART_est_solde(a.id_produit) = 1,
                        JSON_ARRAY('sales'),
                        JSON_ARRAY()
                    ),
                    IF(1 - scp.selling_price / COALESCE(scp.reference_price, scp.selling_price) >= $reduction_to_apply_offer,
                        JSON_ARRAY('promotion'),
                        JSON_ARRAY()
                    )
                ) AS tags,
                scp.updated_at AS updated_at,
                p.tva AS vat_rate
            FROM {selection_table} selection
            INNER JOIN backOffice.sales_channel_product scp ON scp.product_id = selection.erp_product_id
            INNER JOIN backOffice.sales_channel sc ON sc.id = scp.sales_channel_id
            INNER JOIN backOffice.article a ON a.id_produit = scp.product_id
            INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
            WHERE scp.sales_channel_id IN (1)
            AND scp.selling_price IS NOT NULL
            AND (p.type = 'compose' OR p.type = 'article')
            AND selection.selection_id = :selection_id
        ) AS prices
        SQL;

        return strtr($sql, [
            '{selection_table}' => self::SELECTION_TABLE,
        ]);
    }

    /**
     * Find and retrieve ERP price messages based on the provided selector.
     *
     * @param string|int|\DateTime $selector The selection criteria
     *
     * @return ErpPriceMessage[] Array of denormalized ERP price message objects
     *
     * @throws \InvalidArgumentException When selector format is invalid
     * @throws \Exception                When database query fails or serialization errors occur
     */
    public function findBySelector($selector): array
    {
        try {
            $this->buildSelectorTable($selector);

            $sql = static::getErpPriceSql() . ' ORDER BY prices.updated_at ASC';
            $data = $this->legacy_pdo->fetchAll($sql, ['selection_id' => $this->selection_id]);

            return $this->serializer->denormalize($data, ErpPriceMessage::class . '[]');
        } finally {
            $this->deleteSelectorTable();
        }
    }

    protected function getSelectorConfig(): array
    {
        return [
            self::STRING_SELECTOR => ['p.reference  = :selector'],
            self::INT_SELECTOR => ['a.id_produit = :selector'],
            self::DATETIME_SELECTOR => ['scp.updated_at > :selector', 'scp.updated_at > a.erp_product_updated_at'],
            self::SELECTOR_QUERY => <<<SQL
                SELECT
                    scp.product_id AS erp_product_id
                FROM backOffice.sales_channel_product scp
                    INNER JOIN backOffice.article a ON a.id_produit = scp.product_id
                    INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
                WHERE {conditions}
                    AND scp.sales_channel_id IN (1)
            SQL
        ];
    }
}
