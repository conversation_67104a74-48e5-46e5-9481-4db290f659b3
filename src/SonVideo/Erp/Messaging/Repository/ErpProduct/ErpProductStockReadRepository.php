<?php

namespace SonVideo\Erp\Messaging\Repository\ErpProduct;

use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;
use App\Message\ErpProductStockMessage;
use App\Sql\AbstractLegacyRepository;

/**
 * Repository for reading ERP product stock data from legacy database.
 */
class ErpProductStockReadRepository extends AbstractLegacyRepository implements SerializerAwareInterface, ErpProductMessageRepositoryInterface
{
    use SerializerAwareTrait;
    use ErpProductMessageSelectorTrait;

    /** Returns SQL query for retrieving product stock information. */
    public static function getErpProductStockSql(): string
    {
        return strtr(
            <<<SQL
            -- ErpProductStockReadRepository
            SELECT
                COALESCE(c.quantite_stock, s.deliverable_stock, 0) AS deliverable_stock,
                selection.erp_product_id AS erp_product_id,
                a.V_quantite_stock AS informational_stock,
                a.V_qte_facturee AS invoiced_quantity,
                COALESCE(c.modif_date, s.updated_at) AS updated_at
            FROM {selection_table} selection
            INNER JOIN backOffice.article a ON a.id_produit = selection.erp_product_id
            INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
            LEFT JOIN backOffice.article_stock s ON s.product_id = a.id_produit AND s.deliverable_stock >= 0
            LEFT JOIN backOffice.BO_PDT_ART_V_compose c ON c.id = a.id_produit
            WHERE selection.selection_id = :selection_id
            SQL
            ,
            [
                '{selection_table}' => self::SELECTION_TABLE,
            ]
        );
    }

    /**
     * Finds product stock data based on provided selector.
     *
     * @param string|int|\DateTime $selector
     *
     * @return ErpProductStockMessage[]
     */
    public function findBySelector($selector): array
    {
        try {
            $this->buildSelectorTable($selector);

            $sql = static::getErpProductStockSql() . ' ORDER BY s.updated_at ASC';
            $data = $this->legacy_pdo->fetchAll($sql, ['selection_id' => $this->selection_id]);

            return $this->serializer->denormalize($data, ErpProductStockMessage::class . '[]');
        } finally {
            $this->deleteSelectorTable();
        }
    }

    protected function getSelectorConfig(): array
    {
        return [
            self::STRING_SELECTOR => ['p.reference  = :selector'],
            self::INT_SELECTOR => ['a.id_produit = :selector'],
            self::DATETIME_SELECTOR => [
                'COALESCE(c.modif_date, s.updated_at) > :selector',
                'COALESCE(c.modif_date, s.updated_at) > a.erp_product_updated_at',
            ],
            self::SELECTOR_QUERY => <<<SQL
                SELECT
                    a.id_produit AS erp_product_id
                FROM backOffice.article a
                    INNER JOIN backOffice.produit p ON p.id_produit = a.id_produit
                    LEFT JOIN backOffice.article_stock s ON a.id_produit = s.product_id AND deliverable_stock >= 0
                    LEFT JOIN backOffice.BO_PDT_ART_V_compose c ON c.id = a.id_produit
                WHERE (s.product_id IS NULL OR c.id IS NULL) AND {conditions}
            SQL
        ];
    }
}
