<?php

namespace SonVideo\Erp\Messaging\Manager;

use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\Message\ErpProductMessage;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Messaging\Repository\ErpProductReadRepository;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

/**
 * Manager responsible for synchronizing ERP products with the messaging system.
 */
class ErpProductManager implements ErpMessageManagerInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;

    public const PARAMETER = 'sync.erp_parameter.erp_product.sync_date';

    private MessageBusInterface $bus;
    private ErpProductReadRepository $repository;
    private ParameterModel $parameter_repo;

    /**
     * Constructor for ErpProductManager.
     *
     * @param MessageBusInterface      $bus            The message bus for dispatching messages
     * @param ErpProductReadRepository $repository     Repository for fetching ERP products
     * @param ParameterModel           $parameter_repo Repository for system parameters
     */
    public function __construct(
        MessageBusInterface $bus,
        ErpProductReadRepository $repository,
        ParameterModel $parameter_repo
    ) {
        $this->bus = $bus;
        $this->repository = $repository;
        $this->parameter_repo = $parameter_repo;
    }

    /**
     * Fetch ERP products and publish them to the message bus.
     *
     * @param int|string|\DateTime $selector Criteria to select ERP products
     */
    public function fetchAndPublish($selector): void
    {
        $erp_products = $this->repository->findErpProducts($selector);
        $this->logger->notice(sprintf('%s %s found !', count($erp_products), $this->getRessourceName()));

        $last_updated_at = null;
        $count = 0;

        foreach ($erp_products as $erp_product) {
            $this->dispatch($erp_product);
            $last_updated_at = $erp_product->updated_at;
            ++$count;
        }

        if ($selector instanceof \DateTime && null !== $last_updated_at) {
            $this->parameter_repo->setParameter(
                self::PARAMETER,
                $last_updated_at = max($last_updated_at, $erp_product->updated_at),
                sprintf('Last %s update date sync', $this->getRessourceName())
            );
            $this->logger->notice(
                sprintf('Save the last %s publication date at %s', $this->getRessourceName(), $last_updated_at)
            );
        }

        $this->logger->notice(sprintf('%s %s published !', $count, $this->getRessourceName()));
    }

    /**
     * Dispatch an ERP product message to the message bus.
     *
     * @param ErpProductMessage $erp_product The product message to dispatch
     */
    public function dispatch(ErpProductMessage $erp_product): void
    {
        $this->bus->dispatch(new Envelope($erp_product));
    }

    public function getRessourceName(): string
    {
        return 'erp-product';
    }

    public function getParameterName(): string
    {
        return self::PARAMETER;
    }
}
