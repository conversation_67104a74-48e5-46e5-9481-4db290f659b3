<?php

namespace SonVideo\Erp\Messaging\Manager\ErpProduct;

use App\Database\PgErpServer\SystemSchema\ParameterModel;
use App\Message\ErpProductStockMessage;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Messaging\Repository\ErpProduct\ErpProductStockReadRepository;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;

class ErpProductStockManager implements ErpProductScopeMessageManagerInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;
    public const PARAMETER = 'sync.erp_parameter.erp_product_stock.sync_date';

    private MessageBusInterface $bus;
    private ErpProductStockReadRepository $repository;
    private ParameterModel $parameter_repo;

    public function __construct(
        MessageBusInterface $bus,
        ErpProductStockReadRepository $repository,
        ParameterModel $parameter_repo
    ) {
        $this->bus = $bus;
        $this->repository = $repository;
        $this->parameter_repo = $parameter_repo;
    }

    /** @param int|string|\DateTime $selector */
    public function fetchAndPublish($selector): void
    {
        $erp_product_stocks = $this->repository->findBySelector($selector);
        $this->logger->notice(sprintf('%s %s found !', count($erp_product_stocks), $this->getRessourceName()));

        $last_updated_at = null;

        $count = 0;
        foreach ($erp_product_stocks as $erp_product_stock) {
            $this->dispatch($erp_product_stock);
            $last_updated_at = $erp_product_stock->updated_at;
            ++$count;
        }

        if ($selector instanceof \DateTime && null !== $last_updated_at) {
            $this->parameter_repo->setParameter(
                self::PARAMETER,
                $last_updated_at = max($last_updated_at, $erp_product_stock->updated_at),
                sprintf('Last %s update date sync', $this->getRessourceName())
            );
            $this->logger->notice(
                sprintf('Save the last %s publication date at %s', $this->getRessourceName(), $last_updated_at)
            );
        }

        $this->logger->notice(sprintf('%s %s published !', $count, $this->getRessourceName()));
    }

    public function dispatch(ErpProductStockMessage $erp_product_stock): void
    {
        $this->bus->dispatch(new Envelope($erp_product_stock));
    }

    public function getRessourceName(): string
    {
        return 'erp-product-stock';
    }

    public function getParameterName(): string
    {
        return self::PARAMETER;
    }
}
