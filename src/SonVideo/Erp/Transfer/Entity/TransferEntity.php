<?php

namespace SonVideo\Erp\Transfer\Entity;

use App\DataLoader\Type\JsonType;
use App\Entity\AbstractEntity;

class TransferEntity extends AbstractEntity
{
    public int $transfer_id;

    public string $created_at;

    public ?string $closed_at = null;

    public string $created_by;

    public ?string $type = null;

    public string $status;

    public ?string $comment = null;

    public ?int $shipment_method_id = null;

    public ?int $customer_order_id = null;

    /** @var array|JsonType */
    public array $warehouse_from = [];

    /** @var array|JsonType */
    public array $warehouse_to = [];

    /** @var array|JsonType */
    public array $transfered_products = [];
}
