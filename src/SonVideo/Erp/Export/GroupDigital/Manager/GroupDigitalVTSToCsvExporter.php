<?php

namespace SonVideo\Erp\Export\GroupDigital\Manager;

use App\Database\PgDataWarehouse\DataSchema\CustomerOrderModel;
use League\Csv\CannotInsertRecord;
use League\Csv\Exception;
use League\Csv\Reader;
use League\Csv\Writer;
use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Export\GroupDigital\Repository\Mysql\ReferenceGroupDigitalRepository;
use SonVideo\Erp\Filesystem\Manager\ExportedFile;
use SonVideo\Erp\Filesystem\Manager\GroupDigitalFtp;

final class GroupDigitalVTSToCsvExporter implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    private const EXPORT_HEADERS = [
        'shop' => 'Magasin',
        'week' => 'No_Semaine',
        'sunday' => 'Date_Ref',
        'reference' => 'Code_Article',
        'ean' => 'Code_Barres',
        'supplier' => 'Fourn_Std',
        'brand' => 'Marque',
        'quantity' => 'Qte_Vendue',
    ];

    public const CODE_MAGASIN = '426001';

    private CustomerOrderModel $customer_orders_repository;

    private ReferenceGroupDigitalRepository $reference_group_digital_repository;

    private ExportedFile $exported_file;

    private GroupDigitalFtp $ftp;

    public function __construct(
        CustomerOrderModel $customer_orders_repository,
        ReferenceGroupDigitalRepository $reference_group_digital_repository,
        ExportedFile $exported_file,
        GroupDigitalFtp $ftp
    ) {
        $this->customer_orders_repository = $customer_orders_repository;
        $this->reference_group_digital_repository = $reference_group_digital_repository;
        $this->exported_file = $exported_file;
        $this->ftp = $ftp;
    }

    /**
     * @throws CannotInsertRecord
     * @throws Exception
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    public function export(): void
    {
        $this->logger->debug('Create CSV structure from records...');

        $group_digital_refs = $this->reference_group_digital_repository->getAllRefsAsArray();

        $stream = tmpfile();
        $writer = Writer::createFromStream($stream)
            ->setOutputBOM(Reader::BOM_UTF8)
            ->setDelimiter(';')
            ->setNewline("\r\n");

        $records = $this->customer_orders_repository->getExportGroupDigitalVTSGenerator(self::CODE_MAGASIN);

        $this->logger->debug('Create CSV header line...');
        $writer->insertOne(array_values(self::EXPORT_HEADERS));

        $this->logger->debug('Write result in CSV file...');
        foreach ($records as $record) {
            if (!isset($group_digital_refs[$record['ean']])) {
                // Skip lines where EAN is not known by Group Digital
                continue;
            }
            $writer->insertOne($this->formatFields($record, $group_digital_refs));
        }

        $this->logger->notice(sprintf('<comment>Memory usage: %s</comment>', memory_get_usage()));

        $week_of_last_sunday = date('yW', strtotime('-' . date('w') . ' days'));
        $file_name = sprintf('VTS%s_%s.CSV', $week_of_last_sunday, self::CODE_MAGASIN);
        $this->exported_file->createOrOverwriteStream($file_name, $stream);

        // Send to Group Digital FTP
        $this->ftp->createOrOverwrite(
            $this->ftp::GROUP_DIGITAL_FTP_PATH,
            $file_name,
            $this->exported_file->getFilesystem()->read($file_name)
        );

        $this->logger->notice(sprintf('<comment>Peak memory usage: %s</comment>', memory_get_peak_usage()));
    }

    private function formatFields(array $record, array $group_digital_refs): array
    {
        // Replace fields 'supplier' and 'brand' with values from $group_digital_refs (Group Digital references for this EAN).
        $record = array_merge($record, $group_digital_refs[$record['ean']]);
        unset($record['product_range']);

        return array_replace(self::EXPORT_HEADERS, $record);
    }
}
