<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\StockMove\Mysql\Repository;

use App\Exception\InternalErrorException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use Doctrine\DBAL\Exception;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\StockMove\Entity\StockMoveEntity;

class StockMoveRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'product_id' => 'ms.id_produit',
        'stock_move_id' => 'ms.id_mouvement_stock',
        'warehouse_name' => 'd.nom_depot',
        'warehouse_id' => 'd.id',
        'quantity' => 'ms.quantite',
        'location_label' => 'l.label',
        'location_code' => 'l.code',
        'created_at' => 'ms.date_creation',
        'user' => 'ms.utilisateur',
        'type' => 'ms.type',
        'comment' => 'ms.commentaire',
        'delivery_note_customer_id' => 'ms.id_bon_livraison',
        'supplier_order_id' => 'ms.id_commande_fournisseur',
        'transfer_id' => 'ms.id_transfert',
        'return_note_id' => 'ms.id_bon_retour',
        'move_mission_id' => 'ms.move_mission_id',
        'delivery_note_transfer_id' => 'bl.id_transfert',
    ];

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM (
            SELECT
              ms.id_mouvement_stock AS stock_move_id,
              d.nom_depot AS warehouse_name,
              ms.quantite AS quantity,
              l.label AS location_label,
              l.code AS location_code,
              ms.date_creation AS created_at,
              ms.utilisateur AS user,
              backOffice.GET_COMPUTED_USER_NAME_BY_USERNAME(ms.utilisateur) AS user_full_name,
              ms.type,
              ms.commentaire AS comment,
              ms.prix_achat AS buy_price,
              pbl.prix_vente AS selling_price,
              ms.id_bon_livraison AS delivery_note_customer_id,
              ms.id_commande_fournisseur AS supplier_order_id,
              ms.id_transfert AS transfer_id,
              ms.id_bon_retour AS return_note_id,
              ms.move_mission_id AS move_mission_id,
              ms.id_avoir AS credit_note_id,
              ms.id_carte_cadeau AS gift_card_id,
              bl.id_commande AS customer_order_id,
              bl.id_transfert AS delivery_note_transfer_id
            FROM backOffice.mouvement_stock ms
              LEFT JOIN backOffice.BO_STK_depot d ON ms.id_depot = d.id
              LEFT JOIN backOffice.WMS_location l ON ms.id_emplacement = l.location_id
              LEFT JOIN backOffice.bon_livraison bl ON bl.id_bon_livraison = ms.id_bon_livraison
              LEFT JOIN backOffice.produit_bon_livraison pbl ON bl.id_bon_livraison = pbl.id_bon_livraison AND pbl.id_produit = ms.id_produit
            WHERE ({conditions})
          ) tmp
          {order_by}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateArray(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function updateBuyPrice(int $stock_move_id, float $buy_price): void
    {
        $sql = <<<SQL
        UPDATE backOffice.mouvement_stock
        SET prix_achat = :buy_price
        WHERE id_mouvement_stock = :stock_move_id
        SQL;

        try {
            $this->legacy_pdo->fetchAffected($sql, [
                'stock_move_id' => $stock_move_id,
                'buy_price' => $buy_price,
            ]);
        } catch (Exception $exception) {
            $message = $exception->getMessage();
            throw new InternalErrorException(InternalError::GENERIC, new Exception($message));
        }
    }

    /** findOneById */
    public function findOneById(int $stock_move_id): StockMoveEntity
    {
        $sql = <<<SQL
        SELECT
            ms.id_mouvement_stock AS 'stock_move_id',
            ms.id_produit AS 'product_id',
            ms.date_creation AS 'created_at',
            ms.utilisateur AS 'user',
            ms.quantite AS 'quantity',
            ms.prix_achat AS 'buy_price',
            ms.type AS 'type',
            ms.commentaire AS 'comment',
            ms.id_bon_livraison AS 'delivery_note_id',
            ms.id_commande_fournisseur AS 'supplier_order_id',
            ms.id_transfert AS 'transfer_id',
            ms.id_bon_retour AS 'return_note_id',
            ms.move_mission_id AS 'move_mission_id'
        FROM backOffice.mouvement_stock ms
        WHERE id_mouvement_stock = :id
        ;
        SQL;

        $result = $this->legacy_pdo->fetchOne($sql, ['id' => $stock_move_id]);

        return $this->data_loader->hydrate($result, StockMoveEntity::class);
    }
}
