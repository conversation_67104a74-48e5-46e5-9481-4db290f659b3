<?php

namespace SonVideo\Erp\KnowledgeBase\Manager;

use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use SonVideo\Erp\Filesystem\Manager\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class Media extends File
{
    public const FILESYSTEM = 'uploads_s3_filesystem';
    private const IMAGE_FILE_MASK = 'images/knowledge-base/{uid}';

    /**
     * @throws FileNotFoundException
     * @throws FileExistsException
     */
    public function moveToFinalPath(UploadedFile $uploaded_file): string
    {
        return '/' . $this->write($uploaded_file, $this->getFormattedFilenameFrom());
    }

    protected function getFormattedFilenameFrom(): callable
    {
        return function (UploadedFile $uploaded_file): string {
            $name = strtr(self::IMAGE_FILE_MASK, [
                '{uid}' => uniqid('', true),
            ]);

            // Compute file + extension
            return sprintf('%s.%s', $name, strtolower($this->getFileExtension($uploaded_file)));
        };
    }
}
