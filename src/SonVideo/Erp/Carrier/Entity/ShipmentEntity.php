<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Entity;

use App\Entity\AbstractEntity;
use SonVideo\Erp\Carrier\Entity\ShipmentProperty\InformationEntity;
use SonVideo\Erp\Carrier\Entity\ShipmentProperty\ParcelEntity;
use SonVideo\Erp\Carrier\Entity\ShipmentProperty\RecipientEntity;
use SonVideo\Erp\Carrier\Entity\ShipmentProperty\SenderEntity;

class ShipmentEntity extends AbstractEntity
{
    public InformationEntity $information;

    public SenderEntity $sender;

    public RecipientEntity $recipient;

    /** @var ParcelEntity[] */
    public array $parcels = [];
}
