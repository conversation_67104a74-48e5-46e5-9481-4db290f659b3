<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Entity\ShipmentProperty;

use App\Entity\AbstractEntity;

class ParcelProductEntity extends AbstractEntity
{
    public int $product_id;

    public ?string $description = null;

    public ?string $short_description = null;

    public ?int $quantity = null;

    public float $price_net_excluding_tax;

    public ?string $customs_code = null;

    public float $weight;

    public int $parcels;

    public string $origin_country_code;
}
