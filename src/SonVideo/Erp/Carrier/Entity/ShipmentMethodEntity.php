<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Entity;

use App\DataLoader\Type\JsonType;
use App\Entity\AbstractEntity;

class ShipmentMethodEntity extends AbstractEntity
{
    public int $shipment_method_id;

    public ?int $carrier_id = null;

    public string $code;

    public string $label;

    public ?bool $is_active = null;

    public ?string $comment = null;

    public ?string $type = null;

    public bool $is_mono_parcel;

    public ?int $store_pickup_id = null;

    /** @var array|JsonType|null */
    public ?array $tags = null;

    public ?ShipmentMethodCarrierEntity $carrier = null;
}
