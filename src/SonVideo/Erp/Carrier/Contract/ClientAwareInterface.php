<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Contract;

interface ClientAwareInterface
{
    /**
     * Set a client explicitly
     * Not in constructor as it's not requirement for all carriers.
     */
    public function setClient(CarrierClientInterface $client): ClientAwareInterface;
}
