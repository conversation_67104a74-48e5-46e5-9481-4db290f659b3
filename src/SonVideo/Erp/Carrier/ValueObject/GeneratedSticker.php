<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\ValueObject;

use League\Flysystem\FileNotFoundException;
use League\Flysystem\FilesystemInterface;
use League\Flysystem\MountManager;

class GeneratedSticker
{
    public const PDF_TYPE = 'pdf';
    public const ZPL_TYPE = 'zpl';
    private const HANDLED_STICKER_EXTENSIONS = ['zpl', 'pdf'];
    private const FILESYSTEM_NAME = 'sticker_filesystem';

    /** @var FilesystemInterface */
    private $filesystem;

    private ?string $filename = null;

    private array $parcels = [];

    /** GeneratedSticker constructor. */
    public function __construct(MountManager $mount_manager)
    {
        $this->filesystem = $mount_manager->getFilesystem(self::FILESYSTEM_NAME);
    }

    /**
     * createOrUpdate.
     *
     * @return $this
     *
     * @throws \Exception
     */
    public function createOrUpdate(string $reference, string $content, string $type = self::PDF_TYPE): self
    {
        $this->make($reference, $type);

        // $content is an url where to fetch the sticker from
        if (filter_var($content, FILTER_VALIDATE_URL)) {
            $stream = fopen($content, 'r');

            // copy the content stream in target file (resource)
            $result = $this->filesystem->putStream($this->getFilename(), $stream);

            // Prevent warning: supplied resource is not a valid stream resource
            if (is_resource($stream)) {
                fclose($stream);
            }

            if (false === $result) {
                throw new \Exception(sprintf('Count not write remote url file to target file: %s', $content));
            }
        } else {
            // Update or create
            $this->filesystem->put($this->getFilename(), $content);
        }

        return $this;
    }

    /**
     * load.
     *
     * @return $this
     */
    public function make(string $reference, string $type = self::PDF_TYPE): self
    {
        if (!in_array($type, self::HANDLED_STICKER_EXTENSIONS)) {
            throw new \InvalidArgumentException(sprintf('Sticker with extension "%s" is not handled by the application', $type));
        }

        $this->filename = strtolower(sprintf('%s.%s', $reference, $type));

        return $this;
    }

    /**
     * attachTrackingNumbers.
     *
     * @return $this
     */
    public function attachTrackingNumbers(array $parcels, array $tracking_numbers): self
    {
        if (1 === count($tracking_numbers) && count($parcels) > 1) {
            $tracking_numbers = array_pad($tracking_numbers, count($parcels), $tracking_numbers[0]);
        }

        foreach ($parcels as $key => $parcel) {
            $this->addParcel($parcel->parcel_id, $tracking_numbers[$key]);
        }

        return $this;
    }

    /**
     * addParcel.
     *
     * @param $tracking_number
     *
     * @return $this
     */
    public function addParcel(int $parcel_id, $tracking_number): self
    {
        $this->parcels[$parcel_id] = $tracking_number;

        return $this;
    }

    /** getParcels */
    public function getParcels(): array
    {
        return $this->parcels;
    }

    /** isLoaded */
    public function isLoaded(): bool
    {
        return null !== $this->filename;
    }

    /** exists */
    public function exists(): bool
    {
        if (null === $this->filename) {
            throw new \UnexpectedValueException('The sticker has not been prepared yet. Use the make method to do so.');
        }

        return $this->filesystem->has($this->getFilename());
    }

    /** getFilename */
    public function getFilename(): string
    {
        return $this->filename;
    }

    /**
     * getContent.
     *
     * @throws FileNotFoundException
     */
    public function getContent(): string
    {
        return $this->filesystem->read($this->filename);
    }
}
