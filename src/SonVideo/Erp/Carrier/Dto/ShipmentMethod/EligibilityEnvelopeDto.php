<?php

namespace SonVideo\Erp\Carrier\Dto\ShipmentMethod;

use Symfony\Component\Validator\Constraints as Assert;

final class EligibilityEnvelopeDto
{
    /**
     * @Assert\Valid
     * @Assert\NotBlank()
     * @Assert\All(
     *     @Assert\Type(EligibilityEnvelopeItemDto::class)
     * )
     *
     * @var EligibilityEnvelopeItemDto[]
     */
    public array $items = [];

    /**
     * @Assert\Valid
     * @Assert\NotBlank()
     */
    public EligibilityEnvelopeShippingAddressDto $shipping_address;

    public ?int $shipping_delay = null;
}
