<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Manager;

use SonVideo\Erp\Carrier\Entity\ShipmentEntity;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ShipmentEntityValidator
{
    /** @see entity classes with assertion in phpdoc */
    private const KEYS_TO_VALIDATE = ['information', 'sender', 'recipient'];

    private ValidatorInterface $validator;

    private array $errors = [];

    /** ShipmentValidator constructor. */
    public function __construct(ValidatorInterface $validator)
    {
        $this->validator = $validator;
    }

    /** validate */
    public function validate(ShipmentEntity $shipment): bool
    {
        foreach (self::KEYS_TO_VALIDATE as $key) {
            $violations = $this->validator->validate($shipment->{$key});
            $validation_errors = [];

            if (0 !== count($violations)) {
                /**
                 * @var ConstraintViolationInterface $violation
                 */
                foreach ($violations as $violation) {
                    $validation_errors[$violation->getPropertyPath()] = $violation->getMessage();
                }

                if ([] !== $validation_errors) {
                    $this->errors[$key] = $validation_errors;
                }
            }
        }

        return [] === $this->errors;
    }

    /** getErrorMessages */
    public function getErrors(): array
    {
        return $this->errors;
    }
}
