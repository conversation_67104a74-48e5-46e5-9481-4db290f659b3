<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Manager\Eligibility;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\Query\QueryBuilder;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\Carrier\Dto\ShipmentMethod\EligibilityEnvelopeDto;
use SonVideo\Erp\Carrier\Dto\ShipmentMethod\EligibleShipmentMethodDto;
use SonVideo\Erp\Carrier\Entity\ShipmentMethodEntity;
use SonVideo\Erp\Carrier\Exception\ShipmentMethod\EligibilityInvalidEnvelopeException;
use SonVideo\Erp\Carrier\Mysql\Repository\ShipmentMethodReadRepository;
use SonVideo\Erp\Referential\Rpc\BridgeRpcMethodReferential;
use SonVideo\Erp\Repository\Article\ArticleReadRepository;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use SonVideo\Synapps\Client\RpcClientAwareInterface;
use SonVideo\Synapps\Client\RpcClientAwareTrait;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class ShipmentMethodResolver implements RpcClientAwareInterface, LoggerAwareInterface
{
    use RpcClientAwareTrait;
    use LoggerAwareTrait;

    private const GENERIC_CUSTOMER = [
        'customer_id' => 970481,
        'email' => '<EMAIL>',
    ];

    private ValidatorInterface $validator;

    private SerializerInterface $serializer;

    private QueryBuilder $query_builder;

    private ArticleReadRepository $article_read_repository;

    private ShipmentMethodReadRepository $shipment_method_read_repository;

    public function __construct(
        ValidatorInterface $validator,
        SerializerInterface $serializer,
        QueryBuilder $query_builder,
        ArticleReadRepository $article_read_repository,
        ShipmentMethodReadRepository $shipment_method_read_repository
    ) {
        $this->validator = $validator;
        $this->serializer = $serializer;
        $this->query_builder = $query_builder;
        $this->article_read_repository = $article_read_repository;
        $this->shipment_method_read_repository = $shipment_method_read_repository;
    }

    /**
     * @return EligibleShipmentMethodDto[]
     *
     * @throws ExceptionInterface
     * @throws \Exception
     */
    public function resolve(EligibilityEnvelopeDto $envelope): array
    {
        $context = $this->getValidContext($envelope);

        // RPC call to bridge to fetch list of eligible shipment methods + cost
        $this->logger->debug('RPC Payload', $context);

        $eligible_shipment_methods = $this->rpc_client->call(
            BridgeRpcMethodReferential::SERVER_NAME,
            BridgeRpcMethodReferential::CARRIER_ELIGIBILTY_ENDPOINT,
            [$context]
        )['result'];

        $this->logger->debug('RPC Response', $eligible_shipment_methods);

        // Decorate results
        return $this->formatShipmentMethods($eligible_shipment_methods);
    }

    /**
     * @throws ExceptionInterface
     * @throws \Exception
     */
    public function getDelay(int $shipment_method_id, EligibilityEnvelopeDto $envelope): ?int
    {
        $context = $this->getValidContext($envelope);

        // RPC call to bridge to fetch the delay for the given shipment method
        $this->logger->debug(sprintf('%s RPC Payload', BridgeRpcMethodReferential::CARRIER_DELAY_ENDPOINT), [
            $shipment_method_id,
            $context,
        ]);

        $result = $this->rpc_client->call(
            BridgeRpcMethodReferential::SERVER_NAME,
            BridgeRpcMethodReferential::CARRIER_DELAY_ENDPOINT,
            [$shipment_method_id, $context]
        )['result'];

        $this->logger->debug(sprintf('%s RPC Response', BridgeRpcMethodReferential::CARRIER_DELAY_ENDPOINT), $result);

        return $result['delay'] ?? null;
    }

    private function formatArticles(array $items): array
    {
        $articles = $this->article_read_repository
            ->findAllPaginated(
                $this->query_builder->reset()->setWhere(
                    [
                        'sku' => ['_in' => array_column($items, 'sku')],
                    ],
                    ArticleReadRepository::COLUMNS_MAPPING
                )
            )
            ->getResults();

        foreach ($items as $key => $item) {
            if (!array_key_exists($item['sku'], $articles)) {
                throw new EligibilityInvalidEnvelopeException(sprintf('Article with sku "%s" does not exists', $item['sku']));
            }

            $items[$key]['article_id'] = $articles[$item['sku']]['product_id'];
            $items[$key]['description'] = $articles[$item['sku']]['short_description'];
            $items[$key]['order_line_no'] = $key + 1;
            $items[$key]['type'] = 'article';
        }

        return $items;
    }

    /**
     * @return EligibleShipmentMethodDto[]
     *
     * @throws ExceptionInterface
     */
    private function formatShipmentMethods(array $eligible_shipment_methods): array
    {
        $shipment_methods = $this->shipment_method_read_repository
            ->findAllPaginated(
                $this->query_builder
                    ->reset()
                    ->setWhere(
                        [
                            'shipment_method_id' => [
                                '_in' => array_column($eligible_shipment_methods, 'shipment_method_id'),
                            ],
                        ],
                        ShipmentMethodReadRepository::COLUMNS_MAPPING
                    )
                    ->setIncludedDependencies(['carrier'])
            )
            ->getResults();

        $serializer = $this->serializer;

        return array_map(static function (array $shipment_method) use (
            $serializer,
            $shipment_methods
        ): EligibleShipmentMethodDto {
            // Populate original results
            $current = $shipment_methods[$shipment_method['shipment_method_id']];
            $current['carrier'] = json_decode($current['carrier'], true);

            $current = $serializer->denormalize($current, ShipmentMethodEntity::class);

            // Returns eligibility DTO
            return $serializer->denormalize(
                array_merge($shipment_method, [
                    'label' => $current->label,
                    'comment' => $current->comment,
                    'carrier_name' => $current->carrier->name,
                    'is_retail_store' => $current->carrier->is_pick_up,
                    'tags' => $current->tags,
                ]),
                EligibleShipmentMethodDto::class
            );
        }, $eligible_shipment_methods);
    }

    /** @throws ExceptionInterface */
    public function getValidContext(EligibilityEnvelopeDto $envelope): array
    {
        $errors = $this->validator->validate($envelope);

        if (count($errors) > 0) {
            throw new EligibilityInvalidEnvelopeException(ConstraintMessageFormatter::prettify($errors));
        }

        // Convert to array
        $context = $this->serializer->normalize($envelope);

        // Prepare data
        $context['items'] = $this->formatArticles($context['items']);
        $context['customer'] = self::GENERIC_CUSTOMER;

        return $context;
    }
}
