<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Carrier\Entity\ShipmentEntity;
use SonVideo\Erp\Carrier\Entity\ShipmentProperty\InformationEntity;
use SonVideo\Erp\Carrier\Entity\ShipmentProperty\ParcelProductEntity;
use SonVideo\Erp\Carrier\Entity\ShipmentProperty\RecipientEntity;
use SonVideo\Erp\Carrier\Entity\ShipmentProperty\SenderEntity;

/**
 * Class ShipmentRepository.
 */
class StickerReadRepository extends AbstractLegacyRepository
{
    /** @var array */
    protected $data = [];

    /** load */
    public function load(int $delivery_note_id, ?int $expedition_parcel_id): ShipmentEntity
    {
        $this->information($delivery_note_id, $expedition_parcel_id)
            ->sender($delivery_note_id)
            ->recipient($delivery_note_id)
            ->parcels($delivery_note_id);

        return $this->data_loader->hydrate($this->data, ShipmentEntity::class);
    }

    /**
     * loadInformation.
     *
     * @return $this
     */
    protected function information(int $delivery_note_id, ?int $expedition_parcel_id): self
    {
        $sql = <<<SQL
        SELECT
          bl.id_bon_livraison                         AS delivery_note_id,
          t.code                                      AS carrier_code,
          bl.id_transporteur                          AS carrier_id,
          t.transporteur                              AS carrier_name,
          bl.id_pdt_transporteur                      AS shipment_method_id,
          btpl.commentaire                            AS shipment_method_name,
          btpl.code_produit                           AS shipment_method_code,
          bl.id_commande                              AS customer_order_id,
          ebl.expedition_id                           AS expedition_id,
          ebl.statut                                  AS print_status_code,
          bl.lvr_assurance = 'Y'                      AS use_insurance,
          COALESCE(ebl.maj_assurance, 0)              AS expedition_insurance_value,
          CMD_mnt_hors_frais_port_ttc(bl.id_commande) AS total_delivery_fee_excluded,
          CMD_mnt_hors_frais_port_ht(bl.id_commande)  AS total_delivery_fee_and_tax_excluded,
          btpl.mono_colis                             AS is_mono_parcel,
          :expedition_parcel_id                       AS expedition_parcel_id,
          CASE
            WHEN e.id IS NULL
              THEN 0
            ELSE COUNT(ebl.id) > 0
            END                                       AS is_registered_in_expedition,
          bl.date_export_transporteur IS NULL         AS can_be_generated
          FROM
            backOffice.bon_livraison                      bl
              INNER JOIN backOffice.commande              c ON bl.id_commande = c.id_commande
              INNER JOIN backOffice.transporteur          t ON bl.id_transporteur = t.id_transporteur
              INNER JOIN backOffice.BO_TPT_PDT_liste      btpl ON bl.id_pdt_transporteur = btpl.id
              LEFT JOIN  backOffice.BO_EXP_expeditions_bl ebl ON bl.id_bon_livraison = ebl.bon_livraison_id
              LEFT JOIN  backOffice.BO_EXP_expeditions    e ON e.id = ebl.expedition_id
          WHERE bl.id_bon_livraison = :delivery_note_id;
        SQL;

        $this->data['information'] = $this->data_loader->hydrate(
            $this->legacy_pdo->fetchOne($sql, [
                'delivery_note_id' => $delivery_note_id,
                'expedition_parcel_id' => $expedition_parcel_id,
            ]),
            InformationEntity::class
        );

        return $this;
    }

    /**
     * loadSender.
     *
     * @return $this
     */
    protected function sender(int $delivery_note_id): self
    {
        $sql = <<<SQL
        SELECT
          IF(
            c.creation_origine = 'ecranlounge.com',
            'EASYLOUNGE',
            'SON-VIDEO DISTRIBUTION'
            )                 AS name,
          btcc.id             AS account_id,
          btcc.code_client    AS account_code,
          btcc.libelle_client AS account_description,
          beee.societe        AS company_name,
          beee.adresse1       AS address_line1,
          beee.adresse2       AS address_line2,
          beee.code_postal    AS zip_code,
          beee.ville          AS city,
          beee.telephone      AS phone,
          beee.email          AS email,
          pe.pays              AS country,
          pe.code_2_lettres    AS country_code
          FROM
            backOffice.BO_TPT_CPT_client          btcc
              INNER JOIN backOffice.bon_livraison bl ON btcc.transporteur_id = bl.id_transporteur
              INNER JOIN backOffice.commande      c ON bl.id_commande = c.id_commande
              INNER JOIN backOffice.BO_EXP_expeditions_expediteurs        beee ON beee.id = IF(c.creation_origine = 'ecranlounge.com', 2, 1)
              INNER JOIN backOffice.pays                     pe ON beee.id_pays = pe.id_pays
              INNER JOIN backOffice.pays                     pbl ON bl.cnt_id_pays = pbl.id_pays
          WHERE bl.id_bon_livraison = :delivery_note_id
            AND btcc.libelle_client = IF(c.creation_origine = 'ecranlounge.com', 'EASYLOUNGE', 'SON-VIDEO DISTRIBUTION')
            AND IF(pbl.code_2_lettres = 'BE' AND bl.id_transporteur = 2,
                btcc.critere = pbl.code_2_lettres COLLATE latin1_general_cs,
                btcc.critere IS NULL
            )
        ;
        SQL;

        $this->data['sender'] = $this->data_loader->hydrate(
            $this->legacy_pdo->fetchOne($sql, ['delivery_note_id' => $delivery_note_id]),
            SenderEntity::class
        );

        return $this;
    }

    /**
     * loadRecipient.
     *
     * @return $this
     */
    protected function recipient(int $delivery_note_id): self
    {
        $sql = <<<SQL
        SELECT
          bl.cnt_civilite                                                                                   AS civility,
          bl.cnt_prenom                                                                                     AS first_name,
          bl.cnt_nom                                                                                        AS last_name,
          CASE WHEN CNT_adresse__get(bl.cnt_adresse, 1) IS NOT NULL
            AND LENGTH(CNT_adresse__get(bl.cnt_adresse, 1)) > 0
                 THEN CNT_adresse__get(bl.cnt_adresse, 1)
               ELSE NULL END                                                                                AS address_line1,
          CASE WHEN CNT_adresse__get(bl.cnt_adresse, 2) IS NOT NULL
            AND LENGTH(CNT_adresse__get(bl.cnt_adresse, 2)) > 0
                 THEN CNT_adresse__get(bl.cnt_adresse, 2)
               ELSE NULL END                                                                                AS address_line2,
          CASE WHEN CNT_adresse__get(bl.cnt_adresse, 3) IS NOT NULL
            AND LENGTH(CNT_adresse__get(bl.cnt_adresse, 3)) > 0
                 THEN CNT_adresse__get(bl.cnt_adresse, 3)
               ELSE NULL END                                                                                AS address_line3,
          CASE WHEN CNT_adresse__get(bl.cnt_adresse, 4) IS NOT NULL
            AND LENGTH(CNT_adresse__get(bl.cnt_adresse, 4)) > 0
                 THEN CNT_adresse__get(bl.cnt_adresse, 4)
               ELSE NULL END                                                                                AS address_line4,
          IF(bl.cnt_id_pays IN (36, 162), bl.cnt_code_postal,
             FORMAT_zipcode(bl.cnt_id_pays, FORMAT_zip(bl.cnt_code_postal)))                                AS zip_code,
          SUBSTRING(IF(bl.cnt_id_pays = 162, bl.cnt_code_postal,
                       FORMAT_zipcode(bl.cnt_id_pays, FORMAT_zip(bl.cnt_code_postal))), 1, 2)               AS region_code,
          UPPER(bl.cnt_ville)                                                                               AS city,
          p.code_2_lettres                                                                                  AS country,
          p.pays                                                                                            AS country_name,
          p.code_2_lettres                                                                                  AS country_code,
          backOffice.FORMAT_phone_number(bl.cnt_telephone)                                                  AS phone,
          backOffice.FORMAT_phone_number(bl.cnt_mobile)                                                     AS mobile,
          FORMAT_mail(bl.id_bon_livraison)                                                                  AS email,
          IF(bl.cnt_societe != '', bl.cnt_societe, NULL)                                                    AS company_name,
          cr.relay_id                                                                                       AS relay_id,
          IF(cr.relay_id IS NOT NULL, bl.cnt_societe, NULL)                                                 AS relay_name,
          IF(cr.relay_id IS NOT NULL, bl.cnt_adresse, NULL)                                                 AS relay_address,
          IF(cr.relay_id IS NOT NULL, UPPER(bl.cnt_ville), NULL)                                            AS relay_city,
          TPT_PDT_code(bl.id_pdt_transporteur)                                                              AS shipment_method_code,
          DATE_FORMAT(bccp.time_slot_start_date, '%Y-%m-%dT%H:%i:%s')                                       AS time_slot_start_date,
          DATE_FORMAT(bccp.time_slot_end_date, '%Y-%m-%dT%H:%i:%s')                                         AS time_slot_end_date,
          bccp.service_code                                                                                 AS service_code,
          bccp.time_slot_tariff_level                                                                       AS time_slot_tariff_level
          FROM
            backOffice.bon_livraison                     bl
              INNER JOIN backOffice.commande             c ON bl.id_commande = c.id_commande
              LEFT JOIN  backOffice.BO_CMD_chrono_precise bccp ON c.id_commande = bccp.id_commande
              INNER JOIN backOffice.pays                 p ON bl.cnt_id_pays = p.id_pays
              LEFT JOIN backOffice.commande_relay cr ON c.id_commande = cr.id_commande
          WHERE bl.id_bon_livraison = :delivery_note_id
        ;
        SQL;

        $this->data['recipient'] = $this->data_loader->hydrate(
            $this->legacy_pdo->fetchOne($sql, ['delivery_note_id' => $delivery_note_id]),
            RecipientEntity::class
        );

        return $this;
    }

    /**
     * loadParcels.
     *
     * @return $this
     */
    protected function parcels(int $delivery_note_id): self
    {
        $sql = <<<SQL
        SELECT
          beebc.id                                 AS parcel_id,
          beebc.nb_colis                           AS number,
          beebc.no_colis                           AS tracking_number,
          beebc.statut                             AS status,
          BO_EXP_bl_poids_colis(beebc.id)          AS weight,
          beebc.maj_poids                          AS updated_weight,
          GROUP_CONCAT(DISTINCT beebcp.produit_id) AS product_ids
          FROM
            BO_EXP_expeditions_bl                            beeb
              INNER JOIN BO_EXP_expeditions_bl_colis         beebc ON beebc.expedition_bl_id = beeb.id
              INNER JOIN BO_EXP_expeditions_bl_colis_produit beebcp ON beebcp.colis_id = beebc.id
          WHERE beeb.bon_livraison_id = :delivery_note_id
          GROUP BY beebc.id;
        SQL;

        $parcels = $this->legacy_pdo->fetchAssoc($sql, ['delivery_note_id' => $delivery_note_id]);
        $parcels_products = $this->parcelProducts($delivery_note_id);

        foreach ($parcels as $key => $parcel) {
            $ids = explode(',', $parcel['product_ids']);
            $parcels[$key]['products'] = array_values(
                array_filter($parcels_products, fn ($product): bool => in_array($product->product_id, $ids))
            );
        }

        $this->data['parcels'] = [] !== $parcels ? array_values($parcels) : [];

        return $this;
    }

    /** parcelProducts */
    protected function parcelProducts(int $delivery_note_id): array
    {
        $sql = <<<SQL
        SELECT
          pbl.id_produit                                                AS product_id,
          IFNULL(
            SUBSTRING(
              REPLACE(
                REPLACE(
                  REPLACE_crlf(
                    REPLACE(
                        FORMAT_string_whithout_accent(
                          SUBSTRING(pbl.description, 1, 50)
                          ) COLLATE latin1_general_cs, '\'', ' '
                      )
                    ), CHAR(92), ''
                  ), CHAR(09), ''
                ), 1, 25
              ), ''
            )                                                           AS description,
          IFNULL(
            SUBSTRING(
              REPLACE(
                REPLACE(
                  REPLACE_crlf(
                    REPLACE(
                        FORMAT_string_whithout_accent(
                          SUBSTRING(a.description_courte, 1, 50)
                          ) COLLATE latin1_general_cs, '\'', ' '
                      )
                    ), CHAR(92), ''
                  ), CHAR(09), ''
                ), 1, 25
              ), ''
            )                                                           AS short_description,
          pbl.quantite                                                  AS quantity,
          CAST(
            IFNULL(
                BL_PDT_px_vte_net_ht(pbl.id_bon_livraison, pbl.id_produit, pbl.id_unique) / pbl.quantite, 0
              ) AS DECIMAL(8, 2)
            )                                                           AS price_net_excluding_tax,
            IF(a.code_douanier IS NULL OR a.code_douanier = '', IF(cts.code_douanier IS NULL OR cts.code_douanier = '', ctc.code_douanier, cts.code_douanier), a.code_douanier) AS customs_code,
          ROUND(IFNULL(pbl.poids / pbl.quantite, 0), 3)                 AS weight,
          pbl.nombre_colis                                              AS parcels,
          IFNULL(
              PAYS_code(
                IF(
                  PDT_ART_pays_origine(a.id_produit) = 0, 40, PDT_ART_pays_origine(a.id_produit))
                ) COLLATE latin1_general_cs, 'CN'
            ) COLLATE latin1_general_cs                                 AS origin_country_code
          FROM
            article                            a
              INNER JOIN produit_bon_livraison pbl ON a.id_produit = pbl.id_produit
              INNER JOIN produit               p ON a.id_produit = p.id_produit
              INNER JOIN CTG_TXN_categorie     ctc ON p.V_id_categorie = ctc.id_categorie
              INNER JOIN CTG_TXN_souscategorie cts ON p.id_souscategorie = cts.id
          WHERE pbl.id_bon_livraison = :delivery_note_id
            AND (
              ctc.code_douanier != 0
              OR cts.code_douanier != 0
              OR a.code_douanier != 0
            )
          ORDER BY pbl.prix_vente DESC;
        SQL;

        $products = $this->legacy_pdo->fetchAssoc($sql, ['delivery_note_id' => $delivery_note_id]);
        $entities = [];

        foreach ($products as $product) {
            $entities[] = $this->data_loader->hydrate($product, ParcelProductEntity::class);
        }

        return $entities;
    }
}
