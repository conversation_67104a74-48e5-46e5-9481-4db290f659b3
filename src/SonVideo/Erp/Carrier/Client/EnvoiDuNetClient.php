<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Carrier\Client;

use App\Client\AbstractSoapClient;
use SonVideo\Erp\Carrier\Contract\EnvoiDuNetAwareTrait;

class EnvoiDuNetClient extends AbstractSoapClient
{
    use EnvoiDuNetAwareTrait;

    protected string $base_url;

    /** @var \stdClass */
    protected $login;

    /** EnvoiDuNetClient constructor. */
    public function __construct(string $envoidunet_ws_url, string $envoidunet_ws_account, string $envoidunet_ws_key)
    {
        // used fo ping only
        $this->base_url = $envoidunet_ws_url;

        // used for other requests
        $this->setAccount($envoidunet_ws_account, $envoidunet_ws_key);
    }

    /** @throws \SoapFault */
    public function getClient(): \SoapClient
    {
        return parent::init($this->base_url);
    }

    /**
     * setAccount.
     *
     * @return $this
     */
    protected function setAccount(string $account, string $key): self
    {
        $this->login = new \stdClass();
        $this->login->api_account = $account;
        $this->login->api_key = $key;

        return $this;
    }

    /** getAccount */
    public function getAccount(): \stdClass
    {
        return $this->login;
    }

    public function ping(): bool
    {
        $timeout = 10;
        $ch = curl_init($this->base_url);

        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        curl_exec($ch);

        // get HTTP response code
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        return $http_code >= 200 && $http_code < 300;
    }
}
