<?php

namespace SonVideo\Erp\Accounting\Payment\Manager;

use App\Entity\ColumnHelper;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sentry\InContextTagHolder;
use App\Sql\Query\QueryBuilder;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\CustomerOrderPayment\Manager\TransactionRemitter;
use SonVideo\Erp\CustomerOrderPayment\Manager\TransactionStateDecorator;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentReadRepository;

class PaymentManager
{
    public const RESPONSE_FIELDS = [
        'customer_order_payment_id',
        'remitted_at',
        'remit_error',
        'remitted_proof',
        'can_be_remitted',
        'can_cancel_remit',
        'computed_transaction_status',
    ];

    private CustomerOrderPaymentReadRepository $order_payment_repository;

    private QueryBuilder $query_builder;

    private TransactionStateDecorator $payment_decorator;

    private TransactionRemitter $transaction_remitter;

    private InContextTagHolder $in_context_tag_holder;

    private LoggerInterface $logger;

    public function __construct(
        CustomerOrderPaymentReadRepository $order_payment_repository,
        QueryBuilder $query_builder,
        TransactionStateDecorator $payment_decorator,
        TransactionRemitter $transaction_remitter,
        InContextTagHolder $in_context_tag_holder,
        LoggerInterface $logger
    ) {
        $this->order_payment_repository = $order_payment_repository;
        $this->query_builder = $query_builder;
        $this->payment_decorator = $payment_decorator;
        $this->transaction_remitter = $transaction_remitter;
        $this->in_context_tag_holder = $in_context_tag_holder;
        $this->logger = $logger;
    }

    /**
     * @throws NotFoundException
     * @throws \Exception
     */
    public function processRemit(array $payments): array
    {
        $result = $this->getManyPayments(array_keys($payments));

        $this->remit($this->payment_decorator->apply($result), $payments);

        $result = $this->getManyPayments(array_keys($payments));

        return ColumnHelper::intersect($this->payment_decorator->apply($result), self::RESPONSE_FIELDS);
    }

    /**
     * @throws InternalErrorException
     * @throws NotFoundException
     * @throws \Exception
     */
    public function processCancelRemit(int $payment_id): array
    {
        $result = $this->getOnePayment($payment_id);

        $this->transaction_remitter->cancelRemitOn($this->payment_decorator->apply([$result])[0]);

        $result = $this->getOnePayment($payment_id);

        return ColumnHelper::intersect($this->payment_decorator->apply([$result]), self::RESPONSE_FIELDS);
    }

    /** @throws \Exception */
    public function remit(array $payments, array $remit_data): void
    {
        foreach ($payments as $payment) {
            if (!$this->transaction_remitter->remit($payment, $remit_data[$payment['customer_order_payment_id']])) {
                $this->in_context_tag_holder->add([
                    'customer_order_payment_id' => $payment['customer_order_payment_id'],
                    'customer_order_id' => $payment['customer_order_id'],
                    'payment_mean' => $payment['payment_mean'],
                ]);
                $tags = $this->in_context_tag_holder->all();
                $this->logger->info('Payment remit failed', [
                    'exception' => 'PaymentCannotBeRemitted',
                    'tags' => $tags,
                    'type' => 'DomainException',
                ]);
            }
        }
    }

    /** @throws NotFoundException */
    private function getManyPayments(array $payments): array
    {
        $this->query_builder
            ->setWhere(
                [
                    [
                        'customer_order_payment_id' => [
                            '_in' => $payments,
                        ],
                    ],
                ],
                CustomerOrderPaymentReadRepository::COLUMNS_MAPPING
            )
            ->setPage(1, count($payments));

        $pager = $this->order_payment_repository->findAllPaginated($this->query_builder);

        if ([] === $pager->getResults()) {
            throw new NotFoundException('Could not load payment(s).');
        }

        return $pager->getResults();
    }

    /** @throws NotFoundException */
    private function getOnePayment(int $payment_id): \stdClass
    {
        $this->query_builder
            ->setWhere(
                [
                    [
                        'customer_order_payment_id' => [
                            '_eq' => $payment_id,
                        ],
                    ],
                ],
                CustomerOrderPaymentReadRepository::COLUMNS_MAPPING
            )
            ->setPage(1, 1);

        $pager = $this->order_payment_repository->findAllPaginated($this->query_builder);

        if (1 !== count($pager->getResults())) {
            throw new NotFoundException(sprintf('Could not load payment with id "%d".', $payment_id));
        }

        return $pager->getResults()[0];
    }
}
