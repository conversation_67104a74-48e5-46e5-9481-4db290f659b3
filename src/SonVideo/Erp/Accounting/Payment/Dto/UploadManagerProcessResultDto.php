<?php

namespace SonVideo\Erp\Accounting\Payment\Dto;

use OpenApi\Annotations as OA;
use SonVideo\Erp\Accounting\Payment\Contract\UploadManagerProcessResultDtoInterface;

class UploadManagerProcessResultDto implements UploadManagerProcessResultDtoInterface
{
    /** @OA\Property(description="Orders not found during import") */
    public array $orders_not_found = [];

    /** @OA\Property(description="Orders found during import") */
    public array $orders = [];

    /** @OA\Property(description="Payments found (and possibly remitted) during import") */
    public array $payments = [];

    /** @OA\Property(description="Unremitted payments") */
    public array $unremitted_payments = [];

    public function __construct(array $orders_not_found, array $orders, array $payments, array $unremitted_payments)
    {
        $this->orders_not_found = $orders_not_found;
        $this->orders = $orders;
        $this->payments = $payments;
        $this->unremitted_payments = $unremitted_payments;
    }

    /** @return array{orders_not_found: string[], orders: int[], payments: mixed, unremitted_payments: mixed, total: int} */
    public function toArray(): array
    {
        return [
            'orders_not_found' => array_values($this->orders_not_found),
            'orders' => array_map('intval', $this->orders),
            'payments' => $this->payments,
            'unremitted_payments' => $this->unremitted_payments,
            'total' => is_countable($this->payments) ? count($this->payments) : 0,
        ];
    }
}
