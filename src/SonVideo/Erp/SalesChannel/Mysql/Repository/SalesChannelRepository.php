<?php

namespace SonVideo\Erp\SalesChannel\Mysql\Repository;

use App\Exception\NotFoundException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\SalesChannel\Dto\SalesChannelContextDto;
use SonVideo\Erp\SalesChannel\Entity\SalesChannelEntity;

class SalesChannelRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'sales_channel_id' => 'sc.id',
        'label' => 'sc.label',
        'legacy_name' => 'sc.legacy_name',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                sc.id AS sales_channel_id,
                sc.label,
                sc.legacy_name,
                sc.display_order,
                sc.average_commission_rate,
                sc.minimum_margin_rate,
                sc.minimum_available_quantity,
                sc.minimum_selling_price,
                sc.maximum_selling_price
              FROM backOffice.sales_channel sc
                WHERE {where}
              ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{where}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateEntity(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters(),
            SalesChannelEntity::class
        );
    }

    /** @throws NotFoundException */
    public function getOneById(int $sales_channel_id): array
    {
        $sql = <<<MYSQL
        SELECT
          sc.id AS sales_channel_id,
          sc.label,
          sc.legacy_name,
          sc.display_order,
          sc.minimum_margin_rate,
          sc.minimum_available_quantity,
          sc.minimum_selling_price,
          sc.maximum_selling_price
        FROM backOffice.sales_channel sc
        WHERE sc.id = :sales_channel_id
        MYSQL;

        $result = $this->legacy_pdo->fetchOne($sql, ['sales_channel_id' => $sales_channel_id]);
        if (false === $result) {
            throw new NotFoundException(sprintf('No sales channel found with id %d.', $sales_channel_id));
        }

        return $result;
    }

    public function update(SalesChannelContextDto $sales_channel): int
    {
        $sql = <<<SQL
         UPDATE backOffice.sales_channel
         SET average_commission_rate = :average_commission_rate,
             minimum_margin_rate = :minimum_margin_rate,
             minimum_available_quantity = :minimum_available_quantity
         WHERE id = :sales_channel_id
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'average_commission_rate' => $sales_channel->average_commission_rate,
            'sales_channel_id' => $sales_channel->sales_channel_id,
            'minimum_margin_rate' => $sales_channel->minimum_margin_rate,
            'minimum_available_quantity' => $sales_channel->minimum_available_quantity,
        ]);
    }

    public function findStatisticsSalesChannels(array $sales_channel_ids): array
    {
        $sql = <<<SQL
        SELECT
            sc.id AS sales_channel_id,
            count(*) AS number_products_configured,
            sum(backOffice.can_sell_on_sales_channel(scp.product_id, scp.sales_channel_id)) AS number_eligible_products
            FROM backOffice.sales_channel sc
                 INNER JOIN backOffice.sales_channel_product scp ON sc.id = scp.sales_channel_id
                 INNER JOIN backOffice.article a ON a.id_produit = scp.product_id AND a.status IN ('oui', 'last')
        WHERE sc.id IN (:sales_channel_ids)
        AND scp.is_active
        GROUP BY sc.id
        SQL;

        return $this->legacy_pdo->fetchAll($sql, ['sales_channel_ids' => $sales_channel_ids]);
    }
}
