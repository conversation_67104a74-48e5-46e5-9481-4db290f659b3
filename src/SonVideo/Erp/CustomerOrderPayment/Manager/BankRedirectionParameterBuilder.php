<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrderPayment\Manager;

use Stringy\Stringy;

final class BankRedirectionParameterBuilder
{
    /** Force accent removal on those columns */
    private const ASCII_COLUMNS = [
        'shipping_address_civility',
        'shipping_address_last_name',
        'shipping_address_first_name',
        'shipping_address_address',
        'shipping_address_postal_code',
        'shipping_address_city',
        'shipping_address_country_code',
    ];

    public static function getParametersFor(array $payment, array $articles): array
    {
        $payment = self::prepareBeforeAssignment($payment);

        if ('EKDO' === $payment['payment_method_code']) {
            // eKdo send back little information : this hack is necessary to get back the amount.
            // cf (legacy). gestion-paiements/public_html/retours/neosurf/retour.php
            $payment['transaction_number'] .= '---' . $payment['amount'] * 100;
        }

        $parameters = [
            $payment['transaction_number'],
            $payment['payment_method_code'],
            $payment['amount'],
            $payment['user_email'],
        ];

        // FYI: PRESTO used to be there but is now handled by Payment v2
        if (in_array($payment['payment_method_code'], ['1EC', 'PRESTO', 'NXCB'])) {
            $parameters = [
                $payment['transaction_number'],
                $payment['payment_method_code'],
                $payment['amount'],
                $payment['user_email'],
                $payment['billing_address_civility'],
                $payment['billing_address_first_name'],
                $payment['billing_address_last_name'],
                $payment['billing_address_address'],
                $payment['billing_address_postal_code'],
                $payment['billing_address_city'],
                $payment['billing_address_phone'],
                $payment['billing_address_cellphone'],
                null,
                null,
                null,
                null,
                $articles,
                null,
                $payment['user_id'],
            ];
        }

        // FYI: Worldline payments (CBS-O and AMX-O) are now handled by Payment v2
        if (
            preg_match(
                '/^CBS-EMPT$|^CBS-COT$|^CBS-O$|^AMX-O$|^CGA-O$|^AUR-O$|^PPAL-O$|^CBS-OT$|^CBS-O2$|^AMX-OT$|^CGA-OT$|^AUR-OT$|^PPAL$/',
                $payment['payment_method_code']
            )
        ) {
            $parameters = [
                $payment['transaction_number'],
                $payment['payment_method_code'],
                $payment['amount'],
                $payment['user_email'],
                $payment['shipping_address_civility'],
                $payment['shipping_address_first_name'],
                $payment['shipping_address_last_name'],
                $payment['shipping_address_address'],
                $payment['shipping_address_postal_code'],
                $payment['shipping_address_city'],
                $payment['shipping_address_phone'],
                $payment['shipping_address_cellphone'],
                $payment['shipping_address_country_code'],
                'son-video.com',
            ];
        }

        return $parameters;
    }

    private static function prepareBeforeAssignment(array $payment): array
    {
        foreach ($payment as $key => $value) {
            if (in_array($key, self::ASCII_COLUMNS)) {
                $payment[$key] = Stringy::create($value)
                    ->toAscii()
                    ->__toString();
            }
        }

        return $payment;
    }
}
