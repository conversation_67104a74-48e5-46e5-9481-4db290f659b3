<?php

namespace SonVideo\Erp\PricingStrategy\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Sql\Helper\InsertValues;

class PricingStrategyCompetitorRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public function removeCompetitorsFromStrategy(int $pricing_strategy_id): int
    {
        $sql = <<<SQL
        DELETE FROM backOffice.pricing_strategy_competitor WHERE pricing_strategy_id = :pricing_strategy_id;
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, ['pricing_strategy_id' => $pricing_strategy_id]);
    }

    public function attachCompetitorsToStrategy(int $pricing_strategy_id, array $competitors): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.pricing_strategy_competitor ({columns}) VALUES {values};
        SQL;

        $insert_values = new InsertValues(['pricing_strategy_id', 'competitor_code']);

        foreach ($competitors as $competitor_code) {
            $insert_values->addValues([
                'pricing_strategy_id' => $pricing_strategy_id,
                'competitor_code' => $competitor_code,
            ]);
        }

        $sql = strtr($sql, [
            '{columns}' => $insert_values->getColumnList(),
            '{values}' => $insert_values,
        ]);

        return $this->legacy_pdo->fetchAffected($sql, $insert_values->getParams());
    }
}
