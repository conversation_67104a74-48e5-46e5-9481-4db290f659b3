<?php

namespace SonVideo\Erp\PricingStrategy\Manager;

use App\Exception\InternalServerErrorException;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\Query\QueryBuilder;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use SonVideo\Erp\PricingStrategy\Dto\PricingStrategyProductPriceResponseDto;
use SonVideo\Erp\PricingStrategy\Dto\PricingStrategyProductResponseDto;
use SonVideo\Erp\PricingStrategy\Entity\PricingStrategyProductEntity;
use SonVideo\Erp\PricingStrategy\Entity\PricingStrategyProductPriceEntity;
use SonVideo\Erp\PricingStrategy\Mysql\Repository\PricingStrategyRepository;
use SonVideo\Erp\Referential\PricingStrategyStatus;
use SonVideo\Erp\SalesChannel\Exception\MarginValidationException;

class PricingStrategyEngineManager implements LoggerAwareInterface
{
    use LoggerAwareTrait;
    private PricingStrategyEngine $pricing_strategy_engine;

    private PricingStrategyRepository $pricing_strategy_repository;
    private QueryBuilder $query_builder;

    public function __construct(
        PricingStrategyEngine $pricing_strategy_engine,
        PricingStrategyRepository $pricing_strategy_repository,
        QueryBuilder $query_builder
    ) {
        $this->pricing_strategy_engine = $pricing_strategy_engine;
        $this->pricing_strategy_repository = $pricing_strategy_repository;
        $this->query_builder = $query_builder;
    }

    public function runOngoingActiveStrategies(): void
    {
        $query_builder = $this->query_builder
            ->setWhere([
                'activation_status' => ['_eq' => PricingStrategyStatus::ACTIVATED],
            ])
            ->setPage(1, 100000);

        $pricing_strategies_list = $this->pricing_strategy_repository->findAllPaginated($query_builder)->getResults();

        foreach ($pricing_strategies_list as $pricing_strategy) {
            try {
                $this->runStrategyById($pricing_strategy['pricing_strategy_id'], false);
            } catch (\Exception $e) {
                $this->logger->error(
                    sprintf(
                        'An error occurred on pricing strategy %s - %s',
                        $pricing_strategy['pricing_strategy_id'],
                        $e->getMessage()
                    )
                );
            }
        }
    }

    /**
     * @throws MarginValidationException
     * @throws InternalServerErrorException
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws \Exception
     */
    public function runStrategyById(int $pricing_strategy_id, bool $dry_run = true): array
    {
        return $this->pricing_strategy_engine->run($pricing_strategy_id, $dry_run);
    }

    /** @return PricingStrategyProductResponseDto[] */
    public function reduceResponse(array $products): array
    {
        $pricing_strategy_products = [];
        foreach ($products as $product) {
            $pricing_strategy_products[] = $this->fromPricingStrategyProductEntity($product);
        }

        return $pricing_strategy_products;
    }

    private function fromPricingStrategyProductEntity(
        PricingStrategyProductEntity $pricing_strategy_product
    ): PricingStrategyProductResponseDto {
        $output_dto = new PricingStrategyProductResponseDto();

        $output_dto->pricing_strategy_id = $pricing_strategy_product->pricing_strategy_id;
        $output_dto->sku = $pricing_strategy_product->sku;
        $output_dto->article_id = $pricing_strategy_product->article_id;
        $output_dto->image = $pricing_strategy_product->image;
        $output_dto->article_name = $pricing_strategy_product->article_name;
        $output_dto->selling_price = $pricing_strategy_product->selling_price;
        $output_dto->stock = $pricing_strategy_product->stock;
        $output_dto->category = $pricing_strategy_product->category;
        $output_dto->margin = $pricing_strategy_product->margin;
        $output_dto->margin_rate = $pricing_strategy_product->margin_rate;
        $output_dto->margin_tax_excluded = $pricing_strategy_product->margin_tax_excluded;
        $output_dto->last_scrapping_date = $pricing_strategy_product->last_scrapping_date;
        $output_dto->purchase_price = $pricing_strategy_product->purchase_price;
        $output_dto->sorecop = $pricing_strategy_product->sorecop;
        $output_dto->ecotax = $pricing_strategy_product->ecotax;
        $output_dto->promo_budget_amount = $pricing_strategy_product->promo_budget_amount;
        $output_dto->unconditional_discount = $pricing_strategy_product->unconditional_discount;
        $output_dto->weighted_cost_tax_excluded = $pricing_strategy_product->weighted_cost_tax_excluded;
        $output_dto->status = $pricing_strategy_product->status;
        $output_dto->delay = $pricing_strategy_product->delay;
        $output_dto->pvgc = $pricing_strategy_product->pvgc;
        $output_dto->is_margin_rate_to_low = $pricing_strategy_product->is_margin_rate_to_low;
        $output_dto->lowest_competitor = [
            'competitor_code' => $pricing_strategy_product->lowest_competitor->competitor_code,
            'selling_price_with_taxes' => $pricing_strategy_product->lowest_competitor->selling_price_with_taxes,
        ];

        foreach ($pricing_strategy_product->new_prices as $new_price) {
            $output_dto->new_prices[] = $this->fromPricingStrategyProductPriceEntity($new_price);
        }

        return $output_dto;
    }

    private function fromPricingStrategyProductPriceEntity(
        PricingStrategyProductPriceEntity $pricing_strategy_product_price
    ): PricingStrategyProductPriceResponseDto {
        $output_dto = new PricingStrategyProductPriceResponseDto();

        $output_dto->sales_channel = $pricing_strategy_product_price->sales_channel;
        $output_dto->margin = $pricing_strategy_product_price->margin;
        $output_dto->current_margin_rate = $pricing_strategy_product_price->current_margin_rate;
        $output_dto->selling_price_tax_excluded = $pricing_strategy_product_price->selling_price_tax_excluded;
        $output_dto->margin_rate = $pricing_strategy_product_price->margin_rate;
        $output_dto->margin_rate_if_cheapest = $pricing_strategy_product_price->margin_rate_if_cheapest;
        $output_dto->current_selling_price_tax_included =
            $pricing_strategy_product_price->current_selling_price_tax_included;
        $output_dto->selling_price_tax_included = $pricing_strategy_product_price->selling_price_tax_included;
        $output_dto->selling_price_if_cheapest = $pricing_strategy_product_price->selling_price_if_cheapest;

        return $output_dto;
    }
}
