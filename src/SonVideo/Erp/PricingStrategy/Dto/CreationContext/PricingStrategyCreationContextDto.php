<?php

namespace SonVideo\Erp\PricingStrategy\Dto\CreationContext;

use App\Validator\Constraint as CustomAssert;
use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

class PricingStrategyCreationContextDto
{
    /**
     * @OA\Property(description="Nom de la stratégie")
     * @Assert\NotBlank()
     */
    public string $name;

    /**
     * @OA\Property(description="Début de la stratégie")
     * @Assert\DateTime()
     * @Assert\NotBlank()
     */
    public string $starts_at;

    /**
     * @OA\Property(description="Fin de la stratégie")
     * @Assert\DateTime()
     * @Assert\NotBlank()
     * @Assert\GreaterThan(propertyPath="starts_at", message="[key:end_date_must_be_higher_than_start_date] End date must be higher than start date")
     */
    public string $ends_at;

    /**
     * @OA\Property(description="Statut de la stratégie")
     * @Assert\NotBlank()
     * @Assert\Choice(callback={\SonVideo\Erp\Referential\PricingStrategyStatus::class, "getConstants"})
     */
    public string $activation_status;

    /** @OA\Property(description="Écart de prix avec le concurrent en semaine") */
    public float $weekdays_increment_amount;

    /**
     * @OA\Property(description="Marge minimale en semaine")
     * @Assert\Positive()
     */
    public float $weekdays_min_margin_rate;

    /** @OA\Property(description="Écart de prix avec le concurrent le weekend") */
    public float $weekend_increment_amount;

    /**
     * @OA\Property(description="Marge minimale le weekend")
     * @Assert\Positive()
     */
    public float $weekend_min_margin_rate;

    /**
     * @OA\Property(description="Canaux de vente")
     * @Assert\Type("array")
     * @CustomAssert\SalesChannels()
     *
     * @var int[]
     */
    public array $sales_channels = [];

    /**
     * @OA\Property(description="Compétiteurs")
     * @Assert\Type("array")
     *
     * @var string[]
     */
    public array $competitors = [];
}
