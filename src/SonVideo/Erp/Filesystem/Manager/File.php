<?php

namespace SonVideo\Erp\Filesystem\Manager;

use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use League\Flysystem\FilesystemInterface;
use League\Flysystem\MountManager;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\HttpFoundation\File\File as HttpFile;
use Symfony\Component\HttpFoundation\File\UploadedFile;

abstract class File implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    public const FILESYSTEM = 'default_filesystem';

    protected FilesystemInterface $filesystem;

    public function __construct(MountManager $mount_manager)
    {
        $this->filesystem = $mount_manager->getFilesystem(static::FILESYSTEM);
    }

    /** @throws FileExistsException|FileNotFoundException */
    protected function write(UploadedFile $uploaded_file, callable $get_file_name): string
    {
        $this->logger->info(sprintf('Original file name: %s', $uploaded_file->getClientOriginalName()));

        $target_filename = $get_file_name($uploaded_file);

        $this->logger->info(sprintf('Target file name: %s', $target_filename));

        $this->deleteFileIfExists($target_filename);

        $stream = fopen($uploaded_file->getRealPath(), 'r+');
        $this->filesystem->writeStream($target_filename, $stream);
        fclose($stream);

        return $target_filename;
    }

    /**
     * @throws FileNotFoundException
     * @throws FileExistsException
     */
    protected function copy(string $path_from, callable $get_file_name): string
    {
        $path_to = $get_file_name(new HttpFile($path_from, false));

        $buffer = $this->filesystem->readStream($path_from);

        if (false === $buffer) {
            return false;
        }

        $this->deleteFileIfExists($path_to);

        $this->filesystem->writeStream($path_to, $buffer);

        if (is_resource($buffer)) {
            fclose($buffer);
        }

        return $path_to;
    }

    public function getFilesystem(): FilesystemInterface
    {
        return $this->filesystem;
    }

    /** @throws FileNotFoundException */
    public function deleteFileIfExists(string $target_filename): void
    {
        if ($this->filesystem->has($target_filename)) {
            $this->filesystem->delete($target_filename);
        }
    }

    public function getFileExtension(HttpFile $uploaded_file): string
    {
        if ($uploaded_file instanceof UploadedFile) {
            return strlen($uploaded_file->getClientOriginalExtension()) > 2
                ? $uploaded_file->getClientOriginalExtension()
                : $uploaded_file->getExtension();
        }

        return $uploaded_file->getExtension();
    }

    public function exists(string $relative_path): bool
    {
        return $this->filesystem->has($relative_path);
    }

    public function getContent(string $relative_path)
    {
        if (!$this->exists($relative_path)) {
            return false;
        }

        return $this->filesystem->read($relative_path);
    }

    public function getMimeType(string $relative_path)
    {
        if (!$this->exists($relative_path)) {
            return false;
        }

        return $this->filesystem->getMimetype($relative_path);
    }
}
