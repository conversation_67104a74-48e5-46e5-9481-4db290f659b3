<?php

namespace SonVideo\Erp\Inventory\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

class InventoryZoneReadRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public const COLUMNS_MAPPING = [
        'inventory_id' => 'bii.id',
        'warehouse_id' => 'biz.warehouse_id',
        'zone_id' => 'biz.zone_id',
        'name' => 'biz.name',
        'last_inventory_date' => 'last_inventory_date',
        'location_count' => 'loc.count',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
               SELECT
                 biz.zone_id                     AS zone_id,
                 biz.name                        AS name,
                 (SELECT MAX(_bii.inv_date_validation)
                    FROM
                        backOffice.BO_INV_inventaire _bii
                            LEFT JOIN backOffice.BO_INV_zone_inventory _bizi
                                ON (_bii.id = _bizi.inventory_id)
                                AND _bii.statut = 'closed'
                       WHERE _bizi.zone_id = biz.zone_id OR _bii.type = 'full'
                     ) AS last_inventory_date
               FROM
                 backOffice.BO_INV_zone biz
                   LEFT JOIN backOffice.BO_INV_zone_inventory bizi ON bizi.zone_id = biz.zone_id
                   LEFT JOIN (SELECT zone_id, count(*) AS count FROM backOffice.BO_INV_zone_location GROUP BY zone_id) loc ON biz.zone_id = loc.zone_id
                   LEFT JOIN backOffice.BO_INV_inventaire bii ON bii.id = bizi.inventory_id
               WHERE {conditions}
               GROUP BY biz.zone_id
             ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_readonly_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }
}
