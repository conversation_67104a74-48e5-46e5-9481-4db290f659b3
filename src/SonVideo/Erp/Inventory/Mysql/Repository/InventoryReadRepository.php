<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Inventory\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

/**
 * Class InventoryReadRepository.
 */
class InventoryReadRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public const COLUMNS_MAPPING = [
        'inventory_id' => 'bii.id',
        'created_at' => 'bii.inv_date',
        'validated_at' => 'bii.inv_date_validation',
        'validated_by' => 'bii.inv_id_utilisateur_validation',
        'closed_at' => 'bii.inv_date_closed',
        'active_inventory_collect_id' => 'bii.collecte_active_id',
        'warehouse_id' => 'bii.id_depot',
        'warehouse_name' => 'bsd.nom_depot',
        'type' => 'bii.type',
        'name' => 'bii.name',
    ];

    public const BRANDS_COLUMNS_MAPPING = [
        'inventory_id' => 'bid.inventory_id',
        'brand_id' => 'bid.brand_id',
        'brand_name' => 'm.marque',
        'total_expected_quantities' => 'bid.expected_quantity',
        'total_expected_stock_values' => 'bid.expected_total_value',
        'total_counted_quantities' => 'bid.counted_quantity',
        'total_counted_stock_values' => 'bid.counted_total_value',
    ];

    public const PRODUCTS_COLUMNS_MAPPING = [
        'inventory_id' => 'bii.id',
        'differential_id' => 'bid.differential_id',
        'brand_id' => 'bid.brand_id',
        'product_id' => 'p.id_produit',
        'sku' => 'p.reference',
        'short_description' => 'a.description_courte',
        'buy_price' => 'bid.buy_price',
        'expected_quantity' => 'bid.expected_quantity',
        'expected_total_value' => 'bid.expected_total_value',
        'counted_quantity' => 'bid.counted_quantity',
        'counted_total_value' => 'bid.counted_total_value',
        'computed_stock_difference' => 'bid.computed_stock_difference',
        'computed_total_value_difference' => 'bid.computed_total_value_difference',
        'is_validated_manually' => 'bid.is_validated_manually',
    ];

    public const INFO_COLUMS_MAPPING = [
        'article_collect_id' => 'bica.id',
        'product_id' => 'bica.id_produit',
        'location_id' => 'bica.id_emplacement',
        'user_id' => 'bica.sf_guard_user_id',
        'quantity' => 'bica.quantite',
        'collect_id' => 'bic.id',
        'collect_number' => 'bic.numero',
        'collect_type' => 'bic.collecte_type',
        'active_collect_id' => 'bii.collecte_active_id',
        'is_validated' => 'bii.inv_date_validation',
        'is_closed' => 'bii.inv_date_closed',
        'inventory_id' => 'bii.id',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                bii.id                                                                     AS inventory_id,
                bii.inv_date                                                               AS created_at,
                bii.inv_date_validation                                                    AS validated_at,
                bii.inv_id_utilisateur_validation                                          AS validated_by,
                backOffice.GET_COMPUTED_USER_NAME_BY_ID(bii.inv_id_utilisateur_validation) AS validated_by_name,
                bii.inv_date_closed                                                        AS closed_at,
                bii.id_depot                                                               AS warehouse_id,
                bsd.nom_depot                                                              AS warehouse_name,
                bii.statut                                                                 AS status,
                bii.collecte_active_id                                                     AS active_inventory_collect_id,
                bii.type                                                                   AS type,
                bii.name                                                                   AS name,
                CASE WHEN bic.id IS NOT NULL AND bic.numero = 1
                       THEN 'global'
                     WHEN bic.id IS NOT NULL AND bic.numero = 1
                       THEN 'recount'
                     ELSE NULL END                                                         AS active_collect_type,
                bic.numero                                                                 AS active_collect_number
                FROM
                  backOffice.BO_INV_inventaire            bii
                    INNER JOIN backOffice.BO_STK_depot    bsd ON bii.id_depot = bsd.id
                    LEFT JOIN  backOffice.BO_INV_collecte bic ON bii.id = bic.BO_INV_inventaire_id AND bic.id = bii.collecte_active_id
                WHERE {conditions}
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_readonly_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    /** @throws SqlErrorMessageException */
    public function exists(int $inventory_id): bool
    {
        $result = $this->legacy_readonly_pdo->fetchValue(
            <<<SQL
            SELECT
              EXISTS(
                SELECT 1 FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id
                ) AS entry;
            SQL
            ,
            ['inventory_id' => $inventory_id]
        );

        return (bool) $result;
    }

    /** @throws NotFoundException */
    public function findOneById(int $inventory_id): array
    {
        $sql = <<<SQL
        SELECT
          bii.id                                                                     AS inventory_id,
          bii.inv_date                                                               AS created_at,
          bii.id_depot                                                               AS warehouse_id,
          bsd.nom_depot                                                              AS warehouse_name,
          bii.collecte_active_id                                                     AS active_collect_id,
          bii.inv_date_validation                                                    AS validated_at,
          bii.inv_id_utilisateur_validation                                          AS validated_by,
          backOffice.GET_COMPUTED_USER_NAME_BY_ID(bii.inv_id_utilisateur_validation) AS validated_by_name,
          bii.inv_date_closed                                                        AS closed_at,
          bii.statut                                                                 AS status,
          bic.numero                                                                 AS active_collect_number,
          bii.type                                                                   AS type,
          bii.name                                                                   AS name
          FROM
            backOffice.BO_INV_inventaire bii
              LEFT JOIN backOffice.BO_INV_collecte bic ON bii.collecte_active_id = bic.id
              INNER JOIN backOffice.BO_STK_depot bsd ON bii.id_depot = bsd.id
          WHERE bii.id = :inventory_id
        SQL;

        $inventory = $this->legacy_readonly_pdo->fetchOne($sql, ['inventory_id' => $inventory_id]);

        if (false === $inventory) {
            throw new NotFoundException('Inventory not found.');
        }

        $inventory['collects'] = $this->getCollectsFor($inventory_id);

        return $inventory;
    }

    protected function getCollectsFor(int $inventory_id): array
    {
        $sql = <<<SQL
        SELECT
          bic.id                          AS collect_id,
          bic.collecte_type               AS collect_type,
          bic.numero                      AS collect_number,
          COUNT(bica.id)                  AS counted_articles,
          bii.collecte_active_id = bic.id AS is_active
          FROM
            backOffice.BO_INV_collecte                     bic
              LEFT JOIN backOffice.BO_INV_collecte_article bica ON bic.id = bica.BO_INV_collecte_id
              LEFT JOIN backOffice.BO_INV_inventaire       bii ON bic.BO_INV_inventaire_id = bii.id
          WHERE bic.BO_INV_inventaire_id = :inventory_id
          GROUP BY bic.id
          ORDER BY bic.numero
        SQL;

        return $this->legacy_readonly_pdo->fetchAll($sql, ['inventory_id' => $inventory_id]);
    }

    public function findAllBrandsPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
        (
          SELECT
            bid.brand_id                  AS brand_id,
            m.marque                      AS brand_name,
            COUNT(bid.product_id)         AS total_products,
            SUM(bid.expected_quantity)    AS total_expected_quantities,
            SUM(bid.expected_total_value) AS total_expected_stock_values,
            SUM(bid.counted_quantity)     AS total_counted_quantities,
            SUM(bid.counted_total_value)  AS total_counted_stock_values
            FROM
              backOffice.BO_INV_differential bid
                INNER JOIN backOffice.marque m ON bid.brand_id = m.id_marque
            WHERE {conditions}
            GROUP BY m.id_marque
          ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_readonly_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function findAllProductsPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                bii.id                                   AS inventory_id,
                bid.id                                   AS differential_id,
                bid.brand_id                             AS brand_id,
                p.id_produit                             AS product_id,
                p.reference                              AS sku,
                a.description_courte                     AS short_description,
                bid.buy_price                            AS buy_price,
                bid.expected_quantity                    AS expected_quantity,
                bid.expected_total_value                 AS expected_total_value,
                bid.counted_quantity                     AS counted_quantity,
                bid.counted_total_value                  AS counted_total_value,
                bid.computed_stock_difference            AS computed_stock_difference,
                bid.computed_total_value_difference      AS computed_total_value_difference,
                ABS(bid.computed_stock_difference)       AS abs_computed_stock_difference,
                ABS(bid.computed_total_value_difference) AS abs_computed_total_value_difference,
                bid.is_validated_manually                AS is_validated_manually,
                bid.is_validated_automatically           AS is_validated_automatically
                FROM
                  backOffice.BO_INV_inventaire                bii
                    LEFT JOIN  backOffice.BO_INV_differential bid ON bii.id = bid.inventory_id
                    INNER JOIN backOffice.produit             p ON bid.product_id = p.id_produit
                    INNER JOIN backOffice.article             a ON p.id_produit = a.id_produit
                WHERE {conditions}
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_readonly_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function getDifferentialStatistics(int $inventory_id): array
    {
        $sql = <<<SQL
        SELECT
          'EXPECTED'    AS label,
          COUNT(bid.id) AS total
          FROM
            backOffice.BO_INV_differential            bid
              INNER JOIN backOffice.BO_INV_inventaire bii ON bid.inventory_id = bii.id
          WHERE bii.id = :inventory_id
            AND bid.expected_quantity > 0
        UNION
        SELECT
          'TO_COUNT'    AS label,
          COUNT(bid.id) AS total
          FROM
            backOffice.BO_INV_differential            bid
              INNER JOIN backOffice.BO_INV_inventaire bii ON bid.inventory_id = bii.id
          WHERE bii.id = :inventory_id
            AND bid.counted_quantity = 0
            AND bid.expected_quantity > 0
            AND bid.is_validated_manually IS FALSE
            AND bid.is_validated_automatically IS FALSE
        UNION
        SELECT
          'COUNTED'     AS label,
          COUNT(bid.id) AS total
          FROM
            backOffice.BO_INV_differential            bid
              INNER JOIN backOffice.BO_INV_inventaire bii ON bid.inventory_id = bii.id
          WHERE bii.id = :inventory_id
            AND bid.counted_quantity > 0
        UNION
        SELECT
          'CAN_BE_AUTOMATICALLY_VALIDATED' AS label,
          COUNT(bid.id)                    AS total
          FROM
            backOffice.BO_INV_differential            bid
              INNER JOIN backOffice.BO_INV_inventaire bii ON bid.inventory_id = bii.id
          WHERE bii.id = :inventory_id
            AND bid.counted_quantity > 0
            AND bid.expected_quantity = bid.counted_quantity
        UNION
        SELECT
          'MANUALLY_VALIDATED' AS label,
          COUNT(bid.id)        AS total
          FROM
            backOffice.BO_INV_differential            bid
              INNER JOIN backOffice.BO_INV_inventaire bii ON bid.inventory_id = bii.id
          WHERE bii.id = :inventory_id
            AND bid.expected_quantity != bid.counted_quantity
            AND bid.is_validated_manually = 1
        UNION
        SELECT
          'NEEDS_REVIEW' AS label,
          COUNT(bid.id)  AS total
          FROM
            backOffice.BO_INV_differential            bid
              INNER JOIN backOffice.BO_INV_inventaire bii ON bid.inventory_id = bii.id
          WHERE bii.id = :inventory_id
            AND bid.counted_quantity > 0
            AND bid.expected_quantity != bid.counted_quantity
            AND bid.is_validated_manually IS FALSE
            AND bid.is_validated_automatically IS FALSE;
        SQL;

        return $this->legacy_readonly_pdo->fetchAll($sql, ['inventory_id' => $inventory_id]);
    }

    public function findAllInfoOnArticleCollectPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                bica.id                             AS article_collect_id,
                bica.id_produit                     AS product_id,
                bica.id_emplacement                 AS location_id,
                bica.sf_guard_user_id               AS user_id,
                sgu.username                        AS username,
                bica.quantite                       AS quantity,
                bic.id                              AS collect_id,
                bic.numero                          AS collect_number,
                bic.collecte_type                   AS collect_type,
                bii.collecte_active_id              AS active_collect_id,
                bii.id                              AS inventory_id,
                bii.inv_date_validation IS NOT NULL AS is_validated,
                bii.inv_date_closed     IS NOT NULL AS is_closed,
                bid.is_validated_manually IS TRUE   AS is_locked
                FROM
                  backOffice.BO_INV_collecte_article          bica
                    INNER JOIN backOffice.BO_INV_collecte     bic ON bica.BO_INV_collecte_id = bic.id
                    INNER JOIN backOffice.BO_INV_inventaire   bii ON bic.BO_INV_inventaire_id = bii.id
                    INNER JOIN backOffice.BO_INV_differential bid ON bii.id = bid.inventory_id AND bica.id_produit = bid.product_id
                    LEFT JOIN backOffice.sf_guard_user       sgu ON bica.sf_guard_user_id = sgu.id
              WHERE {conditions}
              ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_readonly_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    public function getValidatingStatuses(int $inventory_id): array
    {
        $sql = <<<SQL
        SELECT
          COUNT(bid.status_closed) AS total,
          'ALL'                    AS label
          FROM backOffice.BO_INV_differential bid
          WHERE bid.inventory_id = :inventory_id
        UNION
        SELECT
          COUNT(bid.status_closed) AS total,
          'DONE'                   AS label
          FROM backOffice.BO_INV_differential bid
          WHERE bid.inventory_id = :inventory_id
            AND bid.status_closed = 'done'
        UNION
        SELECT
          COUNT(bid.status_closed) AS total,
          'ERROR'                  AS label
          FROM backOffice.BO_INV_differential bid
          WHERE bid.inventory_id = :inventory_id
            AND bid.status_closed = 'error'
        SQL;

        return $this->legacy_readonly_pdo->fetchAll($sql, ['inventory_id' => $inventory_id]);
    }

    public function findInventoriesOverlappingPartialInventory(int $inventory_id): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
            SELECT
                bii.id          AS inventory_id,
                bii.name        AS name,
                bii.type        AS type,
                bii.inv_date    AS created_at,
                bii.statut      AS status
            FROM backOffice.BO_INV_inventaire bii
            LEFT JOIN backOffice.BO_INV_inventory_location biil ON biil.inventory_id = bii.id
            WHERE bii.statut NOT IN ('created', 'closed')
            AND (
                biil.location_id IN (
                    SELECT _bizl.location_id
                    FROM backOffice.BO_INV_zone_inventory _bizi
                        INNER JOIN backOffice.BO_INV_zone_location _bizl ON _bizl.zone_id = _bizi.zone_id
                    WHERE _bizi.inventory_id = :inventory_id
                )
                OR (
                    bii.type = 'full'
                    AND bii.id_depot = (SELECT id_depot FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id)
                )
            )
            AND bii.id != :inventory_id
            GROUP BY bii.id
            ORDER BY created_at DESC
            ) tmp
        SQL;

        return $this->legacy_readonly_pdo->paginateArray(1, 20, $sql, ['inventory_id' => $inventory_id]);
    }

    public function findInventoriesOverlappingProductInventory(int $inventory_id): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
            SELECT
                bii.id          AS inventory_id,
                bii.name        AS name,
                bii.type        AS type,
                bii.inv_date    AS created_at,
                bii.statut      AS status
            FROM backOffice.BO_INV_inventaire bii
                LEFT JOIN backOffice.BO_INV_inventory_location biil ON biil.inventory_id = bii.id
                -- No LATERAL in Mysql5 so we have to use subqueries for 'id_depot'
                LEFT JOIN (
                    -- find location containing products that have not been counted this year
                    SELECT bizl.location_id
                    FROM backOffice.BO_INV_zone_inventory bizi
                        INNER JOIN backOffice.BO_INV_zone_location bizl ON bizi.zone_id = bizl.zone_id
                        INNER JOIN backOffice.WMS_location wl ON bizl.location_id = wl.location_id
                        LEFT JOIN backOffice.WMS_product_location wpl ON wl.location_id = wpl.location_id
                        LEFT JOIN backOffice.article a ON wpl.product_id = a.id_produit
                        LEFT JOIN backOffice.BO_INV_differential bid ON a.id_produit = bid.product_id
                        LEFT JOIN backOffice.BO_INV_inventaire _bii ON
                            bid.inventory_id = _bii.id
                            AND _bii.id_depot = (SELECT id_depot FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id)
                    WHERE
                        backOffice.PDT_ART_qte_stock_depot(
                                a.id_produit,
                                (SELECT id_depot FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id)
                        ) > 0
                        AND a.compose = 0
                        AND a.is_auto_picked = 0
                        AND wpl.quantity > 0
                        AND bizi.inventory_id = :inventory_id
                        AND wl.is_active = 1
                    GROUP BY wl.location_id
                    HAVING MAX(_bii.inv_date_closed) < YEAR(NOW()) OR MAX(_bii.id) IS NULL
                ) inv_location ON biil.location_id = inv_location.location_id
            WHERE
                bii.statut NOT IN ('created', 'closed')
                AND (
                    inv_location.location_id IS NOT NULL
                    OR (
                        bii.type = 'full'
                        AND bii.id_depot = (SELECT id_depot FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id)
                    )
                )
                AND bii.id != :inventory_id
            GROUP BY bii.id
            ORDER BY created_at DESC
            ) tmp
        SQL;

        return $this->legacy_readonly_pdo->paginateArray(1, 20, $sql, ['inventory_id' => $inventory_id]);
    }

    /** @throws SqlErrorMessageException */
    public function getCreatedProductInventoryLocationCount(int $inventory_id): int
    {
        $sql = <<<SQL
        SELECT COUNT(*)
        FROM (
            SELECT 1
                FROM backOffice.BO_INV_zone_inventory bizi
                INNER JOIN backOffice.BO_INV_zone_location bizl ON bizi.zone_id = bizl.zone_id
                INNER JOIN backOffice.WMS_location wl ON bizl.location_id = wl.location_id
                LEFT JOIN backOffice.WMS_product_location wpl ON wl.location_id = wpl.location_id
                LEFT JOIN backOffice.article a ON wpl.product_id = a.id_produit
                LEFT JOIN backOffice.BO_INV_differential bid ON a.id_produit = bid.product_id
                LEFT JOIN backOffice.BO_INV_inventaire _bii ON
                    bid.inventory_id = _bii.id
                    AND _bii.id_depot = (SELECT id_depot FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id)
            WHERE
                backOffice.PDT_ART_qte_stock_depot(
                        a.id_produit,
                        (SELECT id_depot FROM backOffice.BO_INV_inventaire WHERE id = :inventory_id)
                ) > 0
                AND a.compose = 0
                AND a.is_auto_picked = 0
                AND wpl.quantity > 0
                AND bizi.inventory_id = :inventory_id
                AND wl.is_active = 1
            GROUP BY wl.location_id
            HAVING MAX(_bii.inv_date_closed) < YEAR(NOW()) OR MAX(_bii.id) IS NULL
        ) t
        SQL;

        return (int) $this->legacy_readonly_pdo->fetchValue($sql, ['inventory_id' => $inventory_id]);
    }

    public function findAllProductsWithCount(int $inventory_id): array
    {
        $sql = <<<SQL
            SELECT
                cts.souscategorie                       AS subcategory,
                m.marque                                AS brand,
                a.modele                                AS model,
                p.reference                             AS sku,
                tmp.expected_total                      AS expected_total,
                tmp.counted_total                       AS counted_total,
                tmp.counted_total - tmp.expected_total  AS delta_total,
                biz.name                                AS zone,
                wl.label                                AS location,
                tmp.expected                            AS expected,
                tmp.counted                             AS counted,
                tmp.counted - tmp.expected              AS delta,
                tmp.buy_price                           AS buy_price
            FROM
                (
                    -- Products counted during this inventory
                    SELECT
                        bica.id_emplacement         AS location_id,
                        bid.product_id              AS product_id,
                        bid.expected_quantity       AS expected_total,
                        bid.counted_quantity        AS counted_total,
                        bid.buy_price               AS buy_price,
                        COALESCE(biis.quantity,'0') AS expected,
                        COALESCE(bica.quantite,'0') AS counted
                    FROM
                        backOffice.BO_INV_differential bid
                        INNER JOIN backOffice.BO_INV_collecte bic ON bid.inventory_id = bic.BO_INV_inventaire_id
                                                                  AND bic.numero=1
                        INNER JOIN backOffice.BO_INV_collecte_article bica ON bic.id = bica.BO_INV_collecte_id
                                                                           AND bid.product_id = bica.id_produit
                        LEFT JOIN backOffice.BO_INV_initial_state biis ON bid.inventory_id = biis.inventory_id
                                                                       AND bica.id_emplacement = biis.location_id
                                                                       AND bid.product_id = biis.product_id
                    WHERE bid.inventory_id = :inventory_id
                    UNION
                    -- Products present at the activation of this inventory
                    SELECT
                        biis.location_id            AS location_id,
                        bid.product_id              AS product_id,
                        bid.expected_quantity       AS expected_total,
                        bid.counted_quantity        AS counted_total,
                        bid.buy_price               AS buy_price,
                        COALESCE(biis.quantity,'0') AS expected,
                        COALESCE(bica.quantite,'0') AS counted

                    FROM
                        backOffice.BO_INV_differential bid
                        INNER JOIN backOffice.BO_INV_collecte bic ON bid.inventory_id = bic.BO_INV_inventaire_id
                                                                  AND bic.numero=1
                        INNER JOIN backOffice.BO_INV_initial_state biis ON bid.inventory_id = biis.inventory_id
                                                                        AND biis.product_id = bid.product_id
                        LEFT JOIN backOffice.BO_INV_collecte_article bica ON bic.id = bica.BO_INV_collecte_id
                                                                          AND biis.location_id = bica.id_emplacement
                                                                          AND bid.product_id = bica.id_produit
                    WHERE bid.inventory_id = :inventory_id
                ) tmp
                INNER JOIN backOffice.WMS_location wl ON tmp.location_id = wl.location_id
                LEFT JOIN backOffice.BO_INV_zone_location bizl ON wl.location_id = bizl.location_id
                LEFT JOIN backOffice.BO_INV_zone biz ON bizl.zone_id = biz.zone_id
                INNER JOIN backOffice.produit p ON tmp.product_id = p.id_produit
                INNER JOIN backOffice.article a ON p.id_produit = a.id_produit
                LEFT JOIN backOffice.CTG_TXN_souscategorie cts ON p.id_souscategorie = cts.id
                LEFT JOIN backOffice.marque m ON a.id_marque = m.id_marque
            ORDER BY p.reference, wl.code
        SQL;

        return $this->legacy_readonly_pdo->fetchAll($sql, ['inventory_id' => $inventory_id]);
    }

    public function findEmptyLocationInfo(int $inventory_id): array
    {
        $sql = <<<SQL
            SELECT
                biz.name AS zone,
                wl.label AS label
            FROM backOffice.WMS_location wl
                INNER JOIN backOffice.WMS_area wa ON wl.area_id = wa.area_id
                INNER JOIN backOffice.BO_INV_inventaire bii ON wa.warehouse_id = bii.id_depot
                                                            AND bii.id = :inventory_id
                LEFT JOIN backOffice.BO_INV_inventory_location biil ON bii.id = biil.inventory_id
                                                                    AND wl.location_id = biil.location_id
                LEFT JOIN backOffice.BO_INV_zone_location bizl ON wl.location_id = bizl.location_id
                LEFT JOIN backOffice.BO_INV_zone biz ON bizl.zone_id = biz.zone_id
            WHERE
                wl.location_id NOT IN (
                    -- Locations not empty when this inventory has been activated
                    SELECT
                        biis.location_id
                    FROM backOffice.BO_INV_initial_state biis
                    WHERE biis.inventory_id = :inventory_id AND biis.quantity > 0
                    UNION
                    -- Locations not empty when this inventory has been validated
                    SELECT
                        bica.id_emplacement
                    FROM backOffice.BO_INV_collecte bic
                        INNER JOIN backOffice.BO_INV_collecte_article bica ON bic.id = bica.BO_INV_collecte_id
                    WHERE bic.BO_INV_inventaire_id = :inventory_id AND bic.numero=1 AND bica.quantite > 0
                )
                AND (bii.type = 'full' OR biil.location_id IS NOT NULL)
            ORDER BY wl.code
        SQL;

        return $this->legacy_readonly_pdo->fetchAll($sql, ['inventory_id' => $inventory_id]);
    }

    public function findMissingLocations(int $inventory_id): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              SELECT
                biz.name AS zone,
                wl.label AS location
              FROM backOffice.BO_INV_inventory_location biil
                INNER JOIN backOffice.BO_INV_zone_location bizl ON bizl.location_id = biil.location_id
                INNER JOIN backOffice.BO_INV_zone biz ON biz.zone_id = bizl.zone_id
                INNER JOIN backOffice.WMS_location wl ON wl.location_id = biil.location_id
              WHERE
                biil.inventory_id = :inventory_id
                AND biil.scanned_empty_at IS NULL
                AND biil.location_id NOT IN (
                  SELECT bica.id_emplacement
                  FROM backOffice.BO_INV_collecte bic
                  INNER JOIN backOffice.BO_INV_collecte_article bica ON bica.BO_INV_collecte_id = bic.id
                  WHERE bic.BO_INV_inventaire_id = biil.inventory_id
                )
              GROUP BY wl.code
            ) tmp
        SQL;

        return $this->legacy_readonly_pdo->paginateArray(1, 20, $sql, ['inventory_id' => $inventory_id]);
    }
}
