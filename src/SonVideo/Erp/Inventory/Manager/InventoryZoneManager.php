<?php

namespace SonVideo\Erp\Inventory\Manager;

use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryReadRepository;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryZoneReadRepository;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryZoneWriteRepository;

class InventoryZoneManager
{
    private InventoryZoneWriteRepository $inventory_zone_write_repository;

    private InventoryReadRepository $inventory_read_repository;

    private InventoryZoneReadRepository $inventory_zone_read_repository;

    private QueryBuilder $query_builder;

    public function __construct(
        InventoryZoneWriteRepository $inventory_zone_write_repository,
        InventoryReadRepository $inventory_read_repository,
        InventoryZoneReadRepository $inventory_zone_read_repository,
        QueryBuilder $query_builder
    ) {
        $this->inventory_zone_write_repository = $inventory_zone_write_repository;
        $this->inventory_read_repository = $inventory_read_repository;
        $this->inventory_zone_read_repository = $inventory_zone_read_repository;
        $this->query_builder = $query_builder;
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws \Exception
     */
    public function delete(int $inventory_id, int $zone_id): void
    {
        if (!$this->inventory_read_repository->exists($inventory_id)) {
            throw new NotFoundException('Inventory not found.');
        }

        if (!$this->inventory_zone_write_repository->delete($inventory_id, $zone_id)) {
            throw new NotFoundException('Zone not found.');
        }
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws \Exception
     */
    public function create(int $inventory_id, int $zone_id): void
    {
        if (!$this->inventory_read_repository->exists($inventory_id)) {
            throw new NotFoundException('Inventory not found.');
        }

        if (!$this->exists($zone_id)) {
            throw new NotFoundException('Zone not found.');
        }

        $this->inventory_zone_write_repository->create($inventory_id, $zone_id);
    }

    public function exists(int $zone_id): bool
    {
        $this->query_builder->setWhere(
            ['zone_id' => ['_eq' => $zone_id]],
            InventoryZoneReadRepository::COLUMNS_MAPPING
        );

        return [] !== $this->inventory_zone_read_repository->findAllPaginated($this->query_builder)->getResults();
    }
}
