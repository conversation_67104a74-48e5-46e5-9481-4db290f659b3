<?php

namespace SonVideo\Erp\SalesPeriod\Entity;

use App\Entity\AbstractEntity;

class ArticleSalesPeriodEntity extends AbstractEntity
{
    public int $article_id;

    public int $sale_id;

    public bool $is_active;

    public int $order_quantity;

    public int $sales_period_id;

    public int $stock_30d_quantity;

    public float $selling_price;

    public float $initial_selling_price;
}
