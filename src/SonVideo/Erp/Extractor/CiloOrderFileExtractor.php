<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Extractor;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Exception\NotFoundException;
use SonVideo\Erp\Customer\Mysql\Repository\CustomerRepository;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrder\Referential\CustomerOrderOrigin;
use SonVideo\Erp\Entity\OrderEntity;
use SonVideo\Erp\Referential\Civility;
use Symfony\Component\DomCrawler\Crawler;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

/**
 * Class CiloOrderFileExtractor.
 *
 * Used to convert/extract data from an XML Cilo order file to an order.
 */
class CiloOrderFileExtractor implements DataLoaderAwareInterface
{
    use DataLoaderAwareTrait;

    public const CILO_PROSPECT_ID = 1518400;
    public const CILO_IP = '************';
    public const CILO_SHIPMENT_METHODE_ID = 65;

    private ?float $total_price_vat_excluded = null;

    /** @var string Raw XML content */
    protected $content;

    private SerializerInterface $serializer;
    private CustomerRepository $customer_repo;

    /** CiloOrderFileExtractor constructor. */
    public function __construct(CustomerRepository $customer_repo, SerializerInterface $serializer)
    {
        $this->serializer = $serializer;
        $this->customer_repo = $customer_repo;
    }

    /** setContent */
    public function setContent(string $content): self
    {
        $this->content = $content;

        return $this;
    }

    /** getCrawler */
    protected function getCrawler(): Crawler
    {
        if ('' === trim($this->content)) {
            throw new \LogicException('Content needs to be set before trying to extract data.');
        }

        return new Crawler($this->content);
    }

    /**
     * Extract an order entity out of the content.
     *
     * @throws ExceptionInterface|NotFoundException
     */
    public function extractOrder(): CustomerOrderCreationContextDto
    {
        $crawler = $this->getCrawler();

        $products = $this->extractOrderLines($crawler);
        // need extract product for extract payement
        $payment = $this->extractPayment($crawler);

        // retrieve cilo prospect
        $cilo_customer = $this->customer_repo->getById(self::CILO_PROSPECT_ID);

        // create base entity
        $data = [
            'origin' => CustomerOrderOrigin::CILO,
            'original_customer_order_id' => $crawler->filter('CommandeFournisseur > NumCommande')->text(),
            'created_at' => $crawler->filter('CommandeFournisseur > DateCommande')->text(),
            'customer_id' => self::CILO_PROSPECT_ID,
            'ip_address' => self::CILO_IP,
            'is_excluding_tax' => true,
            'billing_address' => [
                'company_name' => $crawler->filter('CommandeFournisseur > Facturation > Societe')->text(),
                'civility' => Civility::MR,
                'firstname' => $crawler->filter('CommandeFournisseur > Facturation > Prenom')->text(),
                'lastname' => $crawler->filter('CommandeFournisseur > Facturation > Nom')->text(),
                'address' => OrderEntity::addressFromMultiline([
                    $crawler->filter('CommandeFournisseur > Facturation > Adresse1')->text(),
                    $crawler->filter('CommandeFournisseur > Facturation > Adresse2')->text(),
                    $crawler->filter('CommandeFournisseur > Facturation > Adresse3')->text(),
                ]),
                'postal_code' => $crawler->filter('CommandeFournisseur > Facturation > CodePostal')->text(),
                'city' => $crawler->filter('CommandeFournisseur > Facturation > Ville')->text(),
                'country_code' => $crawler->filter('CommandeFournisseur > Facturation > Pays')->text(),
                'cellphone' => $crawler->filter('CommandeFournisseur > Facturation > Telephone')->text(),
                'email' => trim(strtolower($crawler->filter('CommandeFournisseur > Facturation > Email')->text())),
                'vat_number' => $cilo_customer->get('cnt_numero_tva'),
            ],
            'shipping_address' => [
                'company_name' => $crawler->filter('CommandeFournisseur > Livraison > Societe')->text(),
                'civility' => Civility::MR,
                'firstname' => $crawler->filter('CommandeFournisseur > Livraison > Prenom')->text(),
                'lastname' => $crawler->filter('CommandeFournisseur > Livraison > Nom')->text(),
                'address' => OrderEntity::addressFromMultiline([
                    $crawler->filter('CommandeFournisseur > Livraison > Adresse1')->text(),
                    $crawler->filter('CommandeFournisseur > Livraison > Adresse2')->text(),
                    $crawler->filter('CommandeFournisseur > Livraison > Adresse3')->text(),
                ]),
                'postal_code' => $crawler->filter('CommandeFournisseur > Livraison > CodePostal')->text(),
                'city' => $crawler->filter('CommandeFournisseur > Livraison > Ville')->text(),
                'country_code' => $crawler->filter('CommandeFournisseur > Livraison > Pays')->text(),
                // cilo puts the cell phone in the phone
                'cellphone' => '' !== $crawler->filter('CommandeFournisseur > Livraison > Portable')->text()
                        ? $crawler->filter('CommandeFournisseur > Livraison > Portable')->text()
                        : $crawler->filter('CommandeFournisseur > Livraison > Telephone')->text(),
                'email' => trim(strtolower($crawler->filter('CommandeFournisseur > Livraison > Email')->text())),
            ],
            'shipment_method' => [
                'cost' => 0.0,
                'shipment_method_id' => self::CILO_SHIPMENT_METHODE_ID,
            ],
            'payments' => [$payment],
            'products' => $products,
        ];

        /* @var CustomerOrderCreationContextDto */
        return $this->serializer->denormalize($data, CustomerOrderCreationContextDto::class);
    }

    /** Return the order lines (products) */
    protected function extractOrderLines(Crawler $crawler): array
    {
        $this->total_price_vat_excluded = 0;

        return $crawler
            ->filter('CommandeFournisseur > Commande > Produit')
            ->each(function (Crawler $product_crawler): array {
                // check type
                $type = $product_crawler->filter('Produit > Type')->text();
                if ('article' !== strtolower($type)) {
                    throw new \InvalidArgumentException(sprintf('Product with type "%s" not handled.', $type));
                }

                // extract product price data
                [$price, $currency, $price_type, $vat] = $product_crawler
                    ->filter('Produit > PrixUnitaire')
                    ->extract(['_text', 'devise', 'type', 'tva'])[0];
                if ('EUR' !== $currency) {
                    throw new \InvalidArgumentException(sprintf('Price with currency "%s" not handled.', $currency));
                }

                $vat /= 100;
                $price_vat = 'HT' === $price_type ? $price * (1 + $vat) : $price;
                $this->total_price_vat_excluded +=
                    ($product_crawler->filter('Produit > Quantite')->text() * $price_vat) / (1 + $vat);

                return [
                    'sku' => $product_crawler->filter('Produit > ReferenceMarchand')->text(),
                    'description' => $product_crawler->filter('Produit > Designation')->text(),
                    'ecotax_price' => 0,
                    'sorecop_price' => 0,
                    'quantity' => $product_crawler->filter('Produit > Quantite')->text(),
                    'selling_price_tax_included' => $price_vat,
                ];
            });
    }

    /**
     * Extract the payment data out of the order.
     * Amount is extracted from the sum of products, without taxes.
     *
     * @return array{payment_mean: string, created_at: mixed, amount: float}
     */
    protected function extractPayment(Crawler $crawler): array
    {
        return [
            'payment_mean' => 'CILO',
            'created_at' => $crawler->filter('CommandeFournisseur > DateCommande')->text(),
            'amount' => $this->total_price_vat_excluded,
        ];
    }
}
