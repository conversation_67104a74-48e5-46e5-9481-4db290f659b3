<?php

namespace SonVideo\Erp\DeliveryNote\Manager;

use App\Sql\CollectionResultSummary;
use SonVideo\Erp\DeliveryNote\Mysql\Repository\DeliveryNoteRepository;

/**
 * Class Generate.
 */
class DeliveryNoteGenerator
{
    private DeliveryNoteRepository $delivery_note_repository;

    public function __construct(DeliveryNoteRepository $delivery_note_repository)
    {
        $this->delivery_note_repository = $delivery_note_repository;
    }

    /** @return array|array[] */
    public function generate(int $customer_order_id, int $carrier_id, string $user, int $shipment_method_id): array
    {
        if (!in_array($carrier_id, [1, 49])) {
            return (new CollectionResultSummary())->create(
                $this->delivery_note_repository->generateDeliveryNote(
                    $customer_order_id,
                    $carrier_id,
                    $user,
                    $shipment_method_id
                )
            );
        }

        return [
            'error' => ['Impossible de générer un BL pour ce transporteur'],
            'success' => [],
        ];
    }
}
