<?php

namespace SonVideo\Erp\Stock\Mysql\Repository\Pager;

use App\Database\ConnectionProvider\AbstractMysqlErpPagerProvider;
use App\Sql\Helper\Pager;
use SonVideo\Erp\Stock\Entity\SafetyStockRecommendation;
use SonVideo\Orm\Entity\PaginatedCollectionRequest;

final class PaginatedSafetyStockRecommendationRepository extends AbstractMysqlErpPagerProvider
{
    public function findAllPaginated(PaginatedCollectionRequest $paginated_collection_request): Pager
    {
        $subquery = <<<'MYSQL'
        SELECT
            scp.id_produit AS article_id,
            p.reference AS sku,
            COALESCE(ast.deliverable_stock, 0) AS deliverable_stock,
            bspd.stock_securite AS original_safety_stock,
            COALESCE(ssa.days_until_out_of_stock, 0) AS days_until_out_of_stock,
            COALESCE(ssa.estimated_out_of_stock_date, 0) AS estimated_out_of_stock_date,
            COALESCE(ssa.product_weighted_avg_replenishment_lead_time, 0) AS product_weighted_avg_replenishment_lead_time,
            COALESCE(ssa.product_min_replenishment_lead_time, 0) AS product_min_replenishment_lead_time,
            COALESCE(ssa.product_max_replenishment_lead_time, 0) AS product_max_replenishment_lead_time,
            ssa.average_daily_quantity,
            CASE
                WHEN bspd.stock_securite = 0 THEN 0
                ELSE ROUND(scsr.z_score * COALESCE(scp.weighted_std_deviation, 0))
             END AS recommended_safety_stock_quantity,
            CASE
                WHEN bspd.stock_securite = 0 THEN 0
                ELSE bspd.stock_securite - ROUND(scsr.z_score * COALESCE(scp.weighted_std_deviation, 0))
            END AS difference,
            a.id_fournisseur AS supplier_id,
            f.fournisseur AS supplier_name,
            p.id_souscategorie AS subcategory_id,
            sc.souscategorie AS subcategory_name,
            a.id_marque AS brand_id,
            m.marque AS brand_name,
            a.mininum_order_quantity AS minimum_order_quantity,
            scp.combined_class AS product_class
            FROM backOffice.stock_classification_product scp
              INNER JOIN backOffice.article a ON a.id_produit = scp.id_produit
              INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
              INNER JOIN backOffice.stock_classification_matrix scm ON scp.combined_class = scm.name
              INNER JOIN backOffice.stock_classification_service_rate scsr ON scm.service_rate = scsr.service_rate
              LEFT JOIN backOffice.fournisseur f ON f.id_fournisseur = a.id_fournisseur
              LEFT JOIN backOffice.CTG_TXN_souscategorie sc ON sc.id = p.id_souscategorie
              LEFT JOIN backOffice.marque m ON m.id_marque = a.id_marque
              LEFT JOIN backOffice.article_stock ast ON a.id_produit = ast.product_id AND ast.deliverable_stock >= 0
              LEFT JOIN backOffice.stock_safety_analysis ssa ON ssa.id_produit = scp.id_produit
              LEFT JOIN backOffice.BO_STK_produit_depot bspd ON a.id_produit = bspd.id_produit AND bspd.id_depot = 21
              LEFT JOIN backOffice.CTG_TXN_categorie ctc ON p.V_id_categorie = ctc.id_categorie
        WHERE {conditions}
            AND scp.weighted_std_deviation IS NOT NULL
            AND bspd.stock_securite != 0
            AND bspd.stock_securite - ROUND(scsr.z_score * COALESCE(scp.weighted_std_deviation, 0)) != 0
            AND a.status != 'last'
            AND ctc.id_categorie NOT IN (17, 18)
        MYSQL;

        return $this->paginateWithModel(
            self::wrapSubQuery($subquery),
            $paginated_collection_request,
            PaginatedSafetyStockRecommendationModel::class,
            SafetyStockRecommendation::class
        );
    }
}
