<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Client;

use App\Client\AbstractCurlClient;
use App\Contract\ErpV1ApiClientInterface;

/**
 * Class Erpv1ApiClient.
 */
class Erpv1ApiClient extends AbstractCurlClient implements ErpV1ApiClientInterface
{
    /** Erpv1ApiClient constructor. */
    public function __construct(string $erpv1_api_end_point)
    {
        $this->setBaseUrl($erpv1_api_end_point);
    }

    /** {@inheritDoc} */
    public function callWithToken($method, string $token, array $params, ?callable $check_http_code = null)
    {
        $options = [
            'post' => 1,
            'postfields' => json_encode($params, JSON_THROW_ON_ERROR),
            'returntransfer' => true,
            'httpheader' => [
                'Accept: application/json',
                'Content-Type: application/json',
                sprintf('Content-Length: %s', strlen(json_encode($params, JSON_THROW_ON_ERROR))),
                sprintf('Authorization: Bearer %s', $token),
            ],
        ];

        return $this->send($method, $options, $check_http_code);
    }

    /**
     * ping.
     *
     * @throws \Exception
     */
    public function ping(): bool
    {
        $timeout = 10;

        $options = [
            'fresh_connect' => true,
            'timeout' => $timeout,
            'connecttimeout' => $timeout,
            'followlocation' => true,
            'returntransfer' => true,
        ];

        return $this->send('', $options, function (int $http_code): void {
            if (200 !== $http_code) {
                throw new \Exception('Erp V1 API is not available');
            }
        });
    }
}
