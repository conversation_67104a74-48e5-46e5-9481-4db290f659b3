<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Client;

use App\Client\AbstractCurlClient;

/**
 * Class EasyLoungeClient.
 */
class EasyLoungeApiClient extends AbstractCurlClient
{
    private string $ezl_api_key;

    public function __construct(string $ezl_api_end_point, string $ezl_api_key)
    {
        $this->setBaseUrl($ezl_api_end_point);
        $this->ezl_api_key = $ezl_api_key;
    }

    public function get(string $endpoint)
    {
        $options = [
            'returntransfer' => true,
            'httpheader' => [
                'Accept: application/json',
                'Content-Type: application/json',
                sprintf('apiKey: %s', $this->ezl_api_key),
            ],
            // capcaisse returns 401 when HTTP/2 is used
            'http_version' => CURL_HTTP_VERSION_1_1,
        ];

        return $this->send($endpoint, $options);
    }

    /**
     * @param $endpoint
     * @param array|object $fields
     *
     * @return mixed
     *
     * @throws \Exception
     */
    public function post(string $endpoint, $fields)
    {
        $encoded_fields = json_encode($fields, JSON_THROW_ON_ERROR);
        $options = [
            'post' => 1,
            'postfields' => $encoded_fields,
            'returntransfer' => true,
            'httpheader' => [
                'Accept: application/json',
                'Content-Type: application/json',
                sprintf('apiKey: %s', $this->ezl_api_key),
                sprintf('Content-Length: %s', strlen($encoded_fields)),
            ],
        ];

        return $this->send($endpoint, $options);
    }
}
