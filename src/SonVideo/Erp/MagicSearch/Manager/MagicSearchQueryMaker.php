<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\MagicSearch\Manager;

use Elasticsearch\Client as ElasticsearchClient;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\MagicSearch\Contract\MagicSearchQueryProviderInterface;
use SonVideo\Erp\MagicSearch\Entity\MagicSearchHttpRequestPayload;

final class MagicSearchQueryMaker
{
    private ElasticsearchClient $client;

    private LoggerInterface $logger;

    public function __construct(ElasticsearchClient $client, LoggerInterface $logger)
    {
        $this->client = $client;
        $this->logger = $logger;
    }

    /** @param MagicSearchQueryProviderInterface[] $context_providers */
    public function make(MagicSearchHttpRequestPayload $request_payload, array $context_providers): array
    {
        $response = [];

        $this->logger->debug('PARAMS', ['params' => (array) $request_payload]);

        foreach ($context_providers as $context_provider) {
            $this->logger->debug('CONFIG', [
                'context' => $context_provider->getContext(),
                'index' => $context_provider->getIndexName(),
            ]);

            $body = $context_provider->getBody($request_payload);

            $this->logger->debug('BODY', [json_encode($body->toArray(), JSON_THROW_ON_ERROR)]);

            $results = $this->client->search([
                'size' => $request_payload->size,
                'from' => $request_payload->from,
                'index' => $context_provider->getIndexName(),
                'body' => $body->toArray(),
            ]);

            // mostly for tests which returns nothing usable
            if (!isset($results['hits'])) {
                continue;
            }

            $response[$context_provider->getContext()] = $context_provider->decorateResults($results);
        }

        return $response;
    }
}
