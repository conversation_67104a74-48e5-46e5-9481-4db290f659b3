<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\MagicSearch\Entity;

use App\DataLoader\Type\JsonType;

class ArticleMagicSearchEntity
{
    public int $article_id;

    public string $sku;

    public string $name;

    public string $computed_name;

    public string $short_description;

    public string $basket_description;

    public ?string $reference_date = null;

    public string $status;

    public string $type;

    /** @var array|JsonType */
    public array $brand;

    public ?string $group_brand = null;

    /** @var array|JsonType|null */
    public ?array $category = null;

    /** @var array|JsonType|null */
    public ?array $subcategory = null;

    public ?string $article_url = null;

    public ?string $image = null;

    /** @var array|JsonType|null */
    public ?array $barcodes = null;

    /** @var array|JsonType|null */
    public ?array $prices = null;

    public ?string $unbasketable_reason = null;

    public int $stock;

    public ?int $delay = null;

    /** @var array|JsonType|null */
    public ?array $stock_details = null;

    /** @var array|JsonType|null */
    public ?array $related_products = null;
}
