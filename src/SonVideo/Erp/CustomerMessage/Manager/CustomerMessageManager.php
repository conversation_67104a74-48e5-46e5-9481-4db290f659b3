<?php

namespace SonVideo\Erp\CustomerMessage\Manager;

use App\Formatter\String\StringFormatter;
use SonVideo\Erp\CustomerMessage\Mysql\Repository\CustomerMessageRepository;
use SonVideo\Erp\Mailing\Manager\Contact\ContactEmailDispatcher;
use SonVideo\Erp\User\Entity\UserEntity;

class CustomerMessageManager
{
    private CustomerMessageRepository $customer_message_repository;

    private ContactEmailDispatcher $contact_email_dispatcher;

    public function __construct(
        CustomerMessageRepository $customer_message_repository,
        ContactEmailDispatcher $contact_email_dispatcher
    ) {
        $this->customer_message_repository = $customer_message_repository;
        $this->contact_email_dispatcher = $contact_email_dispatcher;
    }

    public function reply(int $message_id_to_reply, UserEntity $user, string $message): void
    {
        $message = StringFormatter::transliterate($message);

        if ('' === trim($message)) {
            throw new \InvalidArgumentException('Message is required');
        }

        $message .= "\n{$user->get('signature')}";

        $reply_id = $this->customer_message_repository->addReply($message_id_to_reply, $user, $message);

        $reply = $this->customer_message_repository->fetchCustomerMessage($reply_id);

        $this->contact_email_dispatcher->dispatch([
            'to' => $reply['recipient'],
            'from' => ['email' => $user->get('email'), 'name' => "{$user->get('prenom')} {$user->get('nom')}"],
            'context' => ['subject' => $reply['subject'], 'content' => nl2br($reply['message'])],
            '_rel' => ['customer' => (int) $reply['customer_id']],
            '_sent_by' => $user->get('id_utilisateur'),
        ]);
    }
}
