<?php

namespace SonVideo\Erp\Partner\Mysql\Repository;

use App\Sql\AbstractLegacyRepository;

/**
 * Class CustomerOrderRepository.
 */
class CustomerOrderRepository extends AbstractLegacyRepository
{
    public function getConsolidated(array $customer_order_ids): array
    {
        $sql = <<<SQL
        SELECT
          c.id_commande,
          CASE
              -- 289781 -> CONNECTING TECHNOLOGY
            WHEN p.id_prospect = 289781 OR p.blacklist = true
              THEN 'Annulée'
            WHEN c.V_statut_traitement = 'lvr_attente' OR c.V_statut_traitement = 'lvr_attente,facture' OR c.V_statut_traitement = 'trcn_remise' OR c.V_statut_traitement = 'trcn_remise,facture'
              THEN 'Validée'
            WHEN c.V_statut_traitement = 'trcn_acceptation' OR c.V_statut_traitement = 'trcn_anomalie' OR c.V_statut_traitement = 'trcn_acceptation,trcn_anomalie'
              THEN 'En attente'
            WHEN c.flux = 'cloture'
              THEN 'Validée'
            WHEN c.flux = 'annulation'
              THEN 'Annulée'
            ELSE 'En cours de traitement'
            END                   AS statut_commande,
          c.V_statut_traitement AS statut_commande_detail,
          c.creation_origine,
          c.clef
          FROM backOffice.commande c
            LEFT JOIN backOffice.prospect p ON c.id_prospect = p.id_prospect
          WHERE c.id_commande IN (:customer_order_ids)
          ORDER BY statut_commande, id_commande;
        SQL;

        return $this->legacy_pdo->fetchAll($sql, ['customer_order_ids' => $customer_order_ids]);
    }
}
