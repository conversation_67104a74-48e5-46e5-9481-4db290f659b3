<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Article\Entity;

use App\DataLoader\Type\JsonType;
use App\Entity\AbstractEntity;

final class ArticleHistoryEntity extends AbstractEntity
{
    public string $type;

    public int $ref_id;

    public int $article_id;

    public string $created_at;

    /** @var array|JsonType */
    public array $created_by;

    /** @var array|JsonType */
    public array $payload;
}
