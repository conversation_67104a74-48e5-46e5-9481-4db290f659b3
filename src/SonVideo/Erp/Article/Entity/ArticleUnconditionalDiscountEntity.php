<?php

namespace SonVideo\Erp\Article\Entity;

use App\Entity\AbstractEntity;

final class ArticleUnconditionalDiscountEntity extends AbstractEntity
{
    public int $article_id;

    public ?float $amount;

    public string $origin;

    public function __construct(int $article_id, ?float $amount, string $origin)
    {
        $this->article_id = $article_id;
        $this->amount = $amount;
        $this->origin = $origin;
    }
}
