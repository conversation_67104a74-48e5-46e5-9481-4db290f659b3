<?php

namespace SonVideo\Erp\Article\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Article\Dto\CreationContext\ArticleCommentCreationContextDto;
use SonVideo\Erp\Article\Entity\ArticleCommentEntity;

final class ArticleCommentRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public const COLUMNS_MAPPING = [
        'article_comment_id' => 'id',
        'article_id' => 'id_produit',
        'created_at' => 'date_commentaire',
        'message' => 'commentaire',
        'type' => 'type',
        'created_by' => 'user_id',
        'is_active' => 'is_active',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
            (
                {base_sql}
                WHERE {conditions}
                ) tmp
            {order_by}
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
            '{conditions}' => $query_builder->setColumnsMapping(self::COLUMNS_MAPPING)->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateEntity(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters(),
            ArticleCommentEntity::class
        );
    }

    public function findOneById($article_comment_id): ?ArticleCommentEntity
    {
        $sql = <<<SQL
            {base_sql}
            WHERE id = :article_comment_id
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
        ]);

        return $this->legacy_pdo->fetchOneEntity(
            $sql,
            ['article_comment_id' => $article_comment_id],
            ArticleCommentEntity::class
        );
    }

    private function getBaseSql(): string
    {
        return <<<SQL
            SELECT id AS article_comment_id,
                       id_produit AS article_id,
                       commentaire AS message,
                       date_commentaire AS created_at,
                       JSON_OBJECT(
                            'id', user_id,
                            'fullname', backOffice.GET_COMPUTED_USER_NAME_BY_ID(user_id)
                       ) AS created_by,
                       is_active,
                       type
                FROM backOffice.BO_PDT_ART_commentaire
        SQL;
    }

    public function update(int $article_comment_id, array $data): int
    {
        $statement = array_map(
            fn ($value): string => sprintf('%s = :%s', self::COLUMNS_MAPPING[$value], $value),
            array_keys($data)
        );

        $sql = strtr('UPDATE backOffice.BO_PDT_ART_commentaire SET {statement} WHERE id = :article_comment_id', [
            '{statement}' => "\n" . implode(", \n", $statement) . "\n",
        ]);

        return $this->legacy_pdo->fetchAffected(
            $sql,
            array_merge($data, ['article_comment_id' => $article_comment_id])
        );
    }

    public function create(ArticleCommentCreationContextDto $dto): int
    {
        $sql = <<<SQL
            INSERT backOffice.BO_PDT_ART_commentaire
            SET
                id_produit = :article_id,
                user_id = :created_by,
                date_commentaire = NOW(),
                commentaire = :message,
                type = :type
        SQL;

        $this->legacy_pdo->fetchAffected($sql, [
            'article_id' => $dto->article_id,
            'message' => $dto->message,
            'created_by' => $dto->created_by,
            'type' => $dto->type,
        ]);

        $last_id = $this->legacy_pdo->lastInsertId();

        if (!is_string($last_id)) {
            throw new \Exception('Unexpected comment inserted id');
        }

        return $last_id;
    }
}
