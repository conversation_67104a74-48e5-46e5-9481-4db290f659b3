<?php

namespace SonVideo\Erp\Article\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use Doctrine\DBAL\Exception;
use SonVideo\Erp\Article\Dto\ArticleUnconditionalDiscountDto;
use SonVideo\Erp\Article\Entity\ArticleUnconditionalDiscountEntity;

class ArticleUnconditionalDiscountRepository implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public const COLUMNS_MAPPING = [
        'article_id' => 'a.id_produit',
        'amount' => 'amount',
    ];

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
            (
                SELECT
                    a.id_produit as article_id,
                    IF(aud.amount IS NOT NULL, 'article',
                    IF(sc.supplier_contract_id IS NOT NULL, 'supplier_contract', 'article')) AS origin,
                    COALESCE(aud.amount, sc2.unconditional_discount, sc.unconditional_discount, 0) AS amount
                FROM backOffice.article a
                    LEFT JOIN backOffice.supplier_contract sc
                       ON sc.supplier_id = a.id_fournisseur
                           AND (sc.brand_id IS NULL)
                           AND sc.year = YEAR(CURDATE())
                    LEFT JOIN backOffice.supplier_contract sc2
                              ON sc2.supplier_id = a.id_fournisseur
                                  AND sc2.brand_id = a.id_marque
                                  AND sc2.year = YEAR(CURDATE())
                    LEFT JOIN backOffice.article_unconditional_discount aud
                              ON aud.article_id = a.id_produit
                WHERE {conditions}
                ) tmp
            {order_by}
        SQL;

        $sql = strtr($sql, [
            '{conditions}' => $query_builder->setColumnsMapping(static::COLUMNS_MAPPING)->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateEntity(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters(),
            ArticleUnconditionalDiscountEntity::class
        );
    }

    public function findOneById(int $article_id): ?ArticleUnconditionalDiscountEntity
    {
        $sql = <<<SQL
            SELECT
                article_id,
                COALESCE(amount, 0) as amount,
                'article' as origin
            FROM backOffice.article_unconditional_discount
            WHERE article_id = :article_id
        SQL;

        $default = new ArticleUnconditionalDiscountEntity($article_id, 0.0, 'article');

        $article_unconditional_discount = $this->legacy_pdo->fetchOneEntity(
            $sql,
            ['article_id' => $article_id],
            ArticleUnconditionalDiscountEntity::class
        );

        return $article_unconditional_discount ?? $default;
    }

    public function update(ArticleUnconditionalDiscountDto $dto): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.article_unconditional_discount
        VALUES (:article_id, :amount)
            ON DUPLICATE KEY UPDATE amount = :amount
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, [
            'amount' => $dto->amount,
            'article_id' => $dto->article_id,
        ]);
    }

    /** @throws Exception */
    public function delete(int $article_id): int
    {
        $sql = <<<'SQL'
            DELETE FROM backOffice.article_unconditional_discount
            WHERE article_id = :article_id
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, ['article_id' => $article_id]);
    }
}
