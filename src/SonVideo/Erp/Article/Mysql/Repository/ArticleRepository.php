<?php

namespace SonVideo\Erp\Article\Mysql\Repository;

use App\Exception\NotFoundException;
use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Article\Contract\ArticleUpdateRelatedRepositoryInterface;

class ArticleRepository extends AbstractLegacyRepository implements ArticleUpdateRelatedRepositoryInterface
{
    public const COLUMNS_MAPPING = [
        // global
        'article_id' => 'a.id_produit',
        'status' => 'a.status',
        'name' => 'a.modele',
        'brand_id' => 'a.id_marque',
        'stock' => 'a.V_quantite_stock',
        'short_description' => 'a.description_courte',
        'basket_description' => 'a.description_panier',
        'marketplace_description' => 'a.description_marketplace',
        'color_id' => 'a.id_couleur',
        'manufacturer_warranty_years' => 'a.garantie_constructeur',
        'packages' => 'a.vendu_par',
        'comparator' => 'a.comparateur',
        // prices
        'selling_price' => 'a.prix_vente',
        'initial_selling_price' => 'a.prix_vente_initial',
        'pvgc' => 'a.prix_vente_generalement_constate',
        'ecotax' => 'a.prix_ecotaxe',
        'sorecop' => 'a.prix_sorecop',
        'purchase_tax_excluded' => 'a.prix_achat_net',
        'reseller_price' => 'a.prix_revendeur',
        'tariff_tax_excluded' => 'a.prix_achat_tarif',
        'weighted_cost' => 'a.prix_achat_pondere',
        // buyer_informations
        'embargo_date' => 'a.date_embargo',
        // supplier
        'supplier_id' => 'a.id_fournisseur',
        'supplier_reference' => 'a.reference_fournisseur',
        'mininum_order_quantity' => 'a.mininum_order_quantity',
        // logistic_information
        'weight' => 'a.poids',
        'weight_tmp' => 'a.poids_tmp',
        'number_of_packages' => 'a.nombre_colis',
        'customs_code' => 'a.code_douanier',
        'package_unit' => 'a.conditionnement',
        'is_packaged' => 'a.vente_lot',
        'is_package' => 'a.compose = 1',
        'source_country_id' => 'a.id_pays_origine',
        'sku_havre' => 'a.reference_havre',
        'is_active_havre' => 'a.stock_havre',
        'package_unit_havre' => 'a.cdt_havre',
        'ecotax_code' => 'a.code_ecotaxe',
        'stock_location' => 'a.stock_emplacement',
        'is_on_sale' => 'backOffice.PDT_ART_est_solde(a.id_produit)',
        'sale_selling_price' => 'backOffice.PDT_ART_prix_solde_en_cours(a.id_produit)',
    ];

    /** @throws NotFoundException */
    public function getOneById(int $article_id, ?array $columns = null): array
    {
        $columns ??= array_keys(self::COLUMNS_MAPPING);

        $sql = <<<MYSQL
        SELECT {projection}
            FROM backOffice.article a
            WHERE a.id_produit = :article_id
        MYSQL;

        $projection_lines = array_map(
            static fn ($column): string => self::COLUMNS_MAPPING[$column] . ' AS ' . $column,
            $columns
        );

        $sql = strtr($sql, [
            '{projection}' => "\n" . implode(", \n", $projection_lines) . "\n",
        ]);

        $result = $this->legacy_pdo->fetchOne($sql, ['article_id' => $article_id]);
        if (false === $result) {
            throw new NotFoundException(sprintf('No article found with id %d.', $article_id));
        }

        return $result;
    }

    public function getMaxShippingDelay(array $article_ids): ?int
    {
        $sql = <<<MYSQL
        SELECT IF(COUNT(*) > COUNT(a.V_delai_lvr), NULL, MAX(a.V_delai_lvr))
            FROM backOffice.article a
            WHERE a.id_produit IN (:article_ids)
        MYSQL;

        $result = $this->legacy_pdo->fetchValue($sql, ['article_ids' => $article_ids]);
        if (false === $result || null === $result) {
            return null;
        }

        return (int) $result;
    }

    public function update(int $article_id, array $data): int
    {
        $statement = array_map(
            static fn ($column_mapping_key): string => sprintf(
                '%s = :%s',
                self::COLUMNS_MAPPING[$column_mapping_key],
                $column_mapping_key
            ),
            array_keys($data)
        );

        $sql = strtr('UPDATE backOffice.article a SET {statement} WHERE a.id_produit = :article_id', [
            '{statement}' => "\n" . implode(", \n", $statement) . "\n",
        ]);

        return $this->legacy_pdo->fetchAffected($sql, array_merge($data, ['article_id' => $article_id]));
    }
}
