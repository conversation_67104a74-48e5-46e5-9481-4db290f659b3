<?php

namespace SonVideo\Erp\Article\Dto\UpdateContext;

use App\Validator\Constraint as CustomAssert;
use SonVideo\Erp\Article\Contract\ArticleUpdateContextDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

final class ArticleLogisticInformationUpdateContextDto implements ArticleUpdateContextDtoInterface
{
    public int $article_id;

    /**
     * @Assert\PositiveOrZero
     * @CustomAssert\ArticleWeight()
     */
    public float $weight;

    public string $weight_tmp;

    public string $is_packaged;

    /**
     * @Assert\Regex(
     *     pattern="/^[0-9]{0,12}$/",
     *     message="[key:customs_code_format_error] value {{ value }} : Bad format"
     * )
     */
    public ?string $customs_code = null;

    /** @Assert\Positive */
    public int $package_unit;

    public ?int $source_country_id = null;

    /** @Assert\Positive */
    public int $number_of_packages;

    /**
     * @Assert\Regex(
     *     pattern="/^[0-9]{0,12}$/",
     *     message="[key:ecotax_code_format_error] value {{ value }} : Bad format"
     * )
     */
    public ?string $ecotax_code = null;
}
