<?php

namespace SonVideo\Erp\Article\Manager;

use App\Exception\NotFoundException;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;

class ArticleCachedDataProvider
{
    private ArticleRepository $article_read_repository;

    private array $articles = [];

    public function __construct(ArticleRepository $article_read_repository)
    {
        $this->article_read_repository = $article_read_repository;
    }

    /** @throws NotFoundException */
    public function getOneById($article_id): array
    {
        if (!isset($this->articles[$article_id])) {
            $this->articles[$article_id] = $this->article_read_repository->getOneById($article_id);
        }

        return $this->articles[$article_id];
    }
}
