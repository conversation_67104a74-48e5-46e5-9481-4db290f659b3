<?php

namespace SonVideo\Erp\Article\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\InternalErrorException;
use App\Exception\InternalServerErrorException;
use App\Exception\NotFoundException;
use App\Params\FiltersParams;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Article\Dto\CreationContext\ArticleCommentCreationContextDto;
use SonVideo\Erp\Article\Entity\ArticleCommentEntity;
use SonVideo\Erp\Article\Mysql\Repository\ArticleCommentRepository;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\User\Entity\UserEntity;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ArticleCommentManager implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    private const EVENT_SCOPE = 'comment';

    private ArticleCommentRepository $article_comment_repository;

    private ArticleRepository $article_repository;

    private ArticleEventLogger $article_event_logger;

    private SerializerInterface $serializer;

    private ValidatorInterface $validator;

    private QueryBuilder $query_builder;

    public function __construct(
        ArticleCommentRepository $article_comment_repository,
        ArticleRepository $article_repository,
        ArticleEventLogger $article_event_logger,
        SerializerInterface $serializer,
        ValidatorInterface $validator,
        QueryBuilder $query_builder
    ) {
        $this->article_comment_repository = $article_comment_repository;
        $this->article_repository = $article_repository;
        $this->article_event_logger = $article_event_logger;
        $this->serializer = $serializer;
        $this->validator = $validator;
        $this->query_builder = $query_builder;
    }

    /** Get a paginated filtered collection of article comment */
    public function getFilteredCollection(FiltersParams $params): Pager
    {
        $query_builder = $this->query_builder
            ->setWhere($params->getFilters() ?? [])
            ->setOrderBy($params->getOrderBy(), $params->getOrderDirection())
            ->setPage($params->getPage(), $params->getLimit());

        return $this->article_comment_repository->findAllPaginated($query_builder);
    }

    /** @throws NotFoundException|InternalServerErrorException */
    public function update(int $article_id, int $article_comment_id, array $data, UserEntity $user): int
    {
        $this->article_repository->getOneById($article_id);

        $old_comment = $this->article_comment_repository->findOneById($article_comment_id);

        if (!$old_comment instanceof ArticleCommentEntity) {
            throw new NotFoundException('Article comment not found.');
        }

        if ($old_comment->article_id !== $article_id) {
            throw new NotFoundException('The comment is not related to the article.');
        }

        $changes = $this->computeChanges($old_comment, $data);

        try {
            $this->legacy_pdo->beginTransaction();

            $nb_updated = $this->article_comment_repository->update($article_comment_id, $data);

            if ($nb_updated > 0) {
                $this->article_event_logger->logChanges($article_id, self::EVENT_SCOPE, $changes, $user, [
                    'article_comment' => $article_comment_id,
                ]);
            }

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }

        return $nb_updated;
    }

    public function create(int $article_id, array $data, UserEntity $user): int
    {
        $dto = $this->serializer->denormalize(
            array_merge(['article_id' => $article_id, 'created_by' => $user->id_utilisateur], $data),
            ArticleCommentCreationContextDto::class
        );

        $errors = $this->validator->validate($dto);
        if (count($errors) > 0) {
            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('Invalid parameters'), ['validation_errors' => ConstraintMessageFormatter::extract($errors)]);
        }

        $this->article_repository->getOneById($dto->article_id, ['article_id']);

        try {
            $this->legacy_pdo->beginTransaction();

            $article_comment_id = $this->article_comment_repository->create($dto);

            $this->article_event_logger->logCreation(
                $article_id,
                self::EVENT_SCOPE,
                $this->serializer->normalize($dto),
                $user,
                [
                    'article_comment' => $article_comment_id,
                ]
            );

            $this->legacy_pdo->commit();
        } catch (\Throwable $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }

        return $article_comment_id;
    }

    private function computeChanges(ArticleCommentEntity $old_comment, array $data): array
    {
        $changes = [];
        foreach ($data as $column => $new_value) {
            $old_value = $old_comment->$column;
            if ($old_value !== $new_value) {
                $changes[$column] = ['old' => $old_value, 'new' => $new_value];
            }
        }

        return $changes;
    }
}
