<?php

namespace SonVideo\Erp\Article\Manager;

use App\Client\GraphQLClient;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use GuzzleHttp\Exception\GuzzleException;
use League\Flysystem\FileExistsException;
use League\Flysystem\FileNotFoundException;
use SonVideo\Erp\Article\Entity\ArticleV2Entity;
use SonVideo\Erp\Article\Manager\Filesystem\ArticleMediaFile;
use SonVideo\Erp\Article\Mysql\Repository\SingleArticleReadRepository;
use SonVideo\Erp\Referential\ArticleMedia;

class ArticleMediaCloner
{
    public const CLONE_IMAGE = true;
    public const CLONE_DOCUMENT = true;

    public const QUERY = <<<'GQL'
    query fetchArticleMedias(
      $where: cms_article_article_media_i18n_bool_exp,
      $where_draft: cms_draft_article_article_media_i18n_bool_exp,
      $target_article_sku: String!
    ) {
      source_published: cms_article_article_media_i18n(where: $where) {
        media_id
        meta
        type
        supported_culture_id
        display_order
        media {
          media_variation
        }
      }
      source_draft: cms_draft_article_article_media_i18n(where: $where_draft) {
        meta
        type
        supported_culture_id
        display_order
        media {
          media_variation
        }
      }
      target_published: cms_article_article(where: {sku: {_eq: $target_article_sku}}) {
        article_id
      }
      target_published_image: cms_article_article_media_i18n_aggregate(where: {media: {article: {sku: {_eq: $target_article_sku}}, media_variation: {_has_key: "image"}}}) {
        aggregate {
          max {
            display_order
          }
        }
      }
      target_published_document: cms_article_article_media_i18n_aggregate(where: {media: {article: {sku: {_eq: $target_article_sku}}, media_variation: {_has_key: "document"}}}) {
        aggregate {
          max {
            display_order
          }
        }
      }
      target_draft: cms_draft_article_article(where: {sku: {_eq: $target_article_sku}}) {
        article_id
      }
      target_draft_image: cms_draft_article_article_media_i18n_aggregate(where: {media: {article: {sku: {_eq: $target_article_sku}}, media_variation: {_has_key: "image"}}}) {
        aggregate {
          max {
            display_order
          }
        }
      }
      target_draft_document: cms_draft_article_article_media_i18n_aggregate(where: {media: {article: {sku: {_eq: $target_article_sku}}, media_variation: {_has_key: "document"}}}) {
        aggregate {
          max {
            display_order
          }
        }
      }
    }
    GQL;

    public const MUTATION_PUBLISHED = <<<'GQL'
    mutation insertMedia($data: [cms_article_article_media_i18n_insert_input!]!) {
      insert_cms_article_article_media_i18n(objects: $data) {
        affected_rows
      }
    }
    GQL;

    public const MUTATION_DRAFT = <<<'GQL'
    mutation insertMedia($data: [cms_draft_article_article_media_i18n_insert_input!]!) {
      insert_cms_draft_article_article_media_i18n(objects: $data) {
        affected_rows
      }
    }
    GQL;

    private ArticleMediaVariations $article_media_variations;

    private GraphQLClient $graphql_client;

    private ArticleMediaFile $article_media_file;

    private SingleArticleReadRepository $repository;

    public function __construct(
        ArticleMediaVariations $article_media_variations,
        ArticleMediaFile $article_media_file,
        SingleArticleReadRepository $repository,
        GraphQLClient $graphql_client
    ) {
        $this->article_media_variations = $article_media_variations;
        $this->article_media_file = $article_media_file;
        $this->repository = $repository;
        $this->graphql_client = $graphql_client;
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     * @throws GuzzleException
     * @throws \JsonException
     */
    public function clone(
        string $id_or_sku_source,
        string $id_or_sku_target,
        bool $clone_image = true,
        bool $clone_document = true,
        array $selected_media_ids = []
    ): void {
        $article_source = $this->repository->getOneByIdOrSku($id_or_sku_source);
        $article_target = $this->repository->getOneByIdOrSku($id_or_sku_target);

        if ($article_source->article_id === $article_target->article_id) {
            throw new \InvalidArgumentException('The source article and the target article cannot be identical');
        }

        $data = $this->fetchData($article_source->sku, $article_target->sku, $selected_media_ids);
        $cloned_gql_payload = [];

        foreach ($data['medias'] as $media) {
            if (ArticleMedia::TYPE_IMAGE === $media['type'] && $clone_image) {
                $cloned_gql_payload[] = $this->cloneImage($media, $article_target, $data['next_image_display_order']);
                continue;
            }

            // No more treatment possible for images
            if (ArticleMedia::TYPE_IMAGE === $media['type']) {
                continue;
            }

            if (!$clone_document) {
                continue;
            }

            $cloned_gql_payload[] = $this->cloneDocument($media, $article_target, $data['next_document_display_order']);
        }

        if ([] !== $cloned_gql_payload) {
            $this->graphql_client->call([
                'query' => ArticleMedia::PUBLISHED === $data['type'] ? static::MUTATION_PUBLISHED : static::MUTATION_DRAFT,
                'variables' => ['data' => $cloned_gql_payload],
            ]);
        }
    }

    /**
     * @return array{display_order: int, meta: mixed, type: mixed, supported_culture_id: mixed, article_id: int, media:
     *                              array{data: array{article_id: int, media_variation: array{image: array{largest:
     *                              mixed, source: string, referential: array<int|string, string>}}}}}
     *
     * @throws NotFoundException
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    private function cloneImage(array $image, ArticleV2Entity $article_target, int $next_display_order): array
    {
        // extract data to copy
        [
            'meta' => $meta,
            'type' => $type,
            'supported_culture_id' => $supported_culture_id,
            'media' => $media,
            'display_order' => $display_order
        ] = $image;

        // Get the largest image from current media
        $image_path =
            $media['media_variation']['image']['source'] ??
            $media['media_variation']['image']['referential'][(string) $media['media_variation']['image']['largest']];

        // clone it physically for the target article
        $path_to = $this->article_media_file->move($image_path, $article_target, ArticleMedia::TYPE_IMAGE);

        // prepare size variations
        $image_payload = $this->article_media_variations->make($path_to);
        $image_payload['article_id'] = $article_target->article_id;

        // return formatted payload
        return [
            'display_order' => $next_display_order + $display_order,
            'meta' => $meta,
            'type' => $type,
            'supported_culture_id' => $supported_culture_id,
            'article_id' => $article_target->article_id,
            'media' => [
                'data' => $image_payload,
            ],
        ];
    }

    /**
     * @return array{display_order: int, meta: mixed, type: mixed, supported_culture_id: mixed, article_id: int, media:
     *                              array{data: mixed}}
     *
     * @throws FileExistsException
     * @throws FileNotFoundException
     */
    private function cloneDocument(array $document, ArticleV2Entity $article_target, int $next_display_order): array
    {
        // extract data to copy
        [
            'meta' => $meta,
            'type' => $type,
            'supported_culture_id' => $supported_culture_id,
            'media' => $media,
            'display_order' => $display_order
        ] = $document;

        // clone it physically for the target article
        $path_to = $this->article_media_file->move(
            $media['media_variation']['document']['url'],
            $article_target,
            ArticleMedia::TYPE_DOCUMENT
        );

        // return formatted payload
        $media['media_variation']['document']['url'] = $path_to;
        $media['article_id'] = $article_target->article_id;

        return [
            'display_order' => $next_display_order + $display_order,
            'meta' => $meta,
            'type' => $type,
            'supported_culture_id' => $supported_culture_id,
            'article_id' => $article_target->article_id,
            'media' => [
                'data' => $media,
            ],
        ];
    }

    private function fetchData(string $sku_source, string $sku_target, array $selected_media_ids = []): array
    {
        $conditions = [
            'media' => [
                'article' => [
                    'sku' => [
                        '_eq' => $sku_source,
                    ],
                ],
            ],
        ];

        if ([] !== $selected_media_ids) {
            $conditions['media_id'] = ['_in' => $selected_media_ids];
        }

        $where = [
            'where' => $conditions,
            'where_draft' => $conditions,
            'target_article_sku' => $sku_target,
        ];

        $response = $this->graphql_client->call([
            'query' => static::QUERY,
            'variables' => $where,
        ])['data'];

        // Get the next available display order on target article for both media types
        $next_image_display_order =
            $response['target_published_image']['aggregate']['max']['display_order'] ??
            ($response['target_draft_image']['aggregate']['max']['display_order'] ?? 0);

        $next_document_display_order =
            $response['target_published_document']['aggregate']['max']['display_order'] ??
            ($response['target_draft_document']['aggregate']['max']['display_order'] ?? 0);

        if (
            1 !== (is_countable($response['target_draft']) ? count($response['target_draft']) : 0) &&
            1 !== (is_countable($response['target_published']) ? count($response['target_published']) : 0)
        ) {
            throw new \UnexpectedValueException(sprintf('Could not found either a published nor a draft target article with sku "%s"', $sku_target));
        }

        $target_type =
            1 === (is_countable($response['target_draft']) ? count($response['target_draft']) : 0)
                ? ArticleMedia::DRAFT
                : ArticleMedia::PUBLISHED;

        if (empty($response['source_published'])) {
            return [
                'type' => $target_type,
                'medias' => $response['source_draft'],
                'next_image_display_order' => $next_image_display_order,
                'next_document_display_order' => $next_document_display_order,
            ];
        }

        return [
            'type' => $target_type,
            'medias' => $response['source_published'],
            'next_image_display_order' => $next_image_display_order,
            'next_document_display_order' => $next_document_display_order,
        ];
    }
}
