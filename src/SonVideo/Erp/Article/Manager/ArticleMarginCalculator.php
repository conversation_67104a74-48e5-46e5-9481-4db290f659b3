<?php

declare(strict_types=1);

namespace SonVideo\Erp\Article\Manager;

use SonVideo\Erp\Article\Dto\SalesChannelComputedPriceDto;
use SonVideo\Erp\Article\Dto\SalesChannelPriceContextDto;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class ArticleMarginCalculator
{
    public const MARGIN_TOO_LOW_ERROR_CODE = 'selling_price_margin_is_too_low';
    public const MARGIN_RATE_TOO_LOW_ERROR_CODE = 'selling_price_margin_rate_is_too_low';
    public const SELLING_PRICE_ABOVE_PVGC_ERROR_CODE = 'selling_price_is_greater_than_pvgc';
    public const UNKNOWN_CHANNEL_ID = 0;

    private ValidatorInterface $validator;

    public function __construct(ValidatorInterface $validator)
    {
        $this->validator = $validator;
    }

    public function getEffectivePurchasePrice(SalesChannelPriceContextDto $context): float
    {
        if (
            $context->stock > 0 &&
            $context->weighted_purchase_price_tax_excluded &&
            $context->weighted_purchase_price_tax_excluded > 0
        ) {
            return $context->weighted_purchase_price_tax_excluded;
        }

        return $context->purchase_price_tax_excluded;
    }

    public function getAllTaxes(SalesChannelPriceContextDto $context): float
    {
        return $context->ecotax / (1 + $context->vat_rate) + $context->sorecop / (1 + $context->vat_rate);
    }

    public function getPurchasePriceUnconditionalDiscount(float $purchase_price, float $unconditional_discount): float
    {
        return $purchase_price * ($unconditional_discount / 100);
    }

    public function getSellingPriceTaxExcluded(SalesChannelPriceContextDto $context): float
    {
        return $context->selling_price / (1 + $context->vat_rate);
    }

    public function computeMargin(SalesChannelPriceContextDto $context): SalesChannelComputedPriceDto
    {
        $violations = $this->validator->validate($context);

        if (count($violations) > 0) {
            $errors = [];
            foreach ($violations as $violation) {
                $errors[] = [
                    'code' => $violation->getCode(),
                    'message' => $violation->getPropertyPath() . ': ' . $violation->getMessage(),
                ];
            }
            $result = new SalesChannelComputedPriceDto();
            $result->sales_channel_id = $context->sales_channel_id ?? self::UNKNOWN_CHANNEL_ID;
            $result->errors = $errors;

            return $result;
        }

        $selling_price_vat_excluded = $this->getSellingPriceTaxExcluded($context);
        $selling_price_all_taxes_excluded = $selling_price_vat_excluded - $this->getAllTaxes($context);
        $effective_purchase_price = $this->getEffectivePurchasePrice($context);
        $purchase_price_unconditional_discount = $this->getPurchasePriceUnconditionalDiscount(
            $context->purchase_price_tax_excluded,
            $context->unconditional_discount
        );
        $computed_purchase_price =
            $effective_purchase_price - $purchase_price_unconditional_discount - $context->promo_budget_amount;

        $sales_channel_commission = $selling_price_vat_excluded * ($context->sales_channel_commission_fee / 100);

        $margin = $selling_price_all_taxes_excluded - $sales_channel_commission - $computed_purchase_price;

        $result = new SalesChannelComputedPriceDto();
        $result->sales_channel_id = $context->sales_channel_id;
        $result->margin = $margin;
        $result->markup_rate = 0 > $computed_purchase_price ? -1 : $margin / $computed_purchase_price;
        $result->margin_rate = 0 > $selling_price_all_taxes_excluded ? -1 : $margin / $selling_price_all_taxes_excluded;
        $result->selling_price_tax_excluded = $selling_price_vat_excluded;
        $result->sales_channel_commission = $sales_channel_commission;
        $result->selling_price_tax_included = $context->selling_price;

        return $result;
    }

    public function computeMarginWithErrorCheck(
        SalesChannelPriceContextDto $context,
        float $minimum_margin_rate
    ): SalesChannelComputedPriceDto {
        $result = $this->computeMargin($context);
        $errors = $result->errors ?? [];

        if ($result->margin < 0) {
            $errors[] = [
                'code' => self::MARGIN_TOO_LOW_ERROR_CODE,
                'message' => 'Product margin is too low',
            ];
        }
        if ($result->margin_rate < $minimum_margin_rate / 100) {
            $errors[] = [
                'code' => self::MARGIN_RATE_TOO_LOW_ERROR_CODE,
                'message' => "Margin rate should not be lower than $minimum_margin_rate%",
            ];
        }
        if (round($context->pvgc, 2) < round($context->selling_price, 2)) {
            $errors[] = [
                'code' => self::SELLING_PRICE_ABOVE_PVGC_ERROR_CODE,
                'message' => 'Selling price is higher than the GOSP',
            ];
        }
        $result->errors = [] !== $errors ? $errors : null;

        return $result;
    }

    public function computeSellingPriceFromMarginRate(SalesChannelPriceContextDto $context, float $margin_rate): float
    {
        if ($margin_rate <= 0) {
            throw new \InvalidArgumentException('Margin rate must be between 0 and 1.');
        }

        $cost =
            $this->getEffectivePurchasePrice($context) +
            $this->getAllTaxes($context) -
            $context->promo_budget_amount -
            $this->getPurchasePriceUnconditionalDiscount(
                $context->purchase_price_tax_excluded,
                $context->unconditional_discount
            );

        return ($cost * (1 + $context->vat_rate)) / (1 - $margin_rate - $context->sales_channel_commission_fee / 100);
    }
}
