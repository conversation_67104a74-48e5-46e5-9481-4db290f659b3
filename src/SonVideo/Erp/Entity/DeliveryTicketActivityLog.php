<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2017 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Entity;

use App\Entity\AbstractEntity;

/**
 * Class DeliveryTicketActivityLog.
 */
class DeliveryTicketActivityLog extends AbstractEntity
{
    public int $delivery_ticket_activity_log_id;

    public ?int $delivery_ticket_id = null;

    public int $delivery_ticket_id_without_fk;

    public string $action;

    public ?string $description = null;

    public \DateTimeInterface $created_at;

    public int $created_by;

    public ?string $created_by_name = null;

    public ?string $foreign_id = null;
}
