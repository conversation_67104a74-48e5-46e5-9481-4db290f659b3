<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\CustomerOrder;

use App\Contract\EntityInterface;
use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Entity\Shipping\Carrier\EligibilityContext\BillingAddressEligibilityContextEntity;
use SonVideo\Erp\Entity\Shipping\Carrier\EligibilityContext\CustomerEligibilityContextEntity;
use SonVideo\Erp\Entity\Shipping\Carrier\EligibilityContext\ItemEligibilityContextEntity;
use SonVideo\Erp\Entity\Shipping\Carrier\EligibilityContext\ShipmentMethodEligibilityContextEntity;
use SonVideo\Erp\Entity\Shipping\Carrier\EligibilityContext\ShippingAddressEligibilityContextEntity;
use SonVideo\Erp\Entity\Shipping\Carrier\EligibilityContextEntity;

/**
 * Class CarrierEligibilityContextRepository.
 */
class CarrierEligibilityContextRepository extends AbstractLegacyRepository
{
    public const QUOTATION_SHIPMENT_ID = 99;
    public const CHRONOPOST_SHIPMENT_SERVICE_SHIPMENT_ID = [15, 16, 68];
    public const CUSTOMER_ORDER_CREATION_ORIGIN_EXCLUDED = ['cilo.dk'];

    /** fetchById */
    public function fetchById(int $customer_order_id): array
    {
        $sql = <<<SQL
        CALL backOffice.CUSTOMER_ORDER_CARRIER_ELIGIBILITY_CONTEXT_get(:customer_order_id)
        SQL;
        $result = $this->legacy_pdo->fetchCollection($sql, ['customer_order_id' => $customer_order_id]);

        return $this->data_loader
            ->hydrate(
                [
                    'items' => array_map(
                        fn ($article, $index): EntityInterface => $this->formatArticle($article, $index),
                        $result[0],
                        array_keys($result[0])
                    ),
                    'shipment_method' => $this->useEntityWith(
                        $result[1][0] ?? [],
                        ShipmentMethodEligibilityContextEntity::class
                    ),
                    'customer' => $this->useEntityWith($result[2][0] ?? [], CustomerEligibilityContextEntity::class),
                    'shipping_address' => $this->useEntityWith(
                        $result[3][0] ?? [],
                        ShippingAddressEligibilityContextEntity::class
                    ),
                    'billing_address' => $this->useEntityWith(
                        $result[4][0] ?? [],
                        BillingAddressEligibilityContextEntity::class
                    ),
                ],
                EligibilityContextEntity::class
            )
            ->toArray();
    }

    protected function formatArticle(array $article, $index): EntityInterface
    {
        $article['order_line_no'] = $index + 1;

        return $this->useEntityWith($article, ItemEligibilityContextEntity::class);
    }

    /** useEntityWith */
    protected function useEntityWith(array $value, string $entity_class): EntityInterface
    {
        // Throw error explicitly as PHP notices don't throw exceptions in prod environment
        // (On dev they they generate an ErrorException dur to the framework debug setting)
        if ([] === $value) {
            throw new \UnexpectedValueException(sprintf('Empty result set not usable to build a proper eligibility context for entity: "%s"', $entity_class));
        }

        return $this->data_loader->hydrate($value, $entity_class);
    }

    /** fetchAllOrdersThatNeedDelivery */
    public function fetchAllOrdersThatNeedDelivery(): array
    {
        // Undefined carrier are excluded since there are handled via another separated process
        $sql = <<<SQL
        SELECT
          c.id_commande AS customer_order_id
          FROM
            backOffice.commande c
              INNER JOIN backOffice.commande_en_attente_de_livraison ceadl ON c.id_commande = ceadl.id_commande
          WHERE c.id_transporteur IS NOT NULL
            AND c.id_transporteur != 1
          HAVING backOffice.HAS_REMAINING_PRODUCT_TO_DELIVER_ON_CUSTOMER_ORDER(c.id_commande) > 0;
        SQL;

        return $this->legacy_pdo->fetchObjects($sql);
    }

    /** fetchAllStorePickupShipmentMethodIds */
    public function fetchAllStorePickupShipmentMethodIds(): array
    {
        $sql = <<<SQL
        SELECT
          btpl.id              AS shipment_method_id,
          btpl.libelle_produit AS name
          FROM
            backOffice.BO_TPT_PDT_liste          btpl
              INNER JOIN backOffice.BO_STK_depot bsd ON btpl.transporteur_id = bsd.id_transporteur_emport
          WHERE btpl.actif = 1
        ;
        SQL;

        return $this->legacy_pdo->fetchObjects($sql);
    }
}
