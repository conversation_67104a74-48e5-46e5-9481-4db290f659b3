<?php

namespace SonVideo\Erp\Repository;

use App\Sql\AbstractLegacyRepository;

/**
 * Class MysqlEventMonitoringRepository.
 */
class MysqlEventMonitoringRepository extends AbstractLegacyRepository
{
    /** getEventsAndUUID. */
    public function getEventsAndUUID(): array
    {
        $sql = <<<SQL
        SELECT
          event_name,
          healthcheck_uuid
          FROM backOffice.BO_SYS_event_monitoring
        SQL;

        return $this->legacy_pdo->fetchObjects($sql);
    }
}
