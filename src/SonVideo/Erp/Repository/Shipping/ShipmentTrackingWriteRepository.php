<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Shipping;

use App\Sql\AbstractLegacyRepository;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

/**
 * Class ShipmentTrackingWriteRepository.
 */
class ShipmentTrackingWriteRepository extends AbstractLegacyRepository implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    public function updateParcelFranceExpressTracking(int $parcel_id, string $tracking_id): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.colis_franceexpress_tracking (id_colis, tracking_id, date_creation)
          VALUES
            (:parcel_id, :tracking_id, now())
        SQL;

        return $this->legacy_pdo->fetchAffected($sql, ['parcel_id' => $parcel_id, 'tracking_id' => $tracking_id]);
    }
}
