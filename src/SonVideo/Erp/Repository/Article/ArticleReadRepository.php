<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository\Article;

use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\AbstractLegacyRepository;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;

/**
 * Class ArticleReadRepository.
 */
class ArticleReadRepository extends AbstractLegacyRepository
{
    public const COLUMNS_MAPPING = [
        'product_id' => 'a.id_produit',
        'sku' => 'p.reference',
        'type' => 'p.type',
        'model' => 'a.modele',
        'domain_id' => 'p.V_id_domaine',
        'category_id' => 'p.V_id_categorie',
        'subcategory_id' => 'p.id_souscategorie',
        'created_at' => 'a.date_creation',
        'updated_at' => 'a.modif_date',
        'status' => 'a.status',
        'brand_id' => 'a.id_marque',
        'selling_price' => 'a.prix_vente',
        'pvgc' => 'a.prix_vente_generalement_constate',
        'ecotax' => 'a.prix_ecotaxe',
        'sorecop' => 'a.prix_sorecop',
        'weight' => 'a.poids',
        'supplier_id' => 'a.id_fournisseur',
        'supplier_reference' => 'a.reference_fournisseur',
        'stock_quantity' => 'a.V_quantite_stock',
        'basket_description' => 'a.description_panier',
        'short_description' => 'a.description_courte',
        'initial_selling_price' => 'a.prix_vente_initial',
        'is_package' => '(a.compose = 1)',
        'is_destock' => '(p.reference LIKE \'DESTOCK%\')',
        'sku_havre' => 'a.reference_havre',
        'delivery_time' => 'a.V_delai_lvr',
        'wholesale_price' => 'a.prix_achat_tarif',
        'wholesale_weighted_cost' => 'a.prix_achat_pondere',
    ];

    /** findAllPaginated */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<'MYSQL'
        SELECT SQL_CALC_FOUND_ROWS
          fa.*,
          m.marque AS brand_name,
          CASE WHEN fa.is_package = 0
                 THEN fa.delivery_time
               ELSE ac.V_delai_lvr END AS delivery_time,
          backOffice.PDT_ART_format_V_delai_lvr(CASE WHEN fa.is_package = 0
                THEN fa.delivery_time
              ELSE ac.V_delai_lvr END) AS delivery_time_computed,
          dom.domaine AS domain_name,
          cat.categorie AS category_name,
          scat.souscategorie AS subcategory_name,
          coalesce(bc.article_url, '') AS url_site,
          backOffice.FORMAT_image_path_without_cdn(coalesce(bc.media_300_square_uri, bc.media_largest_uri)) AS url_image,
          coalesce(CASE WHEN fa.is_package = 0
                 THEN fa.wholesale_price
               ELSE ac.prix_achat_tarif END, 0) AS wholesale_price,
          coalesce(CASE WHEN fa.is_package = 0
                 THEN fa.wholesale_weighted_cost
               ELSE ac.prix_achat_pondere END, 0) AS wholesale_weighted_cost,
          backOffice.PDT_ART_taux_marque(fa.product_id) * 100 AS margin_rate,
          CASE WHEN fa.is_package = 0
                 THEN backOffice.PDT_ART_qte_stock(fa.product_id)
               ELSE backOffice.PDT_CMP_qte_stock(fa.product_id) END AS stock_quantity
        FROM
            (
              SELECT
                p.reference AS sku,
                a.id_produit AS product_id,
                a.modele AS model,
                p.V_id_domaine AS domain_id,
                p.V_id_categorie AS category_id,
                p.id_souscategorie AS subcategory_id,
                a.date_creation AS created_at,
                a.modif_date AS updated_at,
                a.status AS status,
                a.id_marque AS brand_id,
                a.prix_vente AS selling_price,
                a.prix_vente_generalement_constate AS pvgc,
                a.prix_ecotaxe AS ecotax,
                a.prix_sorecop AS sorecop,
                a.poids AS weight,
                a.id_fournisseur AS supplier_id,
                a.reference_fournisseur AS supplier_reference,
                a.V_quantite_stock AS stock_quantity,
                a.description_panier AS basket_description,
                a.description_courte AS short_description,
                a.prix_vente_initial AS initial_selling_price,
                a.V_delai_lvr AS delivery_time,
                a.prix_achat_tarif AS wholesale_price,
                a.prix_achat_pondere AS wholesale_weighted_cost,
                a.compose = 1 AS is_package,
                p.reference LIKE 'DESTOCK%' AS is_destock,
                a.reference_havre AS sku_havre,
                COALESCE(lpad(backOffice.PDT_ART_ean(a.id_produit), 13, '0'), backOffice.PDT_code128C(a.id_produit)) AS ean
                FROM
                  backOffice.article a
                    INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
                WHERE {conditions}
              ) fa
              LEFT JOIN  backOffice.BO_PDT_ART_V_compose  ac ON fa.product_id = ac.id
              INNER JOIN backOffice.marque m ON fa.brand_id = m.id_marque
              INNER JOIN backOffice.CTG_TXN_domaine dom ON fa.domain_id = dom.id
              INNER JOIN backOffice.CTG_TXN_categorie cat ON fa.category_id = cat.id_categorie
              INNER JOIN backOffice.CTG_TXN_souscategorie scat ON fa.subcategory_id = scat.id
              LEFT JOIN  backOffice.batch_catalog bc ON fa.product_id = bc.article_id
        {order_by}
        MYSQL;
        $sql = strtr($sql, [
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateAssoc(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    /**
     * exists.
     *
     * @param $product_id_or_sku
     *
     * @throws SqlErrorMessageException
     */
    public function exists($product_id_or_sku): bool
    {
        $condition = ctype_digit($product_id_or_sku) ? 'p.id_produit' : 'p.reference';

        $sql = <<<SQL
        SELECT
          EXISTS(
            SELECT 1 FROM backOffice.produit p WHERE {condition} = :product_id_or_sku
            ) AS entry;
        SQL;

        $result = $this->legacy_pdo->fetchValue(
            strtr($sql, [
                '{condition}' => $condition,
            ]),
            ['product_id_or_sku' => $product_id_or_sku]
        );

        return (bool) $result;
    }

    public function findOneByIdOrSku($product_id_or_sku): array
    {
        $condition = ctype_digit($product_id_or_sku) ? 'p.id_produit' : 'p.reference';

        $sql = <<<SQL
        SELECT
          a.id_produit                          AS product_id,
          p.reference                           AS sku,
          p.id_souscategorie                    AS subcategory_id,
          a.date_creation                       AS created_at,
          a.modif_date                          AS updated_at,
          a.status                              AS status,
          m.marque                              AS brand_name,
          a.id_marque                           AS brand_id,
          a.prix_vente                          AS selling_price,
          a.prix_vente_generalement_constate    AS pvgc,
          a.prix_ecotaxe                        AS ecotax,
          a.prix_sorecop                        AS sorecop,
          a.poids                               AS weight,
          a.id_fournisseur                      AS supplier_id,
          a.reference_fournisseur               AS supplier_reference,
          a.V_quantite_stock                    AS stock_quantity,
          a.description_panier                  AS basket_description,
          a.description_courte                  AS short_description,
          a.prix_vente_initial                  AS initial_selling_price,
          a.compose = 1                         AS is_package,
          backOffice.PDT_code128C(p.id_produit) AS code128,
          a.reference_havre                     AS sku_havre,
          a.nombre_colis                        AS package_number
          FROM
            backOffice.article                          a
              INNER JOIN backOffice.produit             p ON a.id_produit = p.id_produit
              INNER JOIN backOffice.marque              m ON a.id_marque = m.id_marque
        WHERE {condition} = :product_id_or_sku
        SQL;

        $article = $this->legacy_pdo->fetchOne(
            strtr($sql, [
                '{condition}' => $condition,
            ]),
            ['product_id_or_sku' => $product_id_or_sku]
        );

        if (false === $article) {
            throw new NotFoundException('Article not found');
        }

        $article['barcodes'] = $this->findAllBarcodesByIdOrSku($product_id_or_sku);

        return $article;
    }

    public function findAllBarcodesByIdOrSku($product_id_or_sku): array
    {
        $condition = ctype_digit($product_id_or_sku) ? 'p.id_produit' : 'p.reference';

        $sql = <<<SQL
        SELECT
          PDT_code128C(p.id_produit) AS code,
          'CODE128'                  AS type
        FROM backOffice.article           a
            INNER JOIN backOffice.produit p ON a.id_produit = p.id_produit
        WHERE {condition} = :product_id_or_sku
        UNION
        SELECT
          lpad(bcpae.ean, 13, '0') AS code,
          'EAN'                    AS type
        FROM backOffice.article                        a
               LEFT JOIN backOffice.BO_CTG_PDT_ART_ean bcpae ON a.id_produit = bcpae.BO_CTG_PDT_ART_article_id
               INNER JOIN backOffice.produit           p ON a.id_produit = p.id_produit
        WHERE {condition} = :product_id_or_sku
        SQL;

        return $this->legacy_pdo->fetchAll(
            strtr($sql, [
                '{condition}' => $condition,
            ]),
            ['product_id_or_sku' => $product_id_or_sku]
        );
    }

    /**
     * findIdWithBarcode.
     *
     * @throws SqlErrorMessageException
     */
    public function findIdWithBarcode(string $barcode): int
    {
        $result = $this->legacy_pdo->fetchValue(
            <<<SQL
            SELECT backOffice.ARTICLE_FIND_WITH_BARCODE(:barcode);
            SQL
            ,
            ['barcode' => $barcode]
        );

        return (int) $result;
    }

    /**
     * findUrlById.
     *
     * @throws SqlErrorMessageException
     */
    public function findUrlById(int $product_id): string
    {
        return $this->legacy_pdo->fetchValue(
            <<<SQL
            SELECT article_url
            FROM backOffice.batch_catalog
            WHERE article_id = :product_id
            ;
            SQL
            ,
            ['product_id' => $product_id]
        ) ?? '';
    }
}
