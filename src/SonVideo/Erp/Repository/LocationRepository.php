<?php
/*
 * This file is part of erp package.
 *
 * (c) 2019 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Repository;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\Entity\LocationEntity;
use SonVideo\Erp\Referential\AreaType;

/**
 * Class LocationRepository.
 */
class LocationRepository implements LegacyPdoAwareInterface, DataLoaderAwareInterface
{
    use LegacyPdoAwareTrait;
    use DataLoaderAwareTrait;

    public const COLUMNS_MAPPING = [
        'location_id' => 'wl.location_id',
        'code' => 'wl.code',
        'area_id' => 'wl.area_id',
        'label' => 'wl.label',
        'is_active' => 'wl.is_active',
        'warehouse_id' => 'wa.warehouse_id',
        'area_type' => 'wa.area_type_id',
        'area_label' => 'wa.label',
    ];

    protected AreaRepository $area_repo;

    protected WarehouseRepository $warehouse_repo;

    protected AccountQueryRepository $user_repo;

    private QueryBuilder $query_builder;

    /** LocationRepository constructor. */
    public function __construct(
        AreaRepository $area_repo,
        WarehouseRepository $warehouse_repo,
        AccountQueryRepository $user_repo,
        QueryBuilder $query_builder
    ) {
        $this->area_repo = $area_repo;
        $this->warehouse_repo = $warehouse_repo;
        $this->user_repo = $user_repo;
        $this->query_builder = $query_builder;
    }

    /** @throws NotFoundException */
    public function findOneById(int $location_id): LocationEntity
    {
        $this->query_builder->setWhere(['location_id' => ['_eq' => $location_id]]);

        return $this->findOne($this->query_builder);
    }

    /** @throws NotFoundException */
    public function findOneByCode(string $code): LocationEntity
    {
        $this->query_builder->setWhere(['code' => ['_eq' => $code]]);

        return $this->findOne($this->query_builder);
    }

    /** @throws NotFoundException */
    public function findOne(QueryBuilder $query_builder): LocationEntity
    {
        $sql = <<<MYSQL
        SELECT *
        FROM backOffice.WMS_location
        WHERE {where};
        MYSQL;

        $sql = strtr($sql, [
            '{where}' => $query_builder->getWhere(),
        ]);

        $location_data = $this->legacy_pdo->fetchOne($sql, $query_builder->getWhereParameters());
        if (false === $location_data) {
            throw new NotFoundException('Location not found.');
        }

        $location = $this->data_loader->hydrate($location_data, LocationEntity::class);

        // add area
        $area = $this->area_repo->findOneById($location->area_id);
        $location->fromArray(['area' => $area]);

        // add warehouse
        $warehouse = $this->warehouse_repo->findOneById($location->area->warehouse_id);
        $location->fromArray(['warehouse' => $warehouse]);

        return $location;
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     */
    public function findOneByUserLocationCode(int $user_id, int $warehouse_id): LocationEntity
    {
        $warehouse_wms_code = $this->warehouse_repo->findOneById($warehouse_id)->code;
        if (null === $warehouse_wms_code) {
            throw new NotFoundException('Warehouse not found.');
        }

        // @throw NotFoundException
        $this->user_repo->findOneById($user_id);

        $user_location_code = sprintf('%s.user.%s', $warehouse_wms_code, $user_id);

        $sql = <<<MYSQL
        SELECT *
        FROM backOffice.WMS_location
        WHERE code = :user_location_code
        MYSQL;

        $user_location_data = $this->legacy_pdo->fetchOne($sql, ['user_location_code' => $user_location_code]);

        // if this user location doesn't exist yet, create
        if (false === $user_location_data) {
            $user_location = $this->createUserLocation($user_location_code, $user_id, $warehouse_id);
        } else {
            $user_location = $this->data_loader->hydrate($user_location_data, LocationEntity::class);

            // add area
            $area = $this->area_repo->findOneById($user_location->area_id);
            $user_location->fromArray(['area' => $area]);

            // add warehouse
            $warehouse = $this->warehouse_repo->findOneById($user_location->area->warehouse_id);
            $user_location->fromArray(['warehouse' => $warehouse]);
        }

        return $user_location;
    }

    /**
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     */
    public function createUserLocation(string $location_code, int $user_id, int $warehouse_id): LocationEntity
    {
        $transit_area = AreaType::TRANSIT;
        $sql = <<<MYSQL
        INSERT INTO backOffice.WMS_location (code, area_id, label)
        SELECT :location_code, area_id, CONCAT('Chariot de ', backOffice.GET_COMPUTED_USER_NAME_BY_ID(:user_id))
        FROM WMS_area
        WHERE warehouse_id = :warehouse_id
          AND area_type_id = :transit_area
        LIMIT 1;
        MYSQL;

        $new_row = $this->legacy_pdo->fetchAffected($sql, [
            'location_code' => $location_code,
            'user_id' => $user_id,
            'warehouse_id' => $warehouse_id,
            'transit_area' => $transit_area,
        ]);
        if (0 === $new_row) {
            throw new \UnexpectedValueException(sprintf('User location creation failed for user "%s".', $user_id));
        }

        // retrieve new location_id ...
        $sql = <<<MYSQL
        SELECT location_id FROM backOffice.WMS_location ORDER BY location_id DESC LIMIT 1;
        MYSQL;
        $location_id = $this->legacy_pdo->fetchValue($sql);

        // ... to fetch user location
        $location = $this->findOneById($location_id);

        return $location;
    }

    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
        FROM
            (
              SELECT
                wl.location_id                 AS location_id,
                wl.code                        AS code,
                wl.area_id                     AS area_id,
                wl.label                       AS label,
                wl.is_active                   AS is_active,
                COUNT(wpl.product_location_id) AS products,
                wa.warehouse_id                AS warehouse_id,
                wa.area_type_id                AS area_type,
                wa.label                       AS area_label
                FROM
                  backOffice.WMS_location                      wl
                    INNER JOIN backOffice.WMS_area             wa ON wa.area_id = wl.area_id
                    LEFT JOIN  backOffice.WMS_product_location wpl ON wl.location_id = wpl.location_id
                WHERE {where}
                GROUP BY wl.location_id
              ) tmp
        {order_by}
        SQL;
        $sql = strtr($sql, [
            '{where}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateObjects(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }
}
