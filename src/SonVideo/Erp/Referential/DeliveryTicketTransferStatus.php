<?php
/*
 * This file is part of [MELKART] ERP SERVER package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Referential;

/**
 * Class DeliveryTicketTransferStatus.
 */
final class DeliveryTicketTransferStatus
{
    public const CANCELED = 'annule';
    public const CLOSED = 'cloture';
    public const LEAVING = 'au depart';
    public const LOST = 'perdu';
    public const PENDING = 'attente';
    public const PROCESSING = 'traitement';
    public const SENT = 'expedie';
    public const TEMP = 'tmp';

    public const PICKABLE_STATUSES = [self::LEAVING];
}
