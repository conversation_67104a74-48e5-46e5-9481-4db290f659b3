<?php

namespace SonVideo\Erp\Referential;

final class UserPermission
{
    public const ACCOUNTING_PAYMENT_CREATE = 'erp.ACCOUNTING_PAYMENT_CREATE';
    public const ACCOUNTING_PAYMENT_READ = 'erp.ACCOUNTING_PAYMENT_READ';
    public const ACCOUNTING_PAYMENT_UPDATE = 'erp.ACCOUNTING_PAYMENT_UPDATE';
    public const ARTICLE_QR_CODE_VIEW = 'erp.ARTICLE_QR_CODE_VIEW';
    public const ALL_COMMISSIONS_READ = 'erp.ALL_COMMISSIONS_READ';
    public const ARTICLE_BUYERS_WRITE = 'erp.ARTICLE_BUYERS_WRITE';
    public const ARTICLE_SALES_CHANNEL_WRITE = 'erp.ARTICLE_SALES_CHANNEL_WRITE';
    public const ARTICLE_DESTOCK_WRITE = 'erp.ARTICLE_DESTOCK_WRITE';
    public const ARTICLE_COMMENT_WRITE = 'erp.ARTICLE_COMMENT_WRITE';
    public const ARTICLE_GENERAL_INFORMATION_WRITE = 'erp.ARTICLE_GENERAL_INFORMATION_WRITE';
    public const ARTICLE_LOGISTIC_WRITE = 'erp.ARTICLE_LOGISTIC_WRITE';
    public const ARTICLE_MEDIA_ADMINISTRATE = 'erp.ARTICLE_MEDIA_ADMINISTRATE';
    public const ARTICLE_PACKAGE_WRITE = 'erp.ARTICLE_PACKAGE_WRITE';
    public const ARTICLE_PRICES_WRITE = 'erp.ARTICLE_PRICES_WRITE';
    public const ARTICLE_SAFETY_STOCK_THRESHOLD_WRITE = 'erp.ARTICLE_SAFETY_STOCK_THRESHOLD_WRITE';
    public const ARTICLE_STATUS_WRITE = 'erp.ARTICLE_STATUS_WRITE';
    public const ATTRIBUTE_CATEGORY_ASSOCIATION_CREATE = 'erp.ATTRIBUTE_CATEGORY_ASSOCIATION_CREATE';
    public const ATTRIBUTE_CATEGORY_ASSOCIATION_DELETE = 'erp.ATTRIBUTE_CATEGORY_ASSOCIATION_DELETE';
    public const ATTRIBUTE_CREATE = 'erp.ATTRIBUTE_CREATE';
    public const ATTRIBUTE_READ = 'erp.ATTRIBUTE_READ';
    public const ATTRIBUTE_UPDATE = 'erp.ATTRIBUTE_UPDATE';
    public const ATTRIBUTE_VALUE_CREATE = 'erp.ATTRIBUTE_VALUE_CREATE';
    public const ATTRIBUTE_VALUE_DELETE = 'erp.ATTRIBUTE_VALUE_DELETE';
    public const CALL_CENTER_COMMISSIONS_READ = 'erp.CALL_CENTER_COMMISSIONS_READ';
    public const CAN_SEE_API_DOCS = 'erp.CAN_SEE_API_DOCS';
    public const CAN_SEE_EZL_API_DOCS = 'erp.CAN_SEE_EZL_API_DOCS';
    public const CUSTOMER_ANONYMIZE = 'erp.CUSTOMER_ANONYMIZE';
    public const CUSTOMER_COMMENT_WRITE = 'erp.CUSTOMER_COMMENT_WRITE';
    public const CUSTOMER_EMAIL_UPDATE = 'erp.CUSTOMER_EMAIL_UPDATE';
    public const CUSTOMER_ORDER_PAYMENT_ACCEPT_CETELEM = 'erp.CUSTOMER_ORDER_PAYMENT_ACCEPT_CETELEM';
    public const CUSTOMER_ORDER_PAYMENT_CANCEL = 'erp.CUSTOMER_ORDER_PAYMENT_CANCEL';
    public const CUSTOMER_ORDER_PAYMENT_REFUND = 'erp.CUSTOMER_ORDER_PAYMENT_REFUND';
    public const CUSTOMER_ORDER_PAYMENT_REMIT = 'erp.CUSTOMER_ORDER_PAYMENT_REMIT';
    public const CUSTOMER_ORDER_PAYMENT_REMIT_CANCEL = 'erp.CUSTOMER_ORDER_PAYMENT_REMIT_CANCEL';
    public const CUSTOMER_ORDER_PAYMENT_REMIT_CREDIT_NOTE_WITH_PRIOR_PAYMENT = 'erp.CUSTOMER_ORDER_PAYMENT_REMIT_CREDIT_NOTE_WITH_PRIOR_PAYMENT';
    public const CUSTOMER_ORDER_PAYMENT_REMIT_FULLCB = 'erp.CUSTOMER_ORDER_PAYMENT_REMIT_FULLCB';
    public const CUSTOMER_ORDER_PAYMENT_REMIT_EASYLOUNGE = 'erp.CUSTOMER_ORDER_PAYMENT_REMIT_EASYLOUNGE';
    public const CUSTOMER_ORDER_PAYMENT_REMIT_RETAIL_CETELEM = 'erp.CUSTOMER_ORDER_PAYMENT_REMIT_RETAIL_CETELEM';
    public const DELIVERY_NOTE_WRITE = 'erp.DELIVERY_NOTE_WRITE';
    public const CUSTOMER_UPDATE = 'erp.CUSTOMER_UPDATE';
    public const INVENTORY_ADMINISTRATE = 'erp.INVENTORY_ADMINISTRATE';
    public const INVENTORY_CREATE = 'erp.INVENTORY_CREATE';
    public const INVENTORY_DELETE = 'erp.INVENTORY_DELETE';
    public const INVENTORY_READ = 'erp.INVENTORY_READ';
    public const INVENTORY_UPDATE = 'erp.INVENTORY_UPDATE';
    public const MOVE_MISSION_DELETE = 'erp.MOVE_MISSION_DELETE';
    public const MOVE_MISSION_READ = 'erp.MOVE_MISSION_READ';
    public const MOVE_MISSION_UPDATE = 'erp.MOVE_MISSION_UPDATE';
    public const PERMISSION_READ = 'erp.PERMISSION_READ';
    public const PERMISSION_WRITE = 'erp.PERMISSION_WRITE';
    public const PICKING_CREATE = 'erp.PICKING_CREATE';
    public const PRODUCT_LOCATION_UNLINK_DELIVERY = 'erp.PRODUCT_LOCATION_UNLINK_DELIVERY';
    public const PRODUCT_LOCATION_UPDATE = 'erp.PRODUCT_LOCATION_UPDATE';
    public const PRODUCT_LOCATION_UPDATE_WITH_SUPPLIER_ORDER = 'erp.PRODUCT_LOCATION_UPDATE_WITH_SUPPLIER_ORDER';
    public const PRODUCT_MOVE_CREATE = 'erp.PRODUCT_MOVE_CREATE';
    public const PRODUCT_STOCK_READ = 'erp.PRODUCT_STOCK_READ';
    public const QUOTE_DISCOUNT_ADMIN = 'erp.QUOTE_DISCOUNT_ADMIN';
    public const QUOTE_WRITE = 'erp.QUOTE_WRITE';
    public const QUOTE_SHIPMENT_METHOD_CUSTOMIZE = 'erp.QUOTE_SHIPMENT_METHOD_CUSTOMIZE';
    public const QUOTE_SUBTYPE_COMMUNICATION_SELECT = 'erp.QUOTE_SUBTYPE_COMMUNICATION_SELECT';
    public const QUOTE_SUBTYPE_EVENT_SELECT = 'erp.QUOTE_SUBTYPE_EVENT_SELECT';
    public const QUOTE_SUBTYPE_INTERNAL_USE_SELECT = 'erp.QUOTE_SUBTYPE_INTERNAL_USE_SELECT';
    public const QUOTE_SUBTYPE_INTRAGROUP_SELECT = 'erp.QUOTE_SUBTYPE_INTRAGROUP_SELECT';
    public const QUOTE_SUBTYPE_LOAN_SELECT = 'erp.QUOTE_SUBTYPE_LOAN_SELECT';
    public const QUOTE_SUBTYPE_MARKETING_GAME_SELECT = 'erp.QUOTE_SUBTYPE_MARKETING_GAME_SELECT';
    public const QUOTE_SUBTYPE_PARTNER_SALE_SELECT = 'erp.QUOTE_SUBTYPE_PARTNER_SALE_SELECT';
    public const QUOTE_SUBTYPE_STAFF_SALE_SELECT = 'erp.QUOTE_SUBTYPE_STAFF_SALE_SELECT';
    public const RETAIL_STORE_ALL_COMMISSIONS_READ = 'erp.RETAIL_STORE_ALL_COMMISSIONS_READ';
    public const RETAIL_STORE_COMMISSIONS_READ = 'erp.RETAIL_STORE_COMMISSIONS_READ';
    public const ROLE_READ = 'erp.ROLE_READ';
    public const ROLE_WRITE = 'erp.ROLE_WRITE';
    public const SALES_PERIOD_WRITE = 'erp.SALES_PERIOD_WRITE';
    public const SHIPMENT_READ = 'erp.SHIPMENT_READ';
    public const STATISTICS_REPORT_READ = 'erp.STATISTICS_REPORT_READ';
    public const STATISTICS_REPORT_KIBANA_READ = 'erp.STATISTICS_REPORT_KIBANA_READ';
    public const STATISTICS_GLOBAL_READ = 'erp.STATISTICS_GLOBAL_READ';
    public const STOCK_MOVE_BUY_PRICE_UPDATE = 'erp.STOCK_MOVE_BUY_PRICE_UPDATE';
    public const STORE_OPENING_HOURS_UPDATE = 'erp.STORE_OPENING_HOURS_UPDATE';
    public const SUBCATEGORY_UPDATE = 'erp.SUBCATEGORY_UPDATE';
    public const SUPPLIER_ORDER_UPDATE = 'erp.SUPPLIER_ORDER_UPDATE';
    public const SYSTEM_ADMINISTRATION_READ = 'erp.SYSTEM_ADMINISTRATION_READ';
    public const SYSTEM_ADMINISTRATION_UPDATE = 'erp.SYSTEM_ADMINISTRATION_UPDATE';
    public const TEMPORARY_FILE_UPLOAD = 'erp.TEMPORARY_FILE_UPLOAD';
    public const TRANSFER_CREATE = 'erp.TRANSFER_CREATE';
    public const RESET_PRINTING = 'erp.RESET_PRINTING';
    public const UNLOCK_DELIVERY_NOTE = 'erp.UNLOCK_DELIVERY_NOTE';
}
