<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\ReleaseNote\Manager;

class ReleaseNoteIoResponseFormatter
{
    private array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function format(): array
    {
        $formatted_results = [];

        foreach ($this->data as $release_note) {
            $extracted_notes = [];

            foreach ($release_note['notes'] as $note) {
                foreach ($note['notes'] as $item) {
                    // Expected formatted result keys
                    $formatted = [
                        'release_note_id' => null,
                        'subject' => null,
                        'description' => null,
                    ];

                    // Extract the release note id if any
                    preg_match(
                        '#http.*son-video\.releasenotes\.io.*\/(?<release_note_id>\S{5}?)(?:-.*)?"#',
                        $item['title'],
                        $results
                    );
                    if (isset($results['release_note_id'])) {
                        $formatted['release_note_id'] = $results['release_note_id'];
                    }

                    // Extract the "tagged" subject if any
                    preg_match('#<code>(?<subject>.*?)<#', $item['title'], $results);
                    if (isset($results['subject'])) {
                        $formatted['subject'] = $results['subject'];
                    }

                    // transform <a> html tag into markdown
                    if (!isset($formatted['release_note_id'])) {
                        $markdown_title = preg_replace(
                            '/<a [^>]*href="([^"]*)"[^>]*>([^<]+)<\/a>/i',
                            '[$2]($1)',
                            $item['title']
                        );
                        $formatted['description'] = trim(
                            str_replace($formatted['subject'] ?? '', '', strip_tags($markdown_title))
                        );
                    } else {
                        // Remove HTML and strip the extracted tagged subject
                        $formatted['description'] = trim(
                            str_replace($formatted['subject'] ?? '', '', strip_tags($item['title']))
                        );
                    }

                    // Clean up extracted subject
                    if (!is_null($formatted['subject'])) {
                        $formatted['subject'] = trim(str_replace(':', '', $formatted['subject']));
                    }

                    // Map in proper object key (easier to use in JSON later)
                    $extracted_notes[$note['display_label']][] = $formatted;
                }
            }

            // Map to our cached column names
            $formatted_results[] = [
                'release_note_id' => $release_note['id'],
                'type' => $release_note['type'],
                'status' => $release_note['status'],
                'released_at' => \DateTime::createFromFormat('U', $release_note['released_at'])->format('Y-m-d H:i:s'),
                'title' => $release_note['title'],
                'description' => $release_note['description'] ?? '',
                'notes' => $release_note['notes'],
                'extracted_notes' => $extracted_notes,
                'tags' => $release_note['tags'],
            ];
        }

        return $formatted_results;
    }
}
