<?php

namespace SonVideo\Erp\CustomerOrder\Manager\CreationDataMapper;

use SonVideo\Erp\CustomerOrder\Referential\CustomerOrderOrigin;
use SonVideo\Erp\CustomerOrder\Referential\CustomerOrderTag;
use SonVideo\Erp\Referential\Payment\PaymentMean;
use SonVideo\Erp\Referential\SalesChannel;

class AmazonEsCreationDataMapper extends AbstractMarketplaceCreationDataMapper
{
    protected const CUSTOMER_ORDER_ORIGIN = CustomerOrderOrigin::AMAZON_ES;
    protected const PAYMENT_MEAN = PaymentMean::AMAZON_ES;

    public function canHandle(string $key): bool
    {
        return self::CUSTOMER_ORDER_ORIGIN === $key;
    }

    public function overloadData($data): array
    {
        $data['sales_channel_id'] = SalesChannel::AMAZON_ES;

        return $data;
    }

    public function getTagsLines(int $customer_order_id, array $data): array
    {
        $tags_lines = [];

        if (isset($data['is_amazon_business']) && $data['is_amazon_business']) {
            $tags_lines[] = $this->getTagBusiness($customer_order_id, $data['is_excluding_tax']);
        }

        $tags_lines[] = [
            'customer_order_id' => $customer_order_id,
            'tag_id' => CustomerOrderTag::SOURCE_AMAZON_ES,
        ];

        return $tags_lines;
    }

    /** @return array{customer_order_id: int, tag_id: string, meta: string|false} */
    private function getTagBusiness(int $customer_order_id, $is_excluding_tax): array
    {
        return [
            'customer_order_id' => $customer_order_id,
            'tag_id' => CustomerOrderTag::AMAZON_BUSINESS,
            'meta' => $is_excluding_tax ? json_encode(['HT' => true]) : null,
        ];
    }
}
