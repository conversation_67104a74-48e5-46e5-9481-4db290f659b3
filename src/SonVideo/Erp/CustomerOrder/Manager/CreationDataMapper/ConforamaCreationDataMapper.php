<?php

namespace SonVideo\Erp\CustomerOrder\Manager\CreationDataMapper;

use SonVideo\Erp\CustomerOrder\Referential\CustomerOrderOrigin;
use SonVideo\Erp\CustomerOrder\Referential\CustomerOrderTag;
use SonVideo\Erp\Referential\Payment\PaymentMean;
use SonVideo\Erp\Referential\SalesChannel;

class ConforamaCrea<PERSON>DataMapper extends AbstractMarketplaceCreationDataMapper
{
    protected const CUSTOMER_ORDER_ORIGIN = CustomerOrderOrigin::CONFORAMA;
    protected const PAYMENT_MEAN = PaymentMean::CONFORAMA;

    public function canHandle(string $key): bool
    {
        return self::CUSTOMER_ORDER_ORIGIN === $key;
    }

    public function overloadData($data): array
    {
        $data['sales_channel_id'] = SalesChannel::CONFORAMA;

        return $data;
    }

    public function getTagsLines(int $customer_order_id, array $data): array
    {
        return [
            [
                'customer_order_id' => $customer_order_id,
                'tag_id' => CustomerOrderTag::SOURCE_CONFORAMA,
            ],
        ];
    }
}
