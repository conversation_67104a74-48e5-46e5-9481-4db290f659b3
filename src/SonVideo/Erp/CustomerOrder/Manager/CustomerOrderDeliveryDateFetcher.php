<?php

namespace SonVideo\Erp\CustomerOrder\Manager;

use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\Carrier\Dto\ShipmentMethod\EligibilityEnvelopeDto;
use SonVideo\Erp\Carrier\Dto\ShipmentMethod\EligibilityEnvelopeItemDto;
use SonVideo\Erp\Carrier\Dto\ShipmentMethod\EligibilityEnvelopeShippingAddressDto;
use SonVideo\Erp\Carrier\Manager\Eligibility\ShipmentMethodResolver;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderCreationContextEntity;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderProductCreationContextEntity;
use SonVideo\Erp\Referential\Product;

class CustomerOrderDeliveryDateFetcher
{
    private ShipmentMethodResolver $shipment_method_resolver;
    private ArticleRepository $article_repository;

    public function __construct(ShipmentMethodResolver $shipment_method_resolver, ArticleRepository $article_repository)
    {
        $this->shipment_method_resolver = $shipment_method_resolver;
        $this->article_repository = $article_repository;
    }

    public function fetchDeliveryDate(
        CustomerOrderCreationContextEntity $customer_order_creation_context,
        string $country_code
    ): ?\DateTimeInterface {
        $articles = array_filter(
            $customer_order_creation_context->products,
            static fn (CustomerOrderProductCreationContextEntity $product) => Product::TYPE_ARTICLE === $product->type
        );

        $shipping_delay = $this->article_repository->getMaxShippingDelay(array_column($articles, 'product_id'));
        if (null === $shipping_delay) {
            return null;
        }

        $delay = $this->shipment_method_resolver->getDelay(
            $customer_order_creation_context->shipment_method_id,
            $this->buildEnvelope($customer_order_creation_context, $articles, $country_code, $shipping_delay)
        );

        return $delay ? (new \DateTime())->modify(sprintf('+%d days', $delay)) : null;
    }

    /** @param CustomerOrderProductCreationContextEntity[] $articles */
    private function buildEnvelope(
        CustomerOrderCreationContextEntity $customer_order_creation_context,
        array $articles,
        string $country_code,
        int $shipping_delay
    ): EligibilityEnvelopeDto {
        $envelope = new EligibilityEnvelopeDto();

        $envelope->items = array_map(
            fn (CustomerOrderProductCreationContextEntity $product_item) => $this->buildEnvelopeItem(
                $product_item,
                'non' !== $customer_order_creation_context->is_excluding_tax
            ),
            $articles
        );

        $envelope->shipping_address = $this->buildEnveloppeShippingAddress(
            $customer_order_creation_context,
            $country_code
        );

        $envelope->shipping_delay = $shipping_delay;

        return $envelope;
    }

    private function buildEnvelopeItem(
        CustomerOrderProductCreationContextEntity $product_item,
        bool $is_excluding_tax
    ): EligibilityEnvelopeItemDto {
        $dto = new EligibilityEnvelopeItemDto();
        $dto->sku = $product_item->sku;
        $dto->quantity = $product_item->quantity;
        $dto->price_vat_excluded = $is_excluding_tax
            ? $product_item->selling_price_tax_included
            : $product_item->selling_price_tax_included / (1 + $product_item->vat);
        $dto->price = $product_item->selling_price_tax_included;
        $dto->total_price = $product_item->selling_price_tax_included * $product_item->quantity;

        return $dto;
    }

    private function buildEnveloppeShippingAddress(
        CustomerOrderCreationContextEntity $creation_context,
        string $country_code
    ): EligibilityEnvelopeShippingAddressDto {
        $dto = new EligibilityEnvelopeShippingAddressDto();
        $dto->title = $creation_context->shipping_address_civility;
        $dto->firstname = $creation_context->shipping_address_firstname;
        $dto->lastname = $creation_context->shipping_address_lastname;
        $dto->cellphone = $creation_context->shipping_address_cellphone;
        $dto->city = $creation_context->shipping_address_city;
        $dto->postal_code = $creation_context->shipping_address_postal_code;
        $dto->address = $creation_context->shipping_address_address;
        $dto->country_code = $country_code;

        return $dto;
    }
}
