<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Entity;

use App\DataLoader\Type\CommaSeparatedListType;
use App\Entity\AbstractEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\CommentEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\CustomerOrderPaymentEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\CustomerOrderProductEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\DeliveryNoteEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\InvoiceEntity;
use SonVideo\Erp\CustomerOrder\Entity\Children\LastInternalCommentEntity;

class CustomerOrderEntity extends AbstractEntity
{
    public const TYPE_CUSTOMER_ORDER = 'commande';
    public const TYPE_QUOTE = 'devis';
    public const TYPE_PROMOCODE = 'promocode';

    public int $customer_order_id;

    public int $customer_id;

    public \DateTimeInterface $created_at;

    /** @var array|CommaSeparatedListType|null */
    public ?array $statuses = null;

    public string $computed_status;

    public float $amount_all_tax_included;

    public ?string $carrier_name = null;

    public ?string $store_label = null;

    /** @var string One of OrderOrigin values */
    public string $origin;

    public string $original_customer_order_id;

    public ?string $payment_fraud_detection = null;

    public ?string $source = null;

    public ?string $source_group = null;

    /** @var CustomerOrderPaymentEntity[]|null */
    public ?array $payments = [];

    /** @var CustomerOrderProductEntity[]|null */
    public ?array $articles = [];

    /** @var DeliveryNoteEntity[]|null */
    public ?array $delivery_notes = [];

    /** @var InvoiceEntity[]|null */
    public array $invoices = [];

    /** @var LastInternalCommentEntity[]|null */
    public array $last_internal_comment = [];

    /** @var CommentEntity[]|null */
    public array $last_customer_comment = [];

    public ?string $computed_customer_firstname = null;

    public ?string $computed_customer_lastname = null;

    public ?string $overdue_status = null;

    public ?\DateTimeInterface $initial_estimated_delivery_date = null;

    public ?\DateTimeInterface $current_estimated_shipping_date = null;

    public ?\DateTimeInterface $shipping_date_updated_at = null;
}
