<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Entity\Children;

use App\Entity\AbstractEntity;

class InvoiceEntity extends AbstractEntity
{
    public int $customer_order_id;

    public int $invoice_id;

    public string $invoice_type;

    public ?int $delivery_note_id = null;

    public \DateTimeInterface $created_at;
}
