<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Entity\CreationContext;

use App\Entity\AbstractEntity;

class CustomerOrderProductCreationContextEntity extends AbstractEntity
{
    public int $customer_order_id;

    public string $sku;

    public int $product_id;

    public string $type;

    public int $quantity;

    public float $selling_price_tax_included;

    public float $vat;

    public string $description;

    public ?string $discount_type = null;

    public float $discount_amount;

    public ?string $discount_description = null;

    public ?float $ecotax_price = null;

    public ?float $sorecop_price = null;

    public ?string $group_type = null;

    public ?string $group_description = null;

    public ?string $warranty_duration_ext = null;

    public ?float $warranty_unit_selling_price_ext = null;

    public ?string $warranty_duration_tb = null;

    public ?float $warranty_unit_selling_price_tb = null;

    public ?string $promo_code = null;
}
