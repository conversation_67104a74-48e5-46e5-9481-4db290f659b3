<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2023 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CustomerOrder\Dto\CreationContext;

use OpenApi\Annotations as OA;
use Symfony\Component\Validator\Constraints as Assert;

final class CustomerOrderChronoPreciseAppointmentCreationContextDto
{
    /**
     * @OA\Property(example="2023-03-07 16:00:00")
     *
     * @Assert\NotBlank()
     * @Assert\DateTime()
     */
    public \DateTimeInterface $time_slot_start_date;

    /**
     * @OA\Property(example="2023-03-07 16:00:00")
     *
     * @Assert\NotBlank()
     * @Assert\DateTime()
     */
    public \DateTimeInterface $time_slot_end_date;

    /**
     * @OA\Property(example="N1")
     *
     * @Assert\NotBlank()
     */
    public string $time_slot_tariff_level;

    /**
     * @OA\Property(example="01")
     *
     * @Assert\NotBlank()
     */
    public string $product_code;

    /**
     * @OA\Property(example="976")
     *
     * @Assert\NotBlank()
     */
    public string $service_code;
}
