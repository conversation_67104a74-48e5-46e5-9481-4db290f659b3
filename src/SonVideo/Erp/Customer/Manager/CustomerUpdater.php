<?php

namespace SonVideo\Erp\Customer\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use SonVideo\Erp\Customer\Dto\CustomerUpdateRequestDto;
use SonVideo\Erp\Customer\Mysql\Repository\CustomerRepository;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\System\Manager\SystemEventLogger;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class CustomerUpdater
{
    private CustomerRepository $customer_repository;
    private SerializerInterface $serializer;
    private LegacyPdo $legacy_pdo;
    private SystemEventLogger $system_event_logger;
    private CurrentUser $current_user;

    public function __construct(
        LegacyPdo $legacy_pdo,
        SystemEventLogger $system_event_logger,
        CustomerRepository $customer_repository,
        SerializerInterface $serializer,
        CurrentUser $current_user
    ) {
        $this->customer_repository = $customer_repository;
        $this->serializer = $serializer;
        $this->legacy_pdo = $legacy_pdo;
        $this->system_event_logger = $system_event_logger;
        $this->current_user = $current_user;
    }

    /**
     * @throws ExceptionInterface
     * @throws NotFoundException
     * @throws \Exception
     */
    public function update(CustomerUpdateRequestDto $dto): int
    {
        $changes = $this->computeChanges($dto);
        $user = $this->current_user->entity();

        if ([] === $changes) {
            return 0;
        }

        try {
            $this->legacy_pdo->beginTransaction();

            $this->customer_repository->updateCustomerWith($dto);

            $this->system_event_logger->logFormattedMysql(
                'customer.update',
                ['customer' => $dto->customer_id],
                $changes,
                'updated_by',
                $user
            );

            $this->legacy_pdo->commit();
        } catch (\Exception $exception) {
            $this->legacy_pdo->rollBack();

            throw $exception;
        }

        return 1;
    }

    /**
     * @throws ExceptionInterface
     * @throws NotFoundException
     */
    public function computeChanges(CustomerUpdateRequestDto $dto): array
    {
        $new_values = $this->serializer->normalize($dto);
        unset($new_values['customer_id']);

        $old_values = $this->serializer->normalize($this->customer_repository->getById($dto->customer_id));

        $new_values['cnt_numero_tva'] = $new_values['tva_number'];
        unset($new_values['tva_number']);
        $new_values['envoi_email'] = $new_values['accept_marketing_emails'];
        unset($new_values['accept_marketing_emails']);
        $new_values['societe'] = $new_values['company_name'];
        unset($new_values['company_name']);
        $new_values['acceptation_relicat'] = $new_values['balance_acceptance'];
        unset($new_values['balance_acceptance']);

        $changes = [];
        foreach ($new_values as $column => $new_value) {
            $old_value = $old_values[$column];
            if ($old_value !== $new_value) {
                $changes[$column] = ['old' => $old_value, 'new' => $new_value];
            }
        }

        return $changes;
    }
}
