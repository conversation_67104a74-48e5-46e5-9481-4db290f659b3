<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\CreditNote\Entity;

use App\Entity\AbstractEntity;

class CreditNoteEntity extends AbstractEntity
{
    public int $credit_note_id;

    public \DateTimeInterface $created_at;

    public int $customer_order_origin;

    public float $initial_amount;

    public float $available_amount;
}
