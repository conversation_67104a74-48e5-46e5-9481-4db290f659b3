<?php

namespace SonVideo\Erp\Category\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Category\Dto\CreationContext\CategoryContextDto;
use SonVideo\Erp\Category\Dto\UpdateContext\CategoryContextUpdateDto;
use SonVideo\Erp\Category\Entity\CategoryEntity;
use SonVideo\Erp\Category\Exception\CategoryNameAlreadyExistException;
use SonVideo\Erp\Category\Mysql\Repository\CategoryRepository;
use SonVideo\Erp\Domain\Mysql\Repository\DomainRepository;
use SonVideo\Erp\Referential\InternalError;
use SonVideo\Erp\Utility\ConstraintMessageFormatter;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class CategoryManager implements LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    private CategoryRepository $category_repository;

    private SerializerInterface $serializer;

    private ValidatorInterface $validator;

    private QueryBuilder $query_builder;

    private DomainRepository $domain_repository;

    public function __construct(
        CategoryRepository $category_repository,
        SerializerInterface $serializer,
        ValidatorInterface $validator,
        QueryBuilder $query_builder,
        DomainRepository $domain_repository
    ) {
        $this->category_repository = $category_repository;
        $this->serializer = $serializer;
        $this->validator = $validator;
        $this->query_builder = $query_builder;
        $this->domain_repository = $domain_repository;
    }

    /** @throws ExceptionInterface
     * @throws CategoryNameAlreadyExistException
     * @throws NotFoundException
     * @throws InternalErrorException
     * @throws \Exception
     */
    public function create(int $domain_id, string $category_name): int
    {
        $category_name = trim($category_name);

        $exist = $this->checkIfCategoryExist($category_name);

        if ($exist) {
            throw new CategoryNameAlreadyExistException('Category name already exists');
        }

        $this->checkIfDomainExist($domain_id);
        /** @var CategoryContextDto $dto */
        $dto = $this->serializer->denormalize(
            [
                'domain_id' => $domain_id,
                'category_name' => $category_name,
            ],
            CategoryContextDto::class
        );
        $errors = $this->validator->validate($dto);

        if (count($errors) > 0) {
            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('Invalid parameters'), ['validation_errors' => ConstraintMessageFormatter::extract($errors)]);
        }

        return $this->category_repository->create($dto);
    }

    /**
     * @throws NotFoundException
     * @throws InternalErrorException
     * @throws ExceptionInterface
     */
    public function update(CategoryEntity $category): int
    {
        $exist = $this->checkIfCategoryExist($category->category_id);

        if (!$exist) {
            throw new NotFoundException(sprintf('Category does not exist with id "%s".', $category->category_id));
        }

        $this->checkIfDomainExist($category->domain_id);
        $dto = $this->serializer->denormalize(
            [
                'domain_id' => $category->domain_id,
                'category_name' => $category->name,
                'category_id' => $category->category_id,
                'custom_code' => $category->custom_code,
            ],
            CategoryContextUpdateDto::class
        );
        $errors = $this->validator->validate($dto);

        if (count($errors) > 0) {
            throw new InternalErrorException(InternalError::GENERIC, new \InvalidArgumentException('Invalid parameters'), ['validation_errors' => ConstraintMessageFormatter::extract($errors)]);
        }

        return $this->category_repository->update($dto);
    }

    /** @throws NotFoundException */
    private function checkIfDomainExist(int $domain_id): void
    {
        $this->query_builder->setWhere(['domain_id' => ['_eq' => $domain_id]], DomainRepository::COLUMNS_MAPPING);

        $domain = $this->domain_repository->findAllPaginated($this->query_builder)->getResults();

        if ([] === $domain) {
            throw new NotFoundException(sprintf('No domain found with id %s', $domain_id));
        }
    }

    private function checkIfCategoryExist(string $category_name_or_category_id): bool
    {
        $condition = ctype_digit($category_name_or_category_id) ? 'category_id' : 'name';

        $this->query_builder->setWhere(
            [$condition => ['_eq' => $category_name_or_category_id]],
            CategoryRepository::COLUMNS_MAPPING
        );

        $category = $this->category_repository->findAllPaginated($this->query_builder)->getResults();

        return [] !== $category;
    }
}
