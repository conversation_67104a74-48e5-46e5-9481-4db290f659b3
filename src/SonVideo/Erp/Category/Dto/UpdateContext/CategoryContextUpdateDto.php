<?php

namespace SonVideo\Erp\Category\Dto\UpdateContext;

use App\Entity\AbstractEntity;
use Symfony\Component\Validator\Constraints as Assert;

final class CategoryContextUpdateDto extends AbstractEntity
{
    /** @Assert\NotBlank() */
    public string $category_name;

    /** @Assert\NotBlank() */
    public int $domain_id;

    /** @Assert\NotBlank() */
    public int $category_id;

    public string $custom_code;
}
