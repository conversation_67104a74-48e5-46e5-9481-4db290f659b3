<?php

namespace SonVideo\Erp\Mailing\Manager\CustomerOrderAvailable;

use SonVideo\Erp\Mailing\Manager\AbstractEmailDispatcher;
use Symfony\Component\Validator\Constraint;

class CustomerOrderAvailableEmailDispatcher extends AbstractEmailDispatcher
{
    protected const BUSINESS_KEY = 'customer_order_available';

    protected const EMAIL_SENDER = [
        'name' => 'Commande Son-Vidéo.com',
        'email' => '<EMAIL>',
    ];

    /** {@inheritDoc} */
    protected function getValidationRules(array $data): Constraint
    {
        return CustomerOrderAvailableEmailPayloadValidation::rules();
    }
}
