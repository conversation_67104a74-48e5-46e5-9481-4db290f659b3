<?php

namespace SonVideo\Erp\Mailing\Manager\PickupStoreAvailability;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints as Assert;

class PickupStoreAvailabilityPayloadValidation
{
    public static function rules(): Constraint
    {
        return new Assert\Collection([
            'to' => new Assert\Required([new Assert\NotBlank(), new Assert\Email()]),
            'context' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Collection([
                    'customer_order_id' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                    'created_at' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('string'),
                    ]),
                    'customer' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\Collection([
                            'civility' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'firstname' => new Assert\Required([new Assert\NotNull(), new Assert\Type('string')]),
                            'lastname' => new Assert\Required([new Assert\NotNull(), new Assert\Type('string')]),
                        ]),
                    ]),
                    'store' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\Collection([
                            'name' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'city' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'address' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'postal_code' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                            'url' => new Assert\Required([
                                new Assert\NotBlank(),
                                new Assert\NotNull(),
                                new Assert\Type('string'),
                            ]),
                        ]),
                    ]),
                    'articles' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\Type('array'),
                        new Assert\All([
                            new Assert\Collection([
                                'sku' => new Assert\Required([
                                    new Assert\NotBlank(),
                                    new Assert\NotNull(),
                                    new Assert\Type('string'),
                                ]),
                                'quantity' => new Assert\Required([
                                    new Assert\NotBlank(),
                                    new Assert\NotNull(),
                                    new Assert\Type('integer'),
                                ]),
                                'description' => new Assert\Required([new Assert\NotNull(), new Assert\Type('string')]),
                                'picture' => new Assert\Required([new Assert\NotNull(), new Assert\Type('string')]),
                            ]),
                        ]),
                    ]),
                ]),
            ]),
            '_rel' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Collection([
                    'customer_order' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                ]),
            ]),
        ]);
    }
}
