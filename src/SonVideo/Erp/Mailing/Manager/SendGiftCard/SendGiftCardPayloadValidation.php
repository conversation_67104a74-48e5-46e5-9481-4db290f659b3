<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Mailing\Manager\SendGiftCard;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints as Assert;

final class SendGiftCardPayloadValidation
{
    public static function rules(): Constraint
    {
        return new Assert\Collection([
            // recipient of the email
            'to' => new Assert\Required([new Assert\NotBlank(), new Assert\Email()]),
            'from' => new Assert\Optional([
                new Assert\Collection([
                    'name' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('string'),
                    ]),
                    'email' => new Assert\Required([new Assert\NotBlank(), new Assert\Email()]),
                ]),
            ]),
            'cc' => new Assert\Required([new Assert\Type('array')]),
            'subject' => new Assert\Required([new Assert\NotBlank(), new Assert\NotNull(), new Assert\Type('string')]),

            // Context contain the template variables required in the email content
            'context' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Collection([
                    'expired_at' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('string'),
                    ]),
                    'amount' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('numeric'),
                    ]),
                    'card_number' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('string'),
                    ]),
                ]),
            ]),

            // Business keys used for logging after the email has been successfully sent
            '_rel' => new Assert\Optional([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Collection([
                    'customer_order' => new Assert\Required([
                        new Assert\NotBlank(),
                        new Assert\NotNull(),
                        new Assert\Type('integer'),
                    ]),
                ]),
            ]),

            // Indicates which legacy user_id triggered the email
            '_sent_by' => new Assert\Required([
                new Assert\NotBlank(),
                new Assert\NotNull(),
                new Assert\Type('integer'),
            ]),

            // Optional boolean flag
            // When set to false, the email dispatcher will return null instead of a loggable event instance
            // Hence preventing the email to be registered in the system.event table
            '_loggable_event' => new Assert\Optional([new Assert\Type('boolean')]),
        ]);
    }
}
