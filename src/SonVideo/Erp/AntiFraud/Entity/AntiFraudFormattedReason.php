<?php

namespace SonVideo\Erp\AntiFraud\Entity;

final class AntiFraudFormattedReason
{
    private string $name;

    private array $details;

    public function __construct(string $name, array $details = [])
    {
        $this->name = $name;
        $this->details = $details;
    }

    public static function create(string $name): self
    {
        return new self($name);
    }

    public function withDetails(array $details): self
    {
        return new self($this->name, $details);
    }

    public function getDetails(): array
    {
        return $this->details;
    }

    public function toJson(): string
    {
        return json_encode(
            [] !== $this->details
                ? [
                    'name' => $this->name,
                    'details' => $this->details,
                ]
                : [
                    'name' => $this->name,
                ],
            JSON_THROW_ON_ERROR
        );
    }
}
