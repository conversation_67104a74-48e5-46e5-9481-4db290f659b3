<?php

namespace SonVideo\Erp\Product\Mysql\Repository;

use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\NotFoundException;
use App\Sql\AbstractLegacyRepository;
use SonVideo\Erp\Article\Contract\ArticleUpdateRelatedRepositoryInterface;
use SonVideo\Erp\Product\Entity\ProductV2Entity;

/**
 * Class ProductRepository.
 */
class ProductV2Repository extends AbstractLegacyRepository implements ArticleUpdateRelatedRepositoryInterface, LegacyPdoAwareInterface
{
    use LegacyPdoAwareTrait;

    public const COLUMNS_MAPPING = [
        'subcategory_id' => 'p.id_souscategorie',
    ];

    /** @throws NotFoundException */
    public function getOneByIdOrSku(string $id_or_sku): ProductV2Entity
    {
        $condition = ctype_digit($id_or_sku) ? 'p.id_produit' : 'p.reference';

        $sql = <<<SQL
        SELECT p.id_produit AS product_id,
               p.reference AS sku,
               p.type AS product_type,
               a.prix_ecotaxe AS ecotax,
               a.prix_sorecop AS sorecop
        FROM backOffice.produit p
        LEFT JOIN backOffice.article a ON p.id_produit = a.id_produit
        WHERE
            {condition} = :id_or_sku
        SQL;

        $result = $this->legacy_pdo->fetchOne(strtr($sql, ['{condition}' => $condition]), [
            'id_or_sku' => $id_or_sku,
        ]);
        if (false === $result) {
            throw new NotFoundException(sprintf('Product not found with id or sku "%s".', $id_or_sku));
        }

        return $this->data_loader->hydrate($result, ProductV2Entity::class);
    }

    /** @throws NotFoundException */
    public function getOneById(int $article_id, ?array $columns = null): array
    {
        $columns ??= array_keys(self::COLUMNS_MAPPING);

        $sql = <<<SQL
            SELECT {projection}
                FROM backOffice.produit p
                WHERE p.id_produit = :product_id
        SQL;

        $projection_lines = array_map(
            static fn ($column): string => self::COLUMNS_MAPPING[$column] . ' as ' . $column,
            $columns
        );

        $sql = strtr($sql, [
            '{projection}' => "\n" . implode(", \n", $projection_lines) . "\n",
        ]);

        $result = $this->legacy_pdo->fetchOne($sql, ['product_id' => $article_id]);
        if (false === $result) {
            throw new NotFoundException(sprintf('No product found with id %d.', $article_id));
        }

        return $result;
    }

    public function update(int $article_id, array $data): int
    {
        $statement = array_map(
            fn ($value): string => sprintf('%s = :%s', self::COLUMNS_MAPPING[$value], $value),
            array_keys($data)
        );

        $sql = strtr('UPDATE backOffice.produit p SET {statement} WHERE p.id_produit = :product_id', [
            '{statement}' => "\n" . implode(", \n", $statement) . "\n",
        ]);

        return $this->legacy_pdo->fetchAffected($sql, array_merge($data, ['product_id' => $article_id]));
    }
}
