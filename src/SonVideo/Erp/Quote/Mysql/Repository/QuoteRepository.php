<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Mysql\Repository;

use App\Contract\DataLoaderAwareInterface;
use App\Contract\DataLoaderAwareTrait;
use App\Contract\LegacyPdoAwareInterface;
use App\Contract\LegacyPdoAwareTrait;
use App\Exception\SqlErrorMessageException;
use App\Formatter\String\StringFormatter;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Referential\Civility;
use SonVideo\Erp\User\Entity\UserEntity;

final class QuoteRepository implements LegacyPdoAwareInterface, DataLoaderAwareInterface
{
    use LegacyPdoAwareTrait;
    use DataLoaderAwareTrait;

    public const COLUMNS_MAPPING = [
        'quote_id' => 'q.quote_id',
        'type' => 'q.type',
        'created_at' => 'q.created_at',
        'created_by' => 'q.created_by',
        'created_by_username' => 'sgu.username',
        'created_by_firstname' => 'sgup.prenom',
        'created_by_lastname' => 'sgup.nom',
        'modified_at' => 'q.modified_at',
        'customer_id' => 'q.customer_id',
        'customer_email' => 'p.cnt_lvr_email',
        'customer_account_firstname' => 'p.cnt_prenom',
        'customer_firstname' => 'p.prenom',
        'customer_account_lastname' => 'p.cnt_nom',
        'customer_lastname' => 'p.nom',
        'intra_community_vat' => 'p.cnt_numero_tva',
        'expired_at' => 'q.expired_at',
        'valid_until' => 'q.valid_until',
        'message' => 'q.message',
        'billing_address' => 'q.billing_address',
        'shipping_address' => 'q.shipping_address',
        'shipment_method' => 'q.shipment_method',
        'sku' => 'JSON_UNQUOTE(JSON_EXTRACT(qlp.product, \'$.sku\'))',
        'product_id' => 'qlp.product_id',
        'quote_subtype' => 'q.quote_subtype',
        'is_ordered' => 'q.is_ordered',
    ];

    /** @throws \Exception */
    public function findAllPaginated(QueryBuilder $query_builder): Pager
    {
        $sql = <<<SQL
        -- The whole query wrapped in a sub query is required
        -- since the order by clauses work with the aliases in column mapping
        -- plus it also perform better when the result set is big
        SELECT SQL_CALC_FOUND_ROWS *
          FROM
            (
              {base_sql}
              WHERE {conditions}
              GROUP BY q.quote_id
            ) tmp
        {order_by}
        SQL;

        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
            '{conditions}' => $query_builder->getWhere(),
            '{order_by}' => $query_builder->getOrderBy(),
        ]);

        return $this->legacy_pdo->paginateAssoc(
            $query_builder->getOffset(),
            $query_builder->getLimit(),
            $sql,
            $query_builder->getWhereParameters()
        );
    }

    /** Find quote entity by id */
    public function findById(int $quote_id): ?QuoteEntity
    {
        $sql = <<<SQL
        {base_sql}
        WHERE q.quote_id = :quote_id
        GROUP BY q.quote_id
        SQL;
        $sql = strtr($sql, [
            '{base_sql}' => $this->getBaseSql(),
        ]);

        // Do not use slave connection because it can have problems with transaction while cloning quote
        $quote_data = $this->legacy_pdo->fetchOne($sql, ['quote_id' => $quote_id]);

        return $quote_data ? $this->data_loader->hydrate($quote_data, QuoteEntity::class) : null;
    }

    public function fetchQuoteLines(array $quote_ids): array
    {
        $sql = <<<SQL
        SELECT
          ql.quote_id,
          ql.quote_line_id,
          ql.display_order,
          IF(qlp.quote_line_product_id IS NOT NULL, 'product', 'section') AS type,
          CASE
            WHEN qlp.quote_line_product_id IS NOT NULL
              THEN JSON_OBJECT(
                'quote_line_product_id', qlp.quote_line_product_id,
                'product_id', qlp.product_id,
                'quantity', qlp.quantity,
                'unit_discount_amount', qlp.unit_discount_amount,
                'product', qlp.product,
                'status', PDT_statut(a.id_produit),
                'delay', a.v_delai_lvr,
                'stock', a.V_quantite_stock,
                'selected_warranties', qlp.selected_warranties,
                'group_brand', m.type_marque_maison,
                'unbasketable_reason', bc.unbasketable_reason
              )
            ELSE JSON_OBJECT(
              'quote_line_section_id', qls.quote_line_section_id,
              'label', qls.label
              ) END AS data
          FROM
            backOffice.quote_line ql
              LEFT JOIN backOffice.quote_line_product qlp ON ql.quote_line_id = qlp.quote_line_id
              LEFT JOIN backOffice.article a ON a.id_produit = qlp.product_id
              LEFT JOIN backOffice.marque m ON a.id_marque = m.id_marque
              LEFT JOIN backOffice.quote_line_section qls ON ql.quote_line_id = qls.quote_line_id
              LEFT JOIN backOffice.batch_catalog bc ON a.id_produit = bc.article_id
          WHERE
            ql.quote_id  IN (:quote_ids)
            AND COALESCE(qlp.quote_line_product_id, qls.quote_line_section_id) IS NOT NULL
          ORDER BY ql.display_order
        SQL;

        return $this->legacy_pdo->fetchAll($sql, ['quote_ids' => $quote_ids]);
    }

    /** Create a new Quote */
    public function create(int $customer_id, int $user_id, array $address = null, string $quote_subtype = null): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.quote (customer_id, created_by, quote_subtype, message, billing_address) VALUE (
            :customer_id,
            :created_by,
            :quote_subtype,
            'Suite à votre demande voici notre offre pour les produits suivants:',
            (
                SELECT JSON_MERGE_PRESERVE(
                    JSON_OBJECT(
                        'country', JSON_OBJECT(
                            'name', p.pays,
                            'country_id', p.id_pays,
                            'country_code', p.code_2_lettres
                        ),
                        'civility', IF(cms.address->'$.title' = 'Mr', "{MR}", "{MRS}")
                    ),
                    JSON_REMOVE(cms.address,'$.country_code', '$.title')
                )
                FROM (SELECT CAST(:billing_address AS JSON) AS address) cms
                INNER JOIN backOffice.pays p ON cms.address->'$.country_code' = p.code_2_lettres
            )
        )
        SQL;

        $sql = strtr($sql, [
            '{MR}' => Civility::MR,
            '{MRS}' => Civility::MRS,
        ]);

        try {
            $this->legacy_pdo->fetchAffected($sql, [
                'customer_id' => $customer_id,
                'created_by' => $user_id,
                'quote_subtype' => $quote_subtype,
                'billing_address' => json_encode($address, JSON_THROW_ON_ERROR),
            ]);
        } catch (\Exception $e) {
            throw new \UnexpectedValueException('Failed to create quote', $e->getCode(), $e);
        }

        return $this->legacy_pdo->lastInsertId();
    }

    /** @throws \Exception */
    public function clone(int $quote_id, string $quote_subtype = null): int
    {
        $sql = <<<SQL
        INSERT INTO backOffice.quote (valid_until,
                                      billing_address,
                                      shipping_address,
                                      shipment_method,
                                      customer_id,
                                      created_by,
                                      quote_subtype)
        SELECT
          valid_until,
          billing_address,
          shipping_address,
          shipment_method,
          customer_id,
          created_by,
          :quote_subtype
          FROM backOffice.quote
          WHERE
            quote_id = :quote_id;
        SQL;
        try {
            $this->legacy_pdo->fetchAffected($sql, ['quote_subtype' => $quote_subtype, 'quote_id' => $quote_id]);
        } catch (\Exception $e) {
            throw new \Exception('Failed to clone quote', $e->getCode(), $e);
        }

        return $this->legacy_pdo->lastInsertId();
    }

    public function fetchCustomerOrders(array $quote_ids): array
    {
        $sql = <<<SQL
        SELECT
          quote_id,
          id_commande AS customer_order_id
        FROM backOffice.commande
        WHERE quote_id IN (:quote_ids)
        ORDER BY id_commande
        SQL;

        return $this->legacy_pdo->fetchAll($sql, ['quote_ids' => $quote_ids]);
    }

    /** @throws SqlErrorMessageException */
    public function exists(int $quote_id): bool
    {
        $result = $this->legacy_pdo->fetchValue(
            <<<SQL
            SELECT EXISTS(
                SELECT quote_id
                FROM backOffice.quote
                WHERE quote_id = :quote_id
            )
            SQL
            ,
            ['quote_id' => $quote_id]
        );

        return (bool) $result;
    }

    public function update(int $quote_id, array $data, int $modified_by = UserEntity::SYSTEM_ID): int
    {
        // add by whom the data has been modified
        $data['modified_by'] = $modified_by;

        $statement = array_map(fn ($value): string => sprintf('%s = :%s', $value, $value), array_keys($data));

        $sql = strtr('UPDATE backOffice.quote SET {statement} WHERE quote_id = :quote_id', [
            '{statement}' => "\n" . implode(", \n", $statement) . "\n",
        ]);
        foreach ($data as $key => $value) {
            // MySQL does not transform arrays to strings
            if (is_array($value) && in_array($key, ['billing_address', 'shipping_address', 'shipment_method'])) {
                $data[$key] = json_encode($value, JSON_THROW_ON_ERROR);
            }

            // Cleanup string
            if (is_array($value) && 'message' === $key) {
                $data[$key] = StringFormatter::transliterate(strip_tags($value));
            }
        }

        return $this->legacy_pdo->fetchAffected($sql, array_merge($data, ['quote_id' => $quote_id]));
    }

    /** removeShipmentMethod. */
    public function removeShipmentMethod(int $quote_id, int $modified_by = UserEntity::SYSTEM_ID): self
    {
        $sql = <<<SQL
        UPDATE backOffice.quote
        SET shipment_method = null,
            modified_by = :modified_by
        WHERE quote_id = :quote_id
        SQL;
        $this->legacy_pdo->fetchAffected($sql, ['modified_by' => $modified_by, 'quote_id' => $quote_id]);

        return $this;
    }

    private function getBaseSql(): string
    {
        return <<<SQL
        SELECT
          q.quote_id,
          q.type,
          q.created_at,
          q.created_by,
          backOffice.GET_COMPUTED_NAME(sgu.username, sgup.prenom, sgup.nom) AS created_by_name,
          q.modified_at,
          q.customer_id,
          p.cnt_lvr_email AS customer_email,
          backOffice.GET_COMPUTED_CUSTOMER_NAME(p.cnt_prenom, p.prenom, p.cnt_nom, p.nom) AS customer_name,
          IF(p.cnt_numero_tva IS NOT NULL AND LENGTH(p.cnt_numero_tva) > 0, p.cnt_numero_tva,
             NULL) AS intra_community_vat,
          q.expired_at,
          q.valid_until,
          q.message,
          q.billing_address,
          q.shipping_address,
          q.shipment_method,
          q.quote_subtype,
          CASE
               WHEN q.is_ordered IS TRUE
                 THEN 'ordered'
               WHEN q.expired_at IS NOT NULL AND q.expired_at <= NOW()
                 THEN 'expired'
               WHEN q.sent_at IS NOT NULL
                 THEN 'sent'
               ELSE 'inactive' END
          AS status,
          COALESCE(bst.taux_tva, bst_default.taux_tva) AS vat_rate,
          SUM(qlp.total_price) + COALESCE(JSON_UNQUOTE(JSON_EXTRACT(q.shipment_method, '$.cost')), 0) AS total_tax_included
          FROM
            backOffice.quote AS q
              INNER JOIN backOffice.prospect p ON q.customer_id = p.id_prospect
              INNER JOIN backOffice.sf_guard_user sgu ON q.created_by = sgu.id
              LEFT JOIN backOffice.sf_guard_user_profile sgup ON sgup.id = sgu.id
              LEFT JOIN backOffice.BO_SYS_tva AS bst
              ON bst.id_pays = coalesce(q.shipping_address -> "$.country.country_id", 67) AND
                 DATE(coalesce(q.sent_at, NOW())) >= bst.date_debut_application AND
                 (DATE(coalesce(q.sent_at, NOW())) <= bst.date_fin_application OR bst.date_fin_application IS NULL)
              LEFT JOIN backOffice.BO_SYS_tva AS bst_default
              ON bst_default.id_pays = 67 AND DATE(coalesce(q.sent_at, NOW())) >= bst_default.date_debut_application AND
                 (DATE(coalesce(q.sent_at, NOW())) <= bst_default.date_fin_application OR
                  bst_default.date_fin_application IS NULL)
              LEFT JOIN backOffice.quote_line ql ON q.quote_id = ql.quote_id
              LEFT JOIN backOffice.quote_line_product qlp ON ql.quote_line_id = qlp.quote_line_id
        SQL;
    }

    public function fetchQuoteToSynchronize(int $limit = 100): array
    {
        $sql = <<<SQL
        SELECT quote_id, modified_at
        FROM backOffice.quote
        WHERE modified_at > backOffice.GET_SYS_VAR_datetime('quote.synchronized_at')
            AND type = 'offer'
            AND expired_at > NOW()
        ORDER BY modified_at
        LIMIT :limit
        SQL;

        return $this->legacy_pdo->fetchAll($sql, ['limit' => $limit]);
    }
}
