<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Erp\Quote\Contract;

use App\Contract\EntityInterface;
use App\DataLoader\MapToEntityTrait;
use App\Exception\NotFoundException;
use SonVideo\Erp\Quote\Entity\QuoteEntity;
use SonVideo\Erp\Quote\Mysql\Repository\QuoteRepository;

trait QuoteRetrieverTrait
{
    use MapToEntityTrait;

    /**
     * Retrieve a quote by its id.
     *
     * @throws NotFoundException
     */
    protected function retrieveQuote(int $quote_id, QuoteRepository $quote_repository): QuoteEntity
    {
        // Do not use slave connection because it can have problems with transaction while cloning quote
        $quote = $quote_repository->findById($quote_id);

        if (!$quote instanceof QuoteEntity) {
            throw new NotFoundException(sprintf('Quote not found with id "%s"', $quote_id));
        }

        return $quote;
    }

    /**
     * Retrieve a quote by its id, with full data hydrated.
     *
     * @return EntityInterface|QuoteEntity
     *
     * @throws NotFoundException
     */
    protected function retrieveFullyHydratedQuote(int $quote_id, QuoteRepository $quote_repository): QuoteEntity
    {
        $quote = $this->retrieveQuote($quote_id, $quote_repository);
        $quote_lines = $quote_repository->fetchQuoteLines([$quote_id]);
        $customer_orders = $quote_repository->fetchCustomerOrders([$quote_id]);

        return $this->hydrateEntity(
            array_merge($quote->toArray(), [
                'quote_line_aggregates' => $quote_lines,
                'customer_order_aggregates' => $customer_orders,
            ]),
            QuoteEntity::class
        );
    }
}
