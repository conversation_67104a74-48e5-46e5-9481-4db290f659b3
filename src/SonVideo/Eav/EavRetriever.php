<?php
/*
 * This file is part of ERP SERVER package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace SonVideo\Eav;

use App\Database\PgErpServer\EavSchema\ProductValueModel;
use App\Database\PgErpServer\EavSchema\SubcategoryModel;
use PommProject\ModelManager\Exception\ModelException;
use SonVideo\Eav\Repository\SubcategoryEavReadRepository;

class EavRetriever
{
    private SubcategoryEavReadRepository $subcategory_eav_read_repository;

    private SubcategoryModel $subcategory_model;

    private ProductValueModel $product_value_model;

    public function __construct(
        SubcategoryEavReadRepository $subcategory_eav_read_repository,
        SubcategoryModel $subcategory_model,
        ProductValueModel $product_value_model
    ) {
        $this->subcategory_eav_read_repository = $subcategory_eav_read_repository;
        $this->subcategory_model = $subcategory_model;
        $this->product_value_model = $product_value_model;
    }

    /**
     * @return array{article: mixed}
     *
     * @throws ModelException
     */
    public function fetchFacetsWithSku(string $sku): array
    {
        $product_info = $this->subcategory_eav_read_repository->findIdFromArticleSku($sku);

        if (false === $product_info) {
            throw new \InvalidArgumentException(sprintf('Could not load product information\'s from sku : "%s"', $sku));
        }

        $data = $this->subcategory_model->findArticleFacetsByIdAndSkus($product_info->subcategory_id, [
            $product_info->sku,
        ]);

        if (!isset($data[0])) {
            throw new \Exception(sprintf('Could not load facets for sku : "%s"', $sku));
        }

        return ['article' => $data[0]];
    }

    public function fetchAttributeValuesForSku(string $sku, int $attribute_id): array
    {
        return $this->product_value_model->findAttributeValuesForSku($sku, $attribute_id) ?? [];
    }
}
