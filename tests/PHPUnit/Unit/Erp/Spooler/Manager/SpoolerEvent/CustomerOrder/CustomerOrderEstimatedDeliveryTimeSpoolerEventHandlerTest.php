<?php

namespace PHPUnit\Unit\Erp\Spooler\Manager\SpoolerEvent\CustomerOrder;

use App\Adapter\Serializer\SerializerInterface;
use SonVideo\Erp\Spooler\Entity\SpoolerEvent;
use SonVideo\Erp\Spooler\Manager\SpoolerEvent\CustomerOrder\CustomerOrderEstimatedDeliveryTimeSpoolerEventHandler;
use SonVideo\Erp\Spooler\Mysql\Repository\CustomerOrderExpectedDeliveryTimeSpoolerRepository;
use SonVideo\Erp\Spooler\Mysql\Repository\SpoolerReadRepository;
use SonVideo\Erp\Spooler\Mysql\Repository\SpoolerWriteRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderEstimatedDeliveryTimeSpoolerEventHandlerTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Tests the handler when there are no awaiting events in the spooler. */
    public function test_when_there_are_no_awaiting_events_in_the_spooler(): void
    {
        $spooler_read_repository = $this->createMock(SpoolerReadRepository::class);
        $spooler_read_repository
            ->expects($this->once())
            ->method('fetchQueuedEvents')
            ->willReturn([]);

        $spooler_write_repository = $this->createMock(SpoolerWriteRepository::class);
        $spooler_write_repository->expects($this->once())->method('lockEvents');
        $spooler_write_repository->expects($this->once())->method('deleteLockedEvents');
        $spooler_write_repository->expects($this->never())->method('deleteEvent');

        $customer_order_expected_delivery_time_spooler_repository = $this->createMock(
            CustomerOrderExpectedDeliveryTimeSpoolerRepository::class
        );
        $customer_order_expected_delivery_time_spooler_repository
            ->expects($this->once())
            ->method('transferLockedProductFromEvents');
        $customer_order_expected_delivery_time_spooler_repository
            ->expects($this->never())
            ->method('updateDeliveryTimeForProducts');
        $customer_order_expected_delivery_time_spooler_repository
            ->expects($this->never())
            ->method('updateDeliveryTimeForCustomerOrder');

        $tested_class = new CustomerOrderEstimatedDeliveryTimeSpoolerEventHandler(
            $spooler_read_repository,
            $spooler_write_repository,
            $customer_order_expected_delivery_time_spooler_repository
        );
        $tested_class->setLogger(self::$container->get('logger'));
        $tested_class->handle();
    }

    /** Tests the handler when there are some awaiting events in the spooler. */
    public function test_when_there_are_some_awaiting_events_in_the_spooler(): void
    {
        $spooler_read_repository = $this->createMock(SpoolerReadRepository::class);
        $spooler_read_repository
            ->expects($this->once())
            ->method('fetchQueuedEvents')
            ->willReturn(
                self::$container->get(SerializerInterface::class)->denormalize(
                    [
                        [
                            'action' => '',
                            'target' => '44',
                            'updated_at' => (new \DateTimeImmutable('now'))->format('Y-m-d H:i:s'),
                        ],
                        [
                            'action' => '',
                            'target' => '666',
                            'updated_at' => (new \DateTimeImmutable('now'))->format('Y-m-d H:i:s'),
                        ],
                    ],
                    SpoolerEvent::class . '[]'
                )
            );

        $spooler_write_repository = $this->createMock(SpoolerWriteRepository::class);
        $spooler_write_repository->expects($this->once())->method('lockEvents');
        $spooler_write_repository->expects($this->once())->method('deleteLockedEvents');
        $spooler_write_repository->expects($this->exactly(2))->method('deleteEvent');

        $customer_order_expected_delivery_time_spooler_repository = $this->createMock(
            CustomerOrderExpectedDeliveryTimeSpoolerRepository::class
        );
        $customer_order_expected_delivery_time_spooler_repository
            ->expects($this->once())
            ->method('transferLockedProductFromEvents');
        $customer_order_expected_delivery_time_spooler_repository
            ->expects($this->exactly(2))
            ->method('updateDeliveryTimeForProducts');
        $customer_order_expected_delivery_time_spooler_repository
            ->expects($this->exactly(2))
            ->method('updateDeliveryTimeForCustomerOrder');

        $tested_class = new CustomerOrderEstimatedDeliveryTimeSpoolerEventHandler(
            $spooler_read_repository,
            $spooler_write_repository,
            $customer_order_expected_delivery_time_spooler_repository
        );
        $tested_class->setLogger(self::$container->get('logger'));
        $tested_class->handle();
    }
}
