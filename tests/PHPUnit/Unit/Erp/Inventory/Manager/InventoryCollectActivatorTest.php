<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2022 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\Inventory\Manager;

use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Inventory\Manager\InventoryCollectActivator;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryCollectReadRepository;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryCollectWriteRepository;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryReadRepository;
use SonVideo\Erp\Inventory\Mysql\Repository\InventoryWriteRepository;
use SonVideo\Erp\Quote\Exception\CloneQuoteException;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class InventoryCollectActivatorTest extends KernelTestCase
{
    /** @var InventoryCollectWriteRepository|\PHPUnit\Framework\MockObject\MockObject */
    protected $inventory_collect_write_repository_mock;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'inventory/inventories.sql',
            'inventory/inventory_activate.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): InventoryCollectActivator
    {
        return self::$container->get(InventoryCollectActivator::class);
    }

    /**
     * Tests the activate method with successful activation.
     *
     * @throws NotFoundException
     * @throws CloneQuoteException
     */
    public function test_activate_success(): void
    {
        $inventory_id = 3;
        $first_collect = 6;
        $second_collect = 7;

        // Value before activate
        $inventory = $this->fetchInventory($inventory_id);
        $this->assertNull($inventory['collecte_active_id']);
        $this->assertEquals('created', $inventory['statut']);

        $this->getTestedInstance()->activate($inventory_id, $first_collect);

        // Value after activate
        $inventory = $this->fetchInventory($inventory_id);
        $this->assertEquals($first_collect, (int) $inventory['collecte_active_id']);
        $this->assertEquals('on-going', $inventory['statut']);

        $this->getTestedInstance()->activate($inventory_id, $second_collect);

        // Value after second activate
        $inventory = $this->fetchInventory($inventory_id);
        $this->assertEquals($second_collect, (int) $inventory['collecte_active_id']);
        $this->assertEquals('on-going', $inventory['statut']);
    }

    /** Tests the activate method with error cases. */
    public function test_activate_error(): void
    {
        $inventory_id = 4;
        $first_collect = 9;

        $tested_class = $this->getInstanceWithMockService();

        // Value before activate
        $inventory = $this->fetchInventory($inventory_id);
        $this->assertNull($inventory['collecte_active_id']);
        $this->assertEquals('created', $inventory['statut']);

        // Test first error case
        $this->expectException(\Exception::class);
        $tested_class->activate($inventory_id, $first_collect);
    }

    /** Tests the activate method with another error case. */
    public function test_activate_error_with_existing_collect(): void
    {
        $inventory_id = 5;
        $first_collect = 12;

        $tested_class = $this->getInstanceWithMockService();

        // Value before activate
        $inventory = $this->fetchInventory($inventory_id);
        $this->assertEquals($first_collect, (int) $inventory['collecte_active_id']);
        $this->assertEquals('on-going', $inventory['statut']);

        // Test second error case
        $this->expectException(\Exception::class);
        $tested_class->activate($inventory_id, $first_collect);
    }

    /** Gets an instance with mock services. */
    protected function getInstanceWithMockService(): InventoryCollectActivator
    {
        $this->inventory_collect_write_repository_mock = $this->createMock(InventoryCollectWriteRepository::class);
        $this->inventory_collect_write_repository_mock->method('activate')->willThrowException(new \Exception('error'));

        return new InventoryCollectActivator(
            self::$container->get(InventoryReadRepository::class),
            self::$container->get(InventoryWriteRepository::class),
            $this->inventory_collect_write_repository_mock,
            self::$container->get(InventoryCollectReadRepository::class)
        );
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /** Fetches an inventory by ID. */
    protected function fetchInventory(int $inventory_id): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.BO_INV_inventaire
        WHERE id = :inventory_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['inventory_id' => $inventory_id]);
    }
}
