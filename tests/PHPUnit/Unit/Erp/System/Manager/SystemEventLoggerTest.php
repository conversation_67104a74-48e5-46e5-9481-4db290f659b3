<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PHPUnit\Unit\Erp\System\Manager;

use App\Database\PgErpServer\SystemSchema\EventModel;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\System\Manager\SystemEventLogger;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SystemEventLoggerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): SystemEventLogger
    {
        return self::$container->get(SystemEventLogger::class);
    }

    /** Tests the log method. */
    public function test_log(): void
    {
        /** @var EventModel $event_model */
        $event_model = self::$container->get('pomm.default_session')->getModel(EventModel::class);

        $logger = $this->getTestedInstance();

        // Nothing is logged
        $rows = $event_model->findWhere('TRUE', []);
        $this->assertEquals(0, $rows->count());

        $logger->log(null);

        $rows = $event_model->findWhere('TRUE', []);
        $this->assertEquals(0, $rows->count());

        // Log successfully PG
        $rows = $event_model->findWhere('TRUE', []);
        $this->assertEquals(0, $rows->count());

        $logger->log(
            new LoggableSystemEvent(LoggableSystemEvent::SYSTEM_ERP_PG, 'dummy', [
                '_rel' => ['customer' => 1],
                'foo' => 'bar',
            ])
        );

        $rows = $event_model->findWhere('TRUE', []);
        $this->assertEquals(1, $rows->count());
    }
}
