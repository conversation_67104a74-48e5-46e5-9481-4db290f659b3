<?php

namespace PHPUnit\Unit\Erp\PricingStrategy\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\MysqlErp\Repository\CompetitorPricingRepository;
use App\Database\Orm\MysqlErp\Repository\Entity\LowestCompetitorPricing;
use App\Exception\InternalServerErrorException;
use App\Exception\NotFoundException;
use App\Exception\SqlErrorMessageException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\PhpUnitPommHelperTrait;
use App\Tests\Utils\SecurityInTestHelper;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\PricingStrategy\Entity\PricingStrategyEntity;
use SonVideo\Erp\PricingStrategy\Entity\PricingStrategyProductEntity;
use SonVideo\Erp\PricingStrategy\Manager\PricingStrategyEngine;
use SonVideo\Erp\Referential\SalesChannel;
use SonVideo\Erp\SalesChannel\Exception\MarginValidationException;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PricingStrategyEngineTest extends KernelTestCase
{
    use PhpUnitPommHelperTrait;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users_backoffice.sql', 'pricing_strategy/pricing_strategy_engine.sql']);
    }

    protected function setUp(): void
    {
        self::bootKernel();

        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::ADMIN_ACCOUNT);
    }

    protected function getTestedInstance(): PricingStrategyEngine
    {
        return self::$container->get(PricingStrategyEngine::class);
    }

    private function find(array $products, int $product_id): ?PricingStrategyProductEntity
    {
        foreach ($products as $product) {
            if ($product->article_id === $product_id) {
                return $product;
            }
        }

        return null;
    }

    /**
     * @throws InternalServerErrorException
     * @throws MarginValidationException
     * @throws NotFoundException
     * @throws SqlErrorMessageException
     */
    public function test_strategy_engine_run_on_weekday(): void
    {
        $engine = $this->getTestedInstance();

        // Always wednesday
        $engine->setBaseDate(new \DateTime('2025-05-15'));
        $engine->setLogger(self::$container->get(LoggerInterface::class));
        $engine->setSerializer(self::$container->get(SerializerInterface::class));

        $result = $engine->run(78);

        $this->assertCount(5, $result);

        $product = $this->find($result, 13896);
        $this->assertEquals(13896, $product->article_id);
        $this->assertCount(2, $product->new_prices);

        // Test easylounge prices
        $easylounge = $product->new_prices[0];
        $this->assertEqualsWithDelta(45.0, $easylounge->current_selling_price_tax_included, 0.001);
        $this->assertEqualsWithDelta(
            round(28.691666666667, 10),
            round($easylounge->selling_price_tax_excluded, 10),
            0.001
        );
        $this->assertEqualsWithDelta(round(34.43, 10), round($easylounge->selling_price_tax_included, 10), 0.001);
        $this->assertEqualsWithDelta(round(34.43, 10), round($easylounge->selling_price_if_cheapest, 10), 0.001);
        $this->assertEqualsWithDelta(0.54831460674157, $easylounge->current_margin_rate, 0.001);
        $this->assertEqualsWithDelta(round(0.40760389036251, 10), round($easylounge->margin_rate, 10), 0.001);
        $this->assertEqualsWithDelta(round(0.4076038904, 10), round($easylounge->margin_rate_if_cheapest, 10), 0.001);
        $this->assertEqualsWithDelta(round(11.525, 10), round($easylounge->margin, 10), 0.001);

        // Test cdiscount prices
        $cdiscount = $product->new_prices[1];
        $this->assertEqualsWithDelta(40, $cdiscount->current_selling_price_tax_included, 0.001);
        $this->assertEqualsWithDelta(
            $easylounge->selling_price_tax_excluded,
            $cdiscount->selling_price_tax_excluded,
            0.001
        );
        $this->assertEqualsWithDelta(
            $easylounge->selling_price_tax_included,
            $cdiscount->selling_price_tax_included,
            0.001
        );
        $this->assertEqualsWithDelta(
            $easylounge->selling_price_if_cheapest,
            $cdiscount->selling_price_if_cheapest,
            0.001
        );
        $this->assertEqualsWithDelta(0.49113924050633, $cdiscount->current_margin_rate, 0.001);
        $this->assertEqualsWithDelta($easylounge->margin_rate, $cdiscount->margin_rate, 0.001);
        $this->assertEqualsWithDelta($easylounge->margin_rate_if_cheapest, $cdiscount->margin_rate_if_cheapest, 0.001);
        $this->assertEqualsWithDelta($easylounge->margin, $cdiscount->margin, 0.001);

        $product = $this->find($result, 81078);
        $this->assertEquals(81078, $product->article_id);
        $this->assertCount(2, $product->new_prices);

        // Test easylounge prices for product 81078
        $easylounge = $product->new_prices[0];
        $this->assertEqualsWithDelta(round(100, 10), round($easylounge->selling_price_tax_excluded, 10), 0.001);
        $this->assertEqualsWithDelta(round(120, 10), round($easylounge->selling_price_tax_included, 10), 0.001);

        // Test cdiscount prices for product 81078
        $cdiscount = $product->new_prices[1];
        $this->assertEqualsWithDelta($easylounge->margin, $cdiscount->margin, 0.001);
        $this->assertEqualsWithDelta($easylounge->margin_rate, $cdiscount->margin_rate, 0.001);
        $this->assertEqualsWithDelta(
            $easylounge->selling_price_tax_excluded,
            $cdiscount->selling_price_tax_excluded,
            0.001
        );
        $this->assertEqualsWithDelta(
            $easylounge->selling_price_tax_included,
            $cdiscount->selling_price_tax_included,
            0.001
        );
        $this->assertEqualsWithDelta($easylounge->margin_rate_if_cheapest, $cdiscount->margin_rate_if_cheapest, 0.001);

        $product = $this->find($result, 128417);
        $this->assertEquals(128417, $product->article_id);
        $this->assertCount(1, $product->new_prices);

        // Test cdiscount prices for product 128417
        $cdiscount = $product->new_prices[0];
        $this->assertEqualsWithDelta(
            round(748.14432989691, 10),
            round($cdiscount->selling_price_tax_excluded, 10),
            0.001
        );
        $this->assertEqualsWithDelta(
            round(897.7731958763, 10),
            round($cdiscount->selling_price_tax_included, 10),
            0.001
        );

        $product = $this->find($result, 81123);
        $this->assertEquals(81123, $product->article_id);
        $this->assertCount(2, $product->new_prices);

        // Test easylounge prices for product 81123
        $easylounge = $product->new_prices[0];
        $this->assertEqualsWithDelta(
            round(340.8333333333, 10),
            round($easylounge->selling_price_tax_excluded, 10),
            0.001
        );
        $this->assertEqualsWithDelta(round(409, 10), round($easylounge->selling_price_tax_included, 10), 0.001);

        // Test cdiscount prices for product 81123
        $cdiscount = $product->new_prices[1];
        $this->assertEqualsWithDelta(
            round(340.8333333333, 10),
            round($cdiscount->selling_price_tax_excluded, 10),
            0.001
        );
        $this->assertEqualsWithDelta(round(409, 10), round($cdiscount->selling_price_tax_included, 10), 0.001);
    }

    public function test_strategy_engine_run_on_weekend(): void
    {
        // Always saturday
        $engine = $this->getTestedInstance();
        $engine->setBaseDate(new \DateTime('2025-05-17'));

        // Test engine run on weekend
        $engine->setLogger(self::$container->get(LoggerInterface::class));
        $engine->setSerializer(self::$container->get(SerializerInterface::class));

        $result = $engine->run(78);
        $this->assertCount(5, $result);

        $product = $this->find($result, 81078);
        $this->assertEquals(81078, $product->article_id);
        $this->assertCount(2, $product->new_prices);

        $easylounge = $product->new_prices[0];
        $this->assertEquals(101.67, round($easylounge->selling_price_tax_excluded, 2));
        $this->assertEquals(122, round($easylounge->selling_price_tax_included, 2));

        $cdiscount = $product->new_prices[1];
        $this->assertEquals($easylounge->selling_price_tax_excluded, $cdiscount->selling_price_tax_excluded);
        $this->assertEquals($easylounge->selling_price_tax_included, $cdiscount->selling_price_tax_included);

        $product = $this->find($result, 13896);
        $this->assertEquals(13896, $product->article_id);
        $this->assertCount(2, $product->new_prices);

        $easylounge = $product->new_prices[0];
        $this->assertEquals(30.36, round($easylounge->selling_price_tax_excluded, 2));
        $this->assertEquals(36.43, round($easylounge->selling_price_tax_included, 2));

        $cdiscount = $product->new_prices[1];
        $this->assertEquals(
            round($easylounge->selling_price_tax_excluded, 2),
            round($cdiscount->selling_price_tax_excluded, 2)
        );
        $this->assertEquals(
            round($easylounge->selling_price_tax_included, 2),
            round($cdiscount->selling_price_tax_included, 2)
        );

        $product = $this->find($result, 128417);
        $this->assertEquals(128417, $product->article_id);
        $this->assertCount(1, $product->new_prices);

        $easylounge = $product->new_prices[0];
        $this->assertEquals(740.51, round($easylounge->selling_price_tax_excluded, 2));
        $this->assertEquals(888.61, round($easylounge->selling_price_tax_included, 2));

        $product = $this->find($result, 81123);
        $this->assertEquals(81123, $product->article_id);
        $this->assertCount(2, $product->new_prices);

        $easylounge = $product->new_prices[0];
        $this->assertEquals(342.5, round($easylounge->selling_price_tax_excluded, 2));
        $this->assertEquals(411, round($easylounge->selling_price_tax_included, 2));

        $cdiscount = $product->new_prices[1];
        $this->assertEquals(342.5, round($cdiscount->selling_price_tax_excluded, 2));
        $this->assertEquals(411, round($cdiscount->selling_price_tax_included, 2));

        // Test products with no competitor
        $result = $engine->run(79);
        $this->assertCount(0, $result);

        // Test strategy with no sales channel
        $result = $engine->run(80);
        $this->assertCount(0, $result);
    }

    public function test_update_strategy_prices(): void
    {
        $engine = $this->getTestedInstance();
        $engine->setLogger(self::$container->get(LoggerInterface::class));
        $engine->setSerializer(self::$container->get(SerializerInterface::class));

        // Test original channel price
        $original_channel_price = $this->getProductPrice(SalesChannel::EASYLOUNGE, 'ARCAMRBLINKNR');
        $this->assertEquals(249, $original_channel_price);

        // Test lowest competitor
        $lowest_competitor = $this->getLowestCompetitorPricing(81, 81078);
        $this->assertEquals(122, $lowest_competitor->selling_price_with_taxes);
        $this->assertEquals('SONVIDEO', $lowest_competitor->competitor_code);

        // Test run with update
        $result = $engine->run(81, false);
        $this->assertCount(3, $result);

        $product = $this->find($result, 81078);

        // Test easylounge prices after update
        $easylounge = $product->new_prices[0];
        $this->assertEquals(
            round($easylounge->selling_price_tax_included, 2),
            $this->getProductPrice($easylounge->sales_channel['sales_channel_id'], $product->sku)
        );
        $this->assertEquals(122, $this->getProductPrice($easylounge->sales_channel['sales_channel_id'], $product->sku));

        // Test cilo prices after update
        $cilo = $product->new_prices[1];
        $this->assertEquals(
            round($cilo->selling_price_tax_included, 2),
            $this->getProductPrice($cilo->sales_channel['sales_channel_id'], $product->sku)
        );

        $product = $this->find($result, 81123);

        // Test cilo prices for product 81123
        $cilo = $product->new_prices[0];
        $this->assertEquals(
            round($cilo->selling_price_tax_included, 2),
            $this->getProductPrice($cilo->sales_channel['sales_channel_id'], $product->sku)
        );

        // Test cdiscount prices for product 81123
        $cdiscount = $product->new_prices[1];
        $this->assertEquals(
            round($cdiscount->selling_price_tax_included, 2),
            $this->getProductPrice($cdiscount->sales_channel['sales_channel_id'], $product->sku)
        );

        // Test system event
        $system_event = $this->fetchLastSystemEvents();
        $payload = json_decode($system_event['payload'], true);

        $this->assertEquals('article.update.sales_channel_product.prices', $system_event['name']);
        $this->assertEquals(['id' => 81], $payload['meta']['pricing_strategy']);
        $this->assertEquals(['price' => 875, 'code' => 'DARTY'], $payload['meta']['lowest_competitor']);
    }

    public function test_get_min_margin(): void
    {
        $strategy = new PricingStrategyEntity();
        $strategy->weekdays_min_margin_rate = 10;
        $strategy->weekend_min_margin_rate = 5;

        // Test weekday before 21:00
        $engine = $this->getTestedInstance();
        $engine->setBaseDate(new \DateTime('2025-02-21 19:50:00'));
        $this->assertEquals(0.1, $engine->getMinMarginRate($strategy));

        // Test weekday after 21:00
        $engine = $this->getTestedInstance();
        $engine->setBaseDate(new \DateTime('2025-02-21 21:00:00'));
        $this->assertEquals(0.05, $engine->getMinMarginRate($strategy));

        // Test weekend before 21:00
        $engine = $this->getTestedInstance();
        $engine->setBaseDate(new \DateTime('2025-02-23 19:50:00'));
        $this->assertEquals(0.05, $engine->getMinMarginRate($strategy));

        // Test weekend after 21:00
        $engine = $this->getTestedInstance();
        $engine->setBaseDate(new \DateTime('2025-02-23 21:00:00'));
        $this->assertEquals(0.1, $engine->getMinMarginRate($strategy));
    }

    public function test_get_increment_amount(): void
    {
        $strategy = new PricingStrategyEntity();
        $strategy->weekdays_increment_amount = 10;
        $strategy->weekend_increment_amount = 5;

        // Test weekday before 21:00
        $engine = $this->getTestedInstance();
        $engine->setBaseDate(new \DateTime('2025-02-21 19:50:00'));
        $this->assertEquals(10, $engine->getIncrementAmount($strategy));

        // Test weekday after 21:00
        $engine = $this->getTestedInstance();
        $engine->setBaseDate(new \DateTime('2025-02-21 21:00:00'));
        $this->assertEquals(5, $engine->getIncrementAmount($strategy));

        // Test weekend before 21:00
        $engine = $this->getTestedInstance();
        $engine->setBaseDate(new \DateTime('2025-02-23 19:50:00'));
        $this->assertEquals(5, $engine->getIncrementAmount($strategy));

        // Test weekend after 21:00
        $engine = $this->getTestedInstance();
        $engine->setBaseDate(new \DateTime('2025-02-23 21:00:00'));
        $this->assertEquals(10, $engine->getIncrementAmount($strategy));
    }

    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    private function getProductPrice(string $sales_channel_id, string $sku): ?float
    {
        $sql = <<<SQL
        SELECT selling_price
        FROM backOffice.sales_channel_product scp
        INNER JOIN backOffice.produit p ON p.reference = :sku
        WHERE p.id_produit = scp.product_id
        AND sales_channel_id = :sales_channel_id;
        SQL;

        return (float) $this->getPdo()->fetchOne($sql, [
            'sales_channel_id' => $sales_channel_id,
            'sku' => $sku,
        ])['selling_price'];
    }

    private function getStrategyCompetitorCodes(int $pricing_strategy_id): array
    {
        $sql = <<<SQL
            SELECT competitor_code
            FROM backOffice.pricing_strategy_competitor
            WHERE pricing_strategy_id = :pricing_strategy_id
        SQL;

        return $this->getPdo()->fetchAll($sql, ['pricing_strategy_id' => $pricing_strategy_id]);
    }

    private function getLowestCompetitorPricing(int $pricing_strategy_id, int $product_id): ?LowestCompetitorPricing
    {
        $competitor_pricing_repo = self::$container->get(CompetitorPricingRepository::class);
        $strategy_competitors = $this->getStrategyCompetitorCodes($pricing_strategy_id);

        return $competitor_pricing_repo->findLowestCompetitorPricing(
            $product_id,
            array_column($strategy_competitors, 'competitor_code')
        );
    }

    private function fetchLastSystemEvents(): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql);
    }
}
