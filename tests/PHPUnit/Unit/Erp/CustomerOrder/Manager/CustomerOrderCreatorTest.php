<?php

namespace PHPUnit\Unit\Erp\CustomerOrder\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Contract\DataLoaderInterface;
use App\Database\Orm\MysqlErp\Repository\SynchronizableTopicReadRepository;
use App\Sql\LegacyPdo;
use App\Tests\Mock\Erp\CustomerOrder\CustomerOrderPayload;
use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\SecurityInTestHelper;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\Customer\Manager\CustomerManager;
use SonVideo\Erp\CustomerOrder\Collection\CustomerOrderCreationDataMapperCollection;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CreationContext\CustomerOrderPaymentCreationContextDto;
use SonVideo\Erp\CustomerOrder\Dto\CustomerOrderBasicInfo;
use SonVideo\Erp\CustomerOrder\Entity\Result\CreatedCustomerOrder;
use SonVideo\Erp\CustomerOrder\Entity\Result\CreatedCustomerOrderWithPaymentAction;
use SonVideo\Erp\CustomerOrder\Exception\CustomerOrderAlreadyExistsException;
use SonVideo\Erp\CustomerOrder\Exception\CustomerOrderRequestPayloadException;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrderCreator as TestedClass;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrderDeliveryDateFetcher;
use SonVideo\Erp\CustomerOrder\Mysql\Repository\CustomerOrderRepository;
use SonVideo\Erp\CustomerOrder\Referential\CustomerOrderOrigin;
use SonVideo\Erp\CustomerOrder\Referential\CustomerOrderTag;
use SonVideo\Erp\CustomerOrderPayment\Dto\CustomerOrderPaymentCreationRequestDto;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Repository\CustomerOrderPaymentWriteRepository;
use SonVideo\Erp\CustomerOrderProduct\Mysql\Repository\CustomerOrderProductRepository;
use SonVideo\Erp\Payment\Entity\PaymentV2CreationPayload;
use SonVideo\Erp\Payment\Manager\PaymentV2CreatorInterface;
use SonVideo\Erp\Payment\Manager\PaymentV2StateInterface;
use SonVideo\Erp\Product\Entity\ProductV2Entity;
use SonVideo\Erp\Quote\Mysql\Repository\QuoteRepository;
use SonVideo\Erp\Referential\PaymentWorkflow;
use SonVideo\Erp\Referential\Product;
use SonVideo\Erp\Referential\Rpc\BoCmsRpcMethodReferential;
use SonVideo\Erp\SvdGiftCard\Manager\SvdGiftCardManager;
use SonVideo\Erp\System\Common\CurrentUser;
use SonVideo\Erp\Task\Mysql\Repository\TaskRepository;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class CustomerOrderCreatorTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
    }

    /**
     * Some original atoum tests were relying on previous test result
     * Which cannot be used with xdebug/coverage (tests run on concurrent mode on CI).
     * For those tests we must make sure that the database is reloaded before running the test.
     *
     * Methinks that this class will need a huge rewrite because now this huge ass test is longer than my life
     */
    protected static function forceReload(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'customer_order/rpc/creator.sql',
        ]);

        PGDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::forceReload();
        self::bootKernel();

        self::$container->get(SecurityInTestHelper::class)->logInAs(SecurityInTestHelper::REGULAR_ACCOUNT);

        // This is the only way to override a service with PHPUnit + Symfony 4.4
        self::$container->set(
            PaymentV2StateInterface::class,
            new class() implements PaymentV2StateInterface {
                public function isValidWithProvidedOrderContext(
                    CustomerOrderCreationContextDto $context_entity
                ): CustomerOrderCreationContextDto {
                    return $context_entity;
                }

                public function isValidWithExistingCustomerOrder(
                    CustomerOrderPaymentCreationRequestDto $request_context,
                    CustomerOrderBasicInfo $customer_order_basic_info
                ): CustomerOrderPaymentCreationRequestDto {
                    return $request_context;
                }

                public function canHandle(string $payment_method_code, int $customer_id): bool
                {
                    return true;
                }
            }
        );

        self::$container->set(
            PaymentV2CreatorInterface::class,
            new class() implements PaymentV2CreatorInterface {
                public function create(CustomerOrderCreationContextDto $order_context, int $customer_order_id): ?array
                {
                    return [];
                }

                public function add(CustomerOrderPaymentCreationRequestDto $request_context): ?array
                {
                    return [];
                }
            }
        );
    }

    protected function getTestedInstance(): TestedClass
    {
        return self::$container->get(TestedClass::class);
    }

    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /** Tests creating a customer order successfully. */
    public function test_create_customer_order_successfully(): void
    {
        $order = $this->getSerializer()->denormalize(
            array_merge(CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode(), [
                'estimated_delivery_date' => '2022-05-27',
            ]),
            CustomerOrderCreationContextDto::class
        );
        $result = $this->getTestedInstance()->create($order);
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $customer_order_id = $result->customer_order_id;

        $this->assertEquals($customer_order_id, $customer_order['no_commande_origine']);
        $this->assertEquals('son-video.com', $customer_order['creation_origine']);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order['date_creation']);
        $this->assertEquals('2022-05-27', $customer_order['initial_estimated_delivery_date']);
        $this->assertEquals('traitement', $customer_order['flux']);
        $this->assertNull($customer_order['V_statut_traitement']);
        $this->assertEquals('***********', $customer_order['ip']);
        $this->assertEquals('N', $customer_order['rappel_client']);
        $this->assertNull($customer_order['depot_emport']);
        $this->assertEquals('non', $customer_order['detaxe_export']);
        $this->assertEquals(1, $customer_order['cmd_intragroupe']);
        $this->assertEquals(1, $customer_order['id_prospect']);
        $this->assertNull($customer_order['id_devis']);
        $this->assertEquals(10, $customer_order['quote_id']);
        $this->assertEquals('', $customer_order['commentaire_facture']);
        $this->assertEquals(23, $customer_order['promotion_id']);
        $this->assertEquals(2, $customer_order['id_transporteur']);
        $this->assertEquals(1, $customer_order['id_pdt_transporteur']);
        $this->assertNull($customer_order['tpt_option_code']);
        $this->assertEquals(5, $customer_order['warehouse_id']);
        $this->assertEquals(1, $customer_order['sales_channel_id']);

        // Billing address assertions
        $this->assertEquals('particulier', $customer_order['cnt_fct_type']);
        $this->assertEquals('<EMAIL>', $customer_order['cnt_fct_email']);
        $this->assertEquals('', $customer_order['cnt_fct_societe']);
        $this->assertEquals('M.', $customer_order['cnt_fct_civilite']);
        $this->assertEquals('TERIEUR', $customer_order['cnt_fct_nom']);
        $this->assertEquals('Alain', $customer_order['cnt_fct_prenom']);
        $this->assertEquals('1 rue des fleurs', $customer_order['cnt_fct_adresse']);
        $this->assertEquals('44100', $customer_order['cnt_fct_code_postal']);
        $this->assertEquals('NANTES', $customer_order['cnt_fct_ville']);
        $this->assertEquals(67, $customer_order['cnt_fct_id_pays']);
        $this->assertEquals('0606060606', $customer_order['cnt_fct_telephone']);
        $this->assertEquals('0606060607', $customer_order['cnt_fct_mobile']);
        $this->assertEquals('', $customer_order['cnt_fct_fax']);
        $this->assertNull($customer_order['cnt_fct_numero_tva']);

        // Shipping address assertions
        $this->assertEquals('particulier', $customer_order['cnt_lvr_type']);
        $this->assertEquals('<EMAIL>', $customer_order['cnt_lvr_email']);
        $this->assertEquals('', $customer_order['cnt_lvr_societe']);
        $this->assertEquals('M.', $customer_order['cnt_lvr_civilite']);
        $this->assertEquals('DURE', $customer_order['cnt_lvr_nom']);
        $this->assertEquals('Laure', $customer_order['cnt_lvr_prenom']);
        $this->assertEquals('111 route de paris', $customer_order['cnt_lvr_adresse']);
        $this->assertEquals('44000', $customer_order['cnt_lvr_code_postal']);
        $this->assertEquals('NANTES', $customer_order['cnt_lvr_ville']);
        $this->assertEquals(67, $customer_order['cnt_lvr_id_pays']);
        $this->assertEquals('0707070707', $customer_order['cnt_lvr_telephone']);
        $this->assertEquals('0707070708', $customer_order['cnt_lvr_mobile']);
        $this->assertEquals('', $customer_order['cnt_lvr_fax']);
        $this->assertNull($customer_order['cnt_lvr_numero_tva']);

        // Products assertions
        $customer_order_products = $this->fetchCustomerOrderProducts($customer_order_id);

        // Product
        $this->assertEquals($customer_order_id, $customer_order_products[0]->id_commande);
        $this->assertEquals(81078, $customer_order_products[0]->id_produit);
        $this->assertEquals(2, $customer_order_products[0]->quantite);
        $this->assertEquals(1200.0, $customer_order_products[0]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[0]->tva);
        $this->assertEquals(131.58, $customer_order_products[0]->prix_achat);
        $this->assertEquals('Récepteur Audio Bluetooth APTX Arcam rBlink', $customer_order_products[0]->description);
        $this->assertEquals(-200, $customer_order_products[0]->remise_montant);
        $this->assertEquals('devis', $customer_order_products[0]->remise_type);
        $this->assertEquals('Remise devis', $customer_order_products[0]->remise_description);
        $this->assertEquals(0, $customer_order_products[0]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[0]->prix_sorecop);
        $this->assertEquals('devis', $customer_order_products[0]->groupe_type);
        $this->assertEquals('Remise devis', $customer_order_products[0]->groupe_description);

        // Shipment method
        $this->assertEquals($customer_order_id, $customer_order_products[1]->id_commande);
        $this->assertEquals(Product::SHIPMENT_PRODUCT_ID, $customer_order_products[1]->id_produit);
        $this->assertEquals(1, $customer_order_products[1]->quantite);
        $this->assertEquals(4.99, $customer_order_products[1]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[1]->tva);
        $this->assertEquals(0.0, $customer_order_products[1]->prix_achat);
        $this->assertEquals(
            ProductV2Entity::SHIPPING_COST_PRODUCT_DESCRIPTION,
            $customer_order_products[1]->description
        );
        $this->assertNull($customer_order_products[1]->remise_type);
        $this->assertEquals(0, $customer_order_products[1]->remise_montant);
        $this->assertNull($customer_order_products[1]->remise_description);
        $this->assertEquals(0, $customer_order_products[1]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[1]->prix_sorecop);
        $this->assertNull($customer_order_products[1]->groupe_type);
        $this->assertNull($customer_order_products[1]->groupe_description);

        // Payments assertions
        $customer_order_payments = $this->fetchCustomerOrderPayments($customer_order_id);

        $this->assertEquals($customer_order_id, $customer_order_payments[0]->id_commande);
        $this->assertEquals(59, $customer_order_payments[0]->id_paiement);
        $this->assertEquals(1, $customer_order_payments[0]->id_unique);
        $this->assertEquals('paiement', $customer_order_payments[0]->type);
        $this->assertNull($customer_order_payments[0]->warehouse_id);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order_payments[0]->creation_date);
        $this->assertEquals('backoffice', $customer_order_payments[0]->creation_usr);
        $this->assertEquals(2362.99, $customer_order_payments[0]->creation_montant);
        $this->assertEquals('son-video.com', $customer_order_payments[0]->creation_origine);

        // Statistics assertions
        $customer_order_product_stat_initial = $this->fetchCustomerOrderProductsStatInitial($customer_order_id);

        // Product initial 1
        $this->assertEquals($customer_order_id, $customer_order_product_stat_initial[0]->customer_order_id);
        $this->assertEquals(81078, $customer_order_product_stat_initial[0]->product_id);
        $this->assertEquals(2, $customer_order_product_stat_initial[0]->quantity);
        $this->assertEquals(1200.0, $customer_order_product_stat_initial[0]->selling_price_tax_included);
        $this->assertEquals(0.2, $customer_order_product_stat_initial[0]->vat);
        $this->assertEquals(131.58, $customer_order_product_stat_initial[0]->purchase_price);
        $this->assertEquals(-200, $customer_order_product_stat_initial[0]->discount_amount);
        $this->assertEquals(0, $customer_order_product_stat_initial[0]->ecotax_price);
        $this->assertEquals(0, $customer_order_product_stat_initial[0]->sorecop_price);
        $this->assertEquals(79, $customer_order_product_stat_initial[0]->warranties_price);
        $this->assertNull($customer_order_product_stat_initial[0]->promo_code);
        $this->assertEquals(3, $customer_order_product_stat_initial[0]->available_quantity);

        // Product initial 2
        $this->assertEquals($customer_order_id, $customer_order_product_stat_initial[1]->customer_order_id);
        $this->assertEquals(Product::SHIPMENT_PRODUCT_ID, $customer_order_product_stat_initial[1]->product_id);
        $this->assertEquals(1, $customer_order_product_stat_initial[1]->quantity);
        $this->assertEquals(4.99, $customer_order_product_stat_initial[1]->selling_price_tax_included);
        $this->assertEquals(0.2, $customer_order_product_stat_initial[1]->vat);
        $this->assertEquals(0, $customer_order_product_stat_initial[1]->purchase_price);
        $this->assertEquals(0, $customer_order_product_stat_initial[1]->discount_amount);
        $this->assertEquals(0, $customer_order_product_stat_initial[1]->ecotax_price);
        $this->assertEquals(0, $customer_order_product_stat_initial[1]->sorecop_price);
        $this->assertEquals(0, $customer_order_product_stat_initial[1]->warranties_price);
        $this->assertNull($customer_order_product_stat_initial[1]->promo_code);
        $this->assertEquals(0, $customer_order_product_stat_initial[1]->available_quantity);

        // Tags assertions
        $this->assertCount(3, $this->fetchCustomerOrderTags($customer_order_id));
        $this->assertEquals(
            [
                [
                    'id_commande' => (string) $customer_order_id,
                    'tag_id' => 'site_checkout_v2',
                    'meta' => '{}',
                ],
                [
                    'id_commande' => (string) $customer_order_id,
                    'tag_id' => 'source.intragroup.intragroup',
                    'meta' => '{}',
                ],
                [
                    'id_commande' => (string) $customer_order_id,
                    'tag_id' => 'status.import',
                    'meta' => '{}',
                ],
            ],
            $this->fetchCustomerOrderTags($customer_order_id)
        );

        // No task created
        $this->assertCount(0, $this->fetchTask($customer_order_id));

        // No comment created
        $this->assertCount(0, $this->fetchComment($customer_order_id));
    }

    /** Tests response without a payment v2. */
    public function test_response_without_a_payment_v2(): void
    {
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode(),
            CustomerOrderCreationContextDto::class
        );

        $this->assertInstanceOf(CreatedCustomerOrder::class, $this->getTestedInstance()->create($order));
    }

    /** Tests response with a successful payment v2. */
    public function test_response_with_a_successful_payment_v2(): void
    {
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode(),
            CustomerOrderCreationContextDto::class
        );

        $payment_state = new class() implements PaymentV2StateInterface {
            public function isValidWithProvidedOrderContext(
                CustomerOrderCreationContextDto $context_entity
            ): CustomerOrderCreationContextDto {
                /** @var CustomerOrderPaymentCreationContextDto $payment */
                $payment = $context_entity->payments[0];
                $payment->workflow = PaymentWorkflow::V2;

                $verified_payment = new PaymentV2CreationPayload();
                $verified_payment->code = $payment->payment_mean;
                $verified_payment->amount = $payment->amount;
                $verified_payment->currency_code = 'EUR';
                $verified_payment->extra_data = $payment->extra_data;
                $verified_payment->workflow = $payment->workflow;
                $verified_payment->return_url = 'DUMMY';

                $payment->setPaymentV2VerifiedPayment($verified_payment);

                $context_entity->payments = [$payment];

                return $context_entity;
            }

            public function isValidWithExistingCustomerOrder(
                CustomerOrderPaymentCreationRequestDto $request_context,
                CustomerOrderBasicInfo $customer_order_basic_info
            ): CustomerOrderPaymentCreationRequestDto {
                return $request_context;
            }

            public function canHandle(string $payment_method_code, int $customer_id): bool
            {
                return true;
            }
        };

        $payment_creator = new class() implements PaymentV2CreatorInterface {
            public function create(CustomerOrderCreationContextDto $order_context, int $customer_order_id): ?array
            {
                return ['action' => ['redirect_to' => 'url/to']];
            }

            public function add(CustomerOrderPaymentCreationRequestDto $request_context): ?array
            {
                return [];
            }
        };

        // Can be improved/simplified when migrating from Atoum in Symfony 5+
        $instance = new TestedClass(
            self::$container->get(LegacyPdo::class),
            self::$container->get(ValidatorInterface::class),
            self::$container->get(DataLoaderInterface::class),
            self::$container->get(CustomerOrderCreationDataMapperCollection::class),
            self::$container->get(CustomerOrderRepository::class),
            self::$container->get(CustomerOrderProductRepository::class),
            self::$container->get(CustomerOrderPaymentWriteRepository::class),
            self::$container->get(TaskRepository::class),
            self::$container->get(SerializerInterface::class),
            self::$container->get(CustomerManager::class),
            self::$container->get(SvdGiftCardManager::class),
            $payment_state,
            $payment_creator,
            self::$container->get(QuoteRepository::class),
            self::$container->get(CurrentUser::class),
            self::$container->get(SynchronizableTopicReadRepository::class),
            self::$container->get(CustomerOrderDeliveryDateFetcher::class)
        );
        $instance->setLogger(self::$container->get(LoggerInterface::class));
        $result = $instance->create($order);

        $this->assertInstanceOf(CreatedCustomerOrderWithPaymentAction::class, $result);
        $this->assertEquals(['redirect_to' => 'url/to'], $result->payment_action);
    }

    /** Tests response with a failed payment v2. */
    public function test_response_with_a_failed_payment_v2(): void
    {
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode(),
            CustomerOrderCreationContextDto::class
        );

        $payment_state = new class() implements PaymentV2StateInterface {
            public function isValidWithProvidedOrderContext(
                CustomerOrderCreationContextDto $context_entity
            ): CustomerOrderCreationContextDto {
                /** @var CustomerOrderPaymentCreationContextDto $payment */
                $payment = $context_entity->payments[0];
                $payment->workflow = PaymentWorkflow::V2;

                $verified_payment = new PaymentV2CreationPayload();
                $verified_payment->code = $payment->payment_mean;
                $verified_payment->amount = $payment->amount;
                $verified_payment->currency_code = 'EUR';
                $verified_payment->extra_data = $payment->extra_data;
                $verified_payment->workflow = $payment->workflow;
                $verified_payment->return_url = 'DUMMY';

                $payment->setPaymentV2VerifiedPayment($verified_payment);

                $context_entity->payments = [$payment];

                return $context_entity;
            }

            public function isValidWithExistingCustomerOrder(
                CustomerOrderPaymentCreationRequestDto $request_context,
                CustomerOrderBasicInfo $customer_order_basic_info
            ): CustomerOrderPaymentCreationRequestDto {
                return $request_context;
            }

            public function canHandle(string $payment_method_code, int $customer_id): bool
            {
                return true;
            }
        };

        $payment_creator = new class() implements PaymentV2CreatorInterface {
            public function create(CustomerOrderCreationContextDto $order_context, int $customer_order_id): ?array
            {
                throw new \Exception('An error');
            }

            public function add(CustomerOrderPaymentCreationRequestDto $request_context): ?array
            {
                return [];
            }
        };

        // Can be improved/simplified when migrating from Atoum in Symfony 5+
        $instance = new TestedClass(
            self::$container->get(LegacyPdo::class),
            self::$container->get(ValidatorInterface::class),
            self::$container->get(DataLoaderInterface::class),
            self::$container->get(CustomerOrderCreationDataMapperCollection::class),
            self::$container->get(CustomerOrderRepository::class),
            self::$container->get(CustomerOrderProductRepository::class),
            self::$container->get(CustomerOrderPaymentWriteRepository::class),
            self::$container->get(TaskRepository::class),
            self::$container->get(SerializerInterface::class),
            self::$container->get(CustomerManager::class),
            self::$container->get(SvdGiftCardManager::class),
            $payment_state,
            $payment_creator,
            self::$container->get(QuoteRepository::class),
            self::$container->get(CurrentUser::class),
            self::$container->get(SynchronizableTopicReadRepository::class),
            self::$container->get(CustomerOrderDeliveryDateFetcher::class)
        );
        $instance->setLogger(self::$container->get(LoggerInterface::class));
        $result = $instance->create($order);

        $this->assertInstanceOf(CreatedCustomerOrderWithPaymentAction::class, $result);
        $this->assertEquals(['cancel' => true], $result->payment_action);
    }

    /** Tests creating a customer order with a promo code. */
    public function test_create_with_promo_code(): void
    {
        // Create customer order with promo code
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithPromoCode();
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        // Verify that the customer order has been inserted
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $this->assertNotEmpty($customer_order);
        $this->assertEquals(23, $customer_order['promotion_id']);

        $customer_order_products = $this->fetchCustomerOrderProducts($customer_order_id);
        $this->assertCount(4, $customer_order_products);

        // Product 1
        $this->assertEquals($customer_order_id, $customer_order_products[2]->id_commande);
        $this->assertEquals(81078, $customer_order_products[2]->id_produit);
        $this->assertEquals(1, $customer_order_products[2]->quantite);
        $this->assertEquals(249.0, $customer_order_products[2]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[2]->tva);
        $this->assertEquals(131.58, $customer_order_products[2]->prix_achat);
        $this->assertEquals(0.0, $customer_order_products[2]->remise_montant);
        $this->assertNull($customer_order_products[2]->remise_type);
        $this->assertNull($customer_order_products[2]->remise_description);
        $this->assertEquals(0.15, $customer_order_products[2]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[2]->prix_sorecop);
        $this->assertNull($customer_order_products[2]->groupe_type);
        $this->assertNull($customer_order_products[2]->groupe_description);

        // Product 2
        $this->assertEquals(128416, $customer_order_products[0]->id_produit);
        $this->assertEquals(1, $customer_order_products[0]->quantite);
        $this->assertEquals(500.0, $customer_order_products[0]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[0]->tva);
        $this->assertEquals(412.5, $customer_order_products[0]->prix_achat);
        $this->assertEquals('Enceinte encastrable BW CCM 7.4', $customer_order_products[0]->description);
        $this->assertEquals(0, $customer_order_products[0]->remise_montant);
        $this->assertNull($customer_order_products[0]->remise_type);
        $this->assertNull($customer_order_products[0]->remise_description);
        $this->assertEquals(0.15, $customer_order_products[0]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[0]->prix_sorecop);
        $this->assertEquals('promocode', $customer_order_products[0]->groupe_type);
        $this->assertEquals('PUYDUFOU', $customer_order_products[0]->groupe_description);

        // Product 3
        $this->assertEquals(81123, $customer_order_products[1]->id_produit);
        $this->assertEquals(1, $customer_order_products[1]->quantite);
        $this->assertEquals(50.0, $customer_order_products[1]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[1]->tva);
        $this->assertEquals(176.35, $customer_order_products[1]->prix_achat);
        $this->assertEquals(
            'Paire de pieds noirs laqués pour station multimédia La Boîte Concept LD120 et LD130',
            $customer_order_products[1]->description
        );
        $this->assertEquals(-40.0, $customer_order_products[1]->remise_montant);
        $this->assertEquals('commande', $customer_order_products[1]->remise_type);
        $this->assertEquals('Remise exceptionnelle', $customer_order_products[1]->remise_description);
        $this->assertEquals(0.15, $customer_order_products[1]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[1]->prix_sorecop);
        $this->assertEquals('promocode', $customer_order_products[1]->groupe_type);
        $this->assertEquals('PUYDUFOU', $customer_order_products[1]->groupe_description);

        $customer_order_payments = $this->fetchCustomerOrderPayments($customer_order_id);

        // Payments
        $this->assertEquals($customer_order_id, $customer_order_payments[0]->id_commande);
        $this->assertEquals(59, $customer_order_payments[0]->id_paiement);
        $this->assertEquals(1, $customer_order_payments[0]->id_unique);
        $this->assertEquals('paiement', $customer_order_payments[0]->type);
        $this->assertNull($customer_order_payments[0]->warehouse_id);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order_payments[0]->creation_date);
        $this->assertEquals('backoffice', $customer_order_payments[0]->creation_usr);
        $this->assertEquals(862.0, $customer_order_payments[0]->creation_montant);

        // Statistics have been inserted with promo code
        $customer_order_product_stat_initial = $this->fetchCustomerOrderProductsStatInitial($customer_order_id);

        $this->assertEquals($customer_order_id, $customer_order_product_stat_initial[0]->customer_order_id);
        $this->assertEquals(128416, $customer_order_product_stat_initial[0]->product_id);
        $this->assertEquals('PUYDUFOU', $customer_order_product_stat_initial[0]->promo_code);
        $this->assertEquals(0, $customer_order_product_stat_initial[0]->available_quantity);
        $this->assertEquals(81123, $customer_order_product_stat_initial[1]->product_id);
        $this->assertEquals('PUYDUFOU', $customer_order_product_stat_initial[1]->promo_code);
        $this->assertEquals(2, $customer_order_product_stat_initial[1]->available_quantity);
        $this->assertEquals(81078, $customer_order_product_stat_initial[2]->product_id);
        $this->assertNull($customer_order_product_stat_initial[2]->promo_code);
        $this->assertEquals(3, $customer_order_product_stat_initial[2]->available_quantity);
    }

    /** Tests creating a customer order successfully even if the amounts are mismatched. */
    public function test_create_with_amount_mismatch(): void
    {
        // Create customer order with mismatched amounts
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $customer_order_payload['payments'][0]['amount'] = 0;
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        // Verify that the customer order has been inserted
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $this->assertNotEmpty($customer_order);
        $this->assertNull($customer_order['relay_id']);
        $this->assertCount(2, $this->fetchCustomerOrderProducts($customer_order_id));
        $this->assertCount(1, $this->fetchCustomerOrderPayments($customer_order_id));
        $this->assertCount(2, $this->fetchCustomerOrderProductsStatInitial($customer_order_id));

        // No task created
        $this->assertCount(0, $this->fetchTask($customer_order_id));

        // No comment created
        $this->assertCount(0, $this->fetchComment($customer_order_id));
    }

    /** Tests creating a customer order successfully even without payment. */
    public function test_create_without_payment(): void
    {
        // Create customer order without payment
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $customer_order_payload['payments'] = [];
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        // Verify that the customer order has been inserted
        $this->assertNotEmpty($this->fetchOneCustomerOrder($result->customer_order_id));
        $this->assertCount(2, $this->fetchCustomerOrderProducts($customer_order_id));
        $this->assertCount(0, $this->fetchCustomerOrderPayments($customer_order_id));
        $this->assertCount(2, $this->fetchCustomerOrderProductsStatInitial($customer_order_id));
        $this->assertCount(0, $this->fetchTask($customer_order_id));
        $this->assertCount(0, $this->fetchComment($customer_order_id));
    }

    /** Tests creating a customer order successfully with float values. */
    public function test_create_with_float_values(): void
    {
        // Create customer order with float values
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuote();
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        // Verify that no tasks or comments were created
        $this->assertCount(0, $this->fetchTask($customer_order_id));
        $this->assertCount(0, $this->fetchComment($customer_order_id));
    }

    /** Tests creating a customer order successfully with product and bundle. */
    public function test_create_with_product_and_bundle(): void
    {
        // Create customer order with product and bundle
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithProductAndBundle();
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        // Verify that the customer order has been inserted
        $this->assertNotEmpty($this->fetchOneCustomerOrder($result->customer_order_id));

        $customer_order_products = $this->fetchCustomerOrderProducts($customer_order_id);
        $this->assertCount(3, $customer_order_products);

        // Product 1
        $this->assertEquals($customer_order_id, $customer_order_products[0]->id_commande);
        $this->assertEquals(81078, $customer_order_products[0]->id_produit);
        $this->assertEquals(25, $customer_order_products[0]->quantite);
        $this->assertEquals(2.99, $customer_order_products[0]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[0]->tva);
        $this->assertEquals(131.58, $customer_order_products[0]->prix_achat);
        $this->assertEquals(
            'Câble d\'enceintes NorStone CL250 Classic - Conducteur en cuivre OFC, section 2 x 2,5 mm2, gaine transparente et longueur 1 m',
            $customer_order_products[0]->description
        );
        $this->assertEquals(-7.5, $customer_order_products[0]->remise_montant);
        $this->assertEquals('commande', $customer_order_products[0]->remise_type);
        $this->assertEquals('Remise exceptionnelle', $customer_order_products[0]->remise_description);
        $this->assertEquals(0.2, $customer_order_products[0]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[0]->prix_sorecop);
        $this->assertEquals('commande', $customer_order_products[0]->groupe_type);
        $this->assertEquals('Remise exceptionnelle', $customer_order_products[0]->groupe_description);

        // Product 2
        $this->assertEquals($customer_order_id, $customer_order_products[1]->id_commande);
        $this->assertEquals(13654, $customer_order_products[1]->id_produit);
        $this->assertEquals(25, $customer_order_products[1]->quantite);
        $this->assertEquals(2.99, $customer_order_products[1]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[1]->tva);
        $this->assertEquals(0.8, $customer_order_products[1]->prix_achat);
        $this->assertEquals('Toto', $customer_order_products[1]->description);
        $this->assertEquals(-66.96, $customer_order_products[1]->remise_montant);
        $this->assertEquals('compose', $customer_order_products[1]->remise_type);
        $this->assertEquals('Remise composé NORSTCL25025M', $customer_order_products[1]->remise_description);
        $this->assertEquals(0.02, $customer_order_products[1]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[1]->prix_sorecop);
        $this->assertEquals('compose', $customer_order_products[1]->groupe_type);
        $this->assertEquals('NORSTCL25025M', $customer_order_products[1]->groupe_description);

        // Verify statistics
        $this->assertCount(3, $this->fetchCustomerOrderProductsStatInitial($customer_order_id));
    }

    /** Tests creating a customer order with a relay point. */
    public function test_create_with_relay_point(): void
    {
        // Create customer order with a relay point
        $customer_order_payload = array_merge(CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode(), [
            'quote_id' => null,
            'shipment_method' => [
                'cost' => 9.99,
                // No particular constraint in the db regarding if the shipment method id is an actual relay one
                'shipment_method_id' => 1,
                'relay_id' => 'TOTO123',
            ],
        ]);

        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $this->assertEquals('TOTO123', $customer_order['relay_id']);
        $this->assertFalse($this->fetchCustomerOrderChronoPreciseAppointment($customer_order_id));
    }

    /** Tests creating a customer order with a chrono precise appointment. */
    public function test_create_with_chrono_precise_appointment(): void
    {
        // Create customer order with a chrono precise appointment
        $today = new \DateTime();
        $customer_order_payload = array_merge(CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode(), [
            'quote_id' => null,
            'shipment_method' => [
                'cost' => 9.99,
                // No particular constraint in the db regarding if the shipment method id is an actual relay one
                'shipment_method_id' => 1,
                'chrono_precise_appointment' => [
                    'time_slot_start_date' => sprintf('%s 16:00:00', $today->format('Y-m-d')),
                    'time_slot_end_date' => sprintf('%s 18:00:00', $today->format('Y-m-d')),
                    'time_slot_tariff_level' => 'N1',
                    'product_code' => '2O',
                    'service_code' => '976',
                ],
            ],
        ]);

        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);

        $this->assertNull($customer_order['relay_id']);
        $this->assertEquals(
            [
                'id_commande' => (string) $customer_order_id,
                'time_slot_start_date' => sprintf('%s 16:00:00', $today->format('Y-m-d')),
                'time_slot_end_date' => sprintf('%s 18:00:00', $today->format('Y-m-d')),
                'time_slot_tariff_level' => 'N1',
                'product_code' => '2O',
                'service_code' => '976',
            ],
            $this->fetchCustomerOrderChronoPreciseAppointment($customer_order_id)
        );
    }

    /** Tests creating a customer order with delivery to Spain. */
    public function test_create_with_delivery_to_spain(): void
    {
        // Create customer order with delivery to Spain
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $customer_order_payload['shipping_address']['country_code'] = 'ES';
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        // Verify that the customer order has been inserted
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $this->assertNotEmpty($customer_order);
        $this->assertEquals(
            62, // Spain id country
            $customer_order['cnt_lvr_id_pays']
        );

        $customer_order_products = $this->fetchCustomerOrderProducts($customer_order_id);
        $this->assertCount(2, $customer_order_products);
        $this->assertEquals(
            0.21, // Spain vat
            $customer_order_products[0]->tva
        );
        $this->assertEquals(
            0.21, // Spain vat
            $customer_order_products[1]->tva
        );

        $this->assertCount(1, $this->fetchCustomerOrderPayments($customer_order_id));

        $customer_order_product_stat_initial = $this->fetchCustomerOrderProductsStatInitial($customer_order_id);
        $this->assertCount(2, $customer_order_product_stat_initial);
        $this->assertEquals(
            0.21, // Spain vat
            $customer_order_product_stat_initial[0]->vat
        );
        $this->assertEquals(0.21, $customer_order_product_stat_initial[1]->vat); // Spain vat
    }

    /** Tests creating a customer order with unknown VAT (defaults to French VAT). */
    public function test_create_with_unknown_vat(): void
    {
        // Create customer order with delivery to Portugal (unknown VAT)
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $customer_order_payload['shipping_address']['country_code'] = 'PT';
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        // Verify that the customer order has been inserted
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $this->assertNotEmpty($customer_order);
        $this->assertEquals(
            158, // Portugal id country
            $customer_order['cnt_lvr_id_pays']
        );

        $customer_order_products = $this->fetchCustomerOrderProducts($customer_order_id);
        $this->assertCount(2, $customer_order_products);
        $this->assertEquals(
            0.2, // French VAT (default)
            $customer_order_products[0]->tva
        );
        $this->assertEquals(
            0.2, // French VAT (default)
            $customer_order_products[1]->tva
        );

        $this->assertCount(1, $this->fetchCustomerOrderPayments($customer_order_id));

        $customer_order_product_stat_initial = $this->fetchCustomerOrderProductsStatInitial($customer_order_id);
        $this->assertCount(2, $customer_order_product_stat_initial);
        $this->assertEquals(
            0.2, // French VAT (default)
            $customer_order_product_stat_initial[0]->vat
        );
        $this->assertEquals(0.2, $customer_order_product_stat_initial[1]->vat); // French VAT (default)
    }

    /** Tests creating a customer order without company name. */
    public function test_create_without_company_name(): void
    {
        // Create customer order without company name
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $customer_order_payload['shipping_address']['company_name'] = null;
        $customer_order_payload['billing_address']['company_name'] = null;
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);

        // Verify that the customer order has been inserted
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $this->assertNotEmpty($customer_order);
        $this->assertEquals('', $customer_order['cnt_fct_societe']);
        $this->assertEquals('', $customer_order['cnt_lvr_societe']);
    }

    /** Tests creating a customer order for EZL. */
    public function test_create_for_ezl(): void
    {
        // Create customer order for EZL
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromEzl();
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        // Verify that the customer order has been inserted
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $this->assertEquals('EC1234', $customer_order['no_commande_origine']);
        $this->assertEquals('ecranlounge.com', $customer_order['creation_origine']);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order['date_creation']);
        $this->assertEquals('traitement', $customer_order['flux']);
        $this->assertNull($customer_order['V_statut_traitement']);
        $this->assertEquals('*************', $customer_order['ip']);
        $this->assertEquals('N', $customer_order['rappel_client']);
        $this->assertNull($customer_order['depot_emport']);
        $this->assertEquals('non', $customer_order['detaxe_export']);
        $this->assertEquals(0, $customer_order['cmd_intragroupe']);
        $this->assertEquals(314559, $customer_order['id_prospect']);
        $this->assertNull($customer_order['id_devis']);
        $this->assertNull($customer_order['quote_id']);
        $this->assertEquals('', $customer_order['commentaire_facture']);
        $this->assertNull($customer_order['promotion_id']);
        $this->assertEquals(2, $customer_order['id_transporteur']);
        $this->assertEquals(1, $customer_order['id_pdt_transporteur']);
        $this->assertNull($customer_order['tpt_option_code']);
        $this->assertNull($customer_order['warehouse_id']);
        $this->assertEquals(2, $customer_order['sales_channel_id']);

        // Billing address assertions
        $this->assertEquals('particulier', $customer_order['cnt_fct_type']);
        $this->assertEquals('<EMAIL>', $customer_order['cnt_fct_email']);
        $this->assertEquals('', $customer_order['cnt_fct_societe']);
        $this->assertEquals('M.', $customer_order['cnt_fct_civilite']);
        $this->assertEquals('TERIEUR', $customer_order['cnt_fct_nom']);
        $this->assertEquals('Alain', $customer_order['cnt_fct_prenom']);
        $this->assertEquals('1 rue des fleurs', $customer_order['cnt_fct_adresse']);
        $this->assertEquals('44100', $customer_order['cnt_fct_code_postal']);
        $this->assertEquals('NANTES', $customer_order['cnt_fct_ville']);
        $this->assertEquals(67, $customer_order['cnt_fct_id_pays']);
        $this->assertEquals('0606060606', $customer_order['cnt_fct_telephone']);
        $this->assertEquals('0606060607', $customer_order['cnt_fct_mobile']);
        $this->assertEquals('', $customer_order['cnt_fct_fax']);
        $this->assertNull($customer_order['cnt_fct_numero_tva']);

        // Shipping address assertions
        $this->assertEquals('particulier', $customer_order['cnt_lvr_type']);
        $this->assertEquals('<EMAIL>', $customer_order['cnt_lvr_email']);
        $this->assertEquals('', $customer_order['cnt_lvr_societe']);
        $this->assertEquals('M.', $customer_order['cnt_lvr_civilite']);
        $this->assertEquals('DURE', $customer_order['cnt_lvr_nom']);
        $this->assertEquals('Laure', $customer_order['cnt_lvr_prenom']);
        $this->assertEquals('111 route de paris', $customer_order['cnt_lvr_adresse']);
        $this->assertEquals('44000', $customer_order['cnt_lvr_code_postal']);
        $this->assertEquals('NANTES', $customer_order['cnt_lvr_ville']);
        $this->assertEquals(67, $customer_order['cnt_lvr_id_pays']);
        $this->assertEquals('0707070707', $customer_order['cnt_lvr_telephone']);
        $this->assertEquals('0707070708', $customer_order['cnt_lvr_mobile']);
        $this->assertEquals('', $customer_order['cnt_lvr_fax']);
        $this->assertNull($customer_order['cnt_lvr_numero_tva']);

        $customer_order_products = $this->fetchCustomerOrderProducts($customer_order_id);

        // Products assertions
        $this->assertEquals($customer_order_id, $customer_order_products[0]->id_commande);
        $this->assertEquals(81123, $customer_order_products[0]->id_produit);
        $this->assertEquals(1, $customer_order_products[0]->quantite);
        $this->assertEquals(40.0, $customer_order_products[0]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[0]->tva);
        $this->assertEquals(176.35, $customer_order_products[0]->prix_achat);
        $this->assertEquals(
            'Paire de supports d\'enceintes B-Tech Mountlogic BT77 Noir',
            $customer_order_products[0]->description
        );
        $this->assertEquals(0, $customer_order_products[0]->remise_montant);
        $this->assertNull($customer_order_products[0]->remise_type);
        $this->assertNull($customer_order_products[0]->remise_description);
        $this->assertEquals(0, $customer_order_products[0]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[0]->prix_sorecop);
        $this->assertNull($customer_order_products[0]->groupe_type);
        $this->assertNull($customer_order_products[0]->groupe_description);

        $this->assertEquals($customer_order_id, $customer_order_products[1]->id_commande);
        $this->assertEquals(81078, $customer_order_products[1]->id_produit);
        $this->assertEquals(1, $customer_order_products[1]->quantite);
        $this->assertEquals(1200.0, $customer_order_products[1]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[1]->tva);
        $this->assertEquals(131.58, $customer_order_products[1]->prix_achat);
        $this->assertEquals('Récepteur Audio Bluetooth APTX Arcam rBlink', $customer_order_products[1]->description);
        $this->assertEquals(-100, $customer_order_products[1]->remise_montant);
        $this->assertEquals('commande', $customer_order_products[1]->remise_type);
        $this->assertEquals('Remise exceptionnelle', $customer_order_products[1]->remise_description);
        $this->assertEquals(0, $customer_order_products[1]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[1]->prix_sorecop);
        $this->assertNull($customer_order_products[1]->groupe_type);
        $this->assertNull($customer_order_products[1]->groupe_description);

        // Shipment method assertions
        $this->assertEquals($customer_order_id, $customer_order_products[2]->id_commande);
        $this->assertEquals(Product::SHIPMENT_PRODUCT_ID, $customer_order_products[2]->id_produit);
        $this->assertEquals(1, $customer_order_products[2]->quantite);
        $this->assertEquals(4.99, $customer_order_products[2]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[2]->tva);
        $this->assertEquals(0.0, $customer_order_products[2]->prix_achat);
        $this->assertEquals(
            ProductV2Entity::SHIPPING_COST_PRODUCT_DESCRIPTION,
            $customer_order_products[2]->description
        );
        $this->assertNull($customer_order_products[2]->remise_type);
        $this->assertEquals(0, $customer_order_products[2]->remise_montant);
        $this->assertNull($customer_order_products[2]->remise_description);
        $this->assertEquals(0, $customer_order_products[2]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[2]->prix_sorecop);
        $this->assertNull($customer_order_products[2]->groupe_type);
        $this->assertNull($customer_order_products[2]->groupe_description);

        $customer_order_payments = $this->fetchCustomerOrderPayments($customer_order_id);

        // Payments assertions
        $this->assertEquals($customer_order_id, $customer_order_payments[0]->id_commande);
        $this->assertEquals(74, $customer_order_payments[0]->id_paiement);
        $this->assertEquals(1, $customer_order_payments[0]->id_unique);
        $this->assertEquals('paiement', $customer_order_payments[0]->type);
        $this->assertNull($customer_order_payments[0]->warehouse_id);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order_payments[0]->creation_date);
        $this->assertEquals('backoffice', $customer_order_payments[0]->creation_usr);
        $this->assertEquals(1144.99, $customer_order_payments[0]->creation_montant);
        $this->assertEquals('EC1234-1', $customer_order_payments[0]->creation_justificatif);
        $this->assertEquals('ecranlounge.com', $customer_order_payments[0]->creation_origine);

        $customer_order_product_stat_initial = $this->fetchCustomerOrderProductsStatInitial($customer_order_id);

        // Product initial assertions
        $this->assertEquals($customer_order_id, $customer_order_product_stat_initial[1]->customer_order_id);
        $this->assertEquals(81078, $customer_order_product_stat_initial[1]->product_id);
        $this->assertEquals(1, $customer_order_product_stat_initial[1]->quantity);
        $this->assertEquals(1200.0, $customer_order_product_stat_initial[1]->selling_price_tax_included);
        $this->assertEquals(0.2, $customer_order_product_stat_initial[1]->vat);
        $this->assertEquals(131.58, $customer_order_product_stat_initial[1]->purchase_price);
        $this->assertEquals(-100, $customer_order_product_stat_initial[1]->discount_amount);
        $this->assertEquals(0, $customer_order_product_stat_initial[1]->ecotax_price);
        $this->assertEquals(0, $customer_order_product_stat_initial[1]->sorecop_price);
        $this->assertEquals(0, $customer_order_product_stat_initial[1]->warranties_price);
        $this->assertNull($customer_order_product_stat_initial[1]->promo_code);
        $this->assertEquals(3, $customer_order_product_stat_initial[1]->available_quantity);

        // Tags assertions
        $this->assertCount(1, $this->fetchCustomerOrderTags($customer_order_id));
        $this->assertEquals(
            [
                [
                    'id_commande' => (string) $customer_order_id,
                    'tag_id' => CustomerOrderTag::SOURCE_WEB_EZL,
                    'meta' => '{}',
                ],
            ],
            $this->fetchCustomerOrderTags($customer_order_id)
        );

        // No task or comment created
        $this->assertCount(0, $this->fetchTask($customer_order_id));
        $this->assertCount(0, $this->fetchComment($customer_order_id));
    }

    /** Tests that creating a customer order for EZL without source throws an exception. */
    public function test_create_for_ezl_without_source_exception(): void
    {
        // Create customer order with EZL but no source
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromEzl();
        $customer_order_payload[
            'original_customer_order_id'
        ] = CustomerOrderPayload::generateUniqueOriginalCustomerOrderId();
        $customer_order_payload['source'] = null;

        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);

        $this->expectException(CustomerOrderRequestPayloadException::class);
        $this->expectExceptionMessage('Parameter source can not be empty or has wrong value');
        $this->getTestedInstance()->create($order);
    }

    /** Tests that creating an already existing customer order for EZL throws an exception. */
    public function test_create_for_ezl_order_already_exists(): void
    {
        // Create already existing customer order with EZL
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromEzl();
        // Make sure that the test is not dependent on another one (eg: a previous test)
        // Use 666 because easylounge is evil!
        $customer_order_payload['original_customer_order_id'] = 'EC666';

        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);

        try {
            $this->getTestedInstance()->create($order);
            $this->fail('Expected exception was not thrown');
        } catch (CustomerOrderAlreadyExistsException $e) {
            $this->assertEquals(666, $e->getCustomerOrderId());
            $this->assertEquals('EC666', $e->getOriginalCustomerOrderId());
            $this->assertEquals('CustomerOrder already exists for original_customer_order_id: EC666', $e->getMessage());
        }
    }

    /** Tests creating a customer order for CILO excluding tax. */
    public function test_create_for_cilo_excluding_tax(): void
    {
        // Create customer order for CILO excluding tax
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPayload::getValidPayloadFromCilo(),
            CustomerOrderCreationContextDto::class
        );
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);

        // Verify that the customer order has been inserted
        $this->assertEquals('*********', $customer_order['no_commande_origine']);
        $this->assertEquals(CustomerOrderOrigin::CILO, $customer_order['creation_origine']);
        $this->assertEquals('2019-07-18 00:00:00', $customer_order['date_creation']);
        $this->assertEquals('traitement', $customer_order['flux']);
        $this->assertNull($customer_order['V_statut_traitement']);
        $this->assertEquals('************', $customer_order['ip']);
        $this->assertEquals('N', $customer_order['rappel_client']);
        $this->assertNull($customer_order['depot_emport']);
        $this->assertEquals('oui', $customer_order['detaxe_export']);
        $this->assertEquals(0, $customer_order['cmd_intragroupe']);
        $this->assertEquals(1518400, $customer_order['id_prospect']);
        $this->assertNull($customer_order['id_devis']);
        $this->assertNull($customer_order['quote_id']);
        $this->assertEquals('', $customer_order['commentaire_facture']);
        $this->assertNull($customer_order['promotion_id']);
        $this->assertEquals(2, $customer_order['id_transporteur']);
        $this->assertEquals(1, $customer_order['id_pdt_transporteur']);
        $this->assertNull($customer_order['tpt_option_code']);
        $this->assertNull($customer_order['warehouse_id']);
        $this->assertEquals(10, $customer_order['sales_channel_id']);

        // Billing address assertions
        $this->assertEquals('particulier', $customer_order['cnt_fct_type']);
        $this->assertEquals('<EMAIL>', $customer_order['cnt_fct_email']);
        $this->assertEquals('CILO', $customer_order['cnt_fct_societe']);
        $this->assertEquals('M.', $customer_order['cnt_fct_civilite']);
        $this->assertEquals('Holst', $customer_order['cnt_fct_nom']);
        $this->assertEquals('Karsten', $customer_order['cnt_fct_prenom']);
        $this->assertEquals('Birk Centerpark 40', $customer_order['cnt_fct_adresse']);
        $this->assertEquals('7400', $customer_order['cnt_fct_code_postal']);
        $this->assertEquals('Herning', $customer_order['cnt_fct_ville']);
        $this->assertEquals(52, $customer_order['cnt_fct_id_pays']);
        $this->assertEquals('', $customer_order['cnt_fct_telephone']);
        $this->assertEquals('004553801044', $customer_order['cnt_fct_mobile']);
        $this->assertEquals('', $customer_order['cnt_fct_fax']);
        $this->assertEquals('NUMTVA', $customer_order['cnt_fct_numero_tva']);

        // Shipping address assertions
        $this->assertEquals('particulier', $customer_order['cnt_lvr_type']);
        $this->assertEquals('<EMAIL>', $customer_order['cnt_lvr_email']);
        $this->assertEquals('', $customer_order['cnt_lvr_societe']);
        $this->assertEquals('M.', $customer_order['cnt_lvr_civilite']);
        $this->assertEquals('Hansen', $customer_order['cnt_lvr_nom']);
        $this->assertEquals('Soren G.', $customer_order['cnt_lvr_prenom']);
        $this->assertEquals('Strandlyst Alle 3 B', $customer_order['cnt_lvr_adresse']);
        $this->assertEquals('02670', $customer_order['cnt_lvr_code_postal']);
        $this->assertEquals('Greve', $customer_order['cnt_lvr_ville']);
        $this->assertEquals(5, $customer_order['cnt_lvr_id_pays']);
        $this->assertEquals('', $customer_order['cnt_lvr_telephone']);
        $this->assertEquals('+4522604660', $customer_order['cnt_lvr_mobile']);
        $this->assertEquals('', $customer_order['cnt_lvr_fax']);
        $this->assertNull($customer_order['cnt_lvr_numero_tva']);

        $customer_order_products = $this->fetchCustomerOrderProducts($customer_order_id);

        // Products assertions
        $this->assertEquals($customer_order_id, $customer_order_products[0]->id_commande);
        $this->assertEquals(81123, $customer_order_products[0]->id_produit);
        $this->assertEquals(1, $customer_order_products[0]->quantite);
        $this->assertEquals(40.0, $customer_order_products[0]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[0]->tva);
        $this->assertEquals(176.35, $customer_order_products[0]->prix_achat);
        $this->assertEquals(
            'Paire de supports d\'enceintes B-Tech Mountlogic BT77 Noir',
            $customer_order_products[0]->description
        );
        $this->assertEquals(0, $customer_order_products[0]->remise_montant);
        $this->assertNull($customer_order_products[0]->remise_type);
        $this->assertNull($customer_order_products[0]->remise_description);
        $this->assertEquals(0, $customer_order_products[0]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[0]->prix_sorecop);
        $this->assertNull($customer_order_products[0]->groupe_type);
        $this->assertNull($customer_order_products[0]->groupe_description);

        // Product 2 assertions
        $this->assertEquals($customer_order_id, $customer_order_products[1]->id_commande);
        $this->assertEquals(81078, $customer_order_products[1]->id_produit);
        $this->assertEquals(1, $customer_order_products[1]->quantite);
        $this->assertEquals(1200.0, $customer_order_products[1]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[1]->tva);
        $this->assertEquals(131.58, $customer_order_products[1]->prix_achat);
        $this->assertEquals('Récepteur Audio Bluetooth APTX Arcam rBlink', $customer_order_products[1]->description);
        $this->assertNull($customer_order_products[1]->remise_type);
        $this->assertNull($customer_order_products[1]->remise_description);
        $this->assertEquals(0, $customer_order_products[1]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[1]->prix_sorecop);
        $this->assertNull($customer_order_products[1]->groupe_type);
        $this->assertNull($customer_order_products[1]->groupe_description);

        // Shipment method assertions
        $this->assertEquals($customer_order_id, $customer_order_products[2]->id_commande);
        $this->assertEquals(Product::SHIPMENT_PRODUCT_ID, $customer_order_products[2]->id_produit);
        $this->assertEquals(1, $customer_order_products[2]->quantite);
        $this->assertEquals(0.0, $customer_order_products[2]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[2]->tva);
        $this->assertEquals(0.0, $customer_order_products[2]->prix_achat);
        $this->assertEquals(
            ProductV2Entity::SHIPPING_COST_PRODUCT_DESCRIPTION,
            $customer_order_products[2]->description
        );
        $this->assertNull($customer_order_products[2]->remise_type);
        $this->assertEquals(0, $customer_order_products[2]->remise_montant);
        $this->assertNull($customer_order_products[2]->remise_description);
        $this->assertEquals(0, $customer_order_products[2]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[2]->prix_sorecop);
        $this->assertNull($customer_order_products[2]->groupe_type);
        $this->assertNull($customer_order_products[2]->groupe_description);

        $customer_order_payments = $this->fetchCustomerOrderPayments($customer_order_id);

        // Payments assertions
        $this->assertEquals($customer_order_id, $customer_order_payments[0]->id_commande);
        $this->assertEquals(92, $customer_order_payments[0]->id_paiement);
        $this->assertEquals(1, $customer_order_payments[0]->id_unique);
        $this->assertEquals('paiement', $customer_order_payments[0]->type);
        $this->assertNull($customer_order_payments[0]->warehouse_id);
        $this->assertEquals('2019-07-18 00:00:00', $customer_order_payments[0]->creation_date);
        $this->assertEquals('backoffice', $customer_order_payments[0]->creation_usr);
        $this->assertEquals(1000.0, $customer_order_payments[0]->creation_montant);
        $this->assertEquals('*********-1', $customer_order_payments[0]->creation_justificatif);
        $this->assertEquals('cilo.dk', $customer_order_payments[0]->creation_origine);

        // Tags assertions
        $this->assertCount(1, $this->fetchCustomerOrderTags($customer_order_id));
        $this->assertEquals(
            [
                [
                    'id_commande' => (string) $customer_order_id,
                    'tag_id' => 'source.intragroup.intragroup',
                    'meta' => '{}',
                ],
            ],
            $this->fetchCustomerOrderTags($customer_order_id)
        );

        // No task or comment created
        $this->assertCount(1, $this->fetchTask($customer_order_id));
        $this->assertCount(1, $this->fetchComment($customer_order_id));
    }

    /** Tests creating a customer order for EZL without carrier. */
    public function test_create_for_ezl_without_carrier(): void
    {
        // Create a customer order without imposed carrier
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromEzl();
        $customer_order_payload['shipment_method']['shipment_method_id'] = null;
        $customer_order_payload['original_customer_order_id'] = 'EC1245';
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;
        $this->acceptCustomerOrderPayments($result->customer_order_id);

        // Verify that the customer order has been inserted
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $this->assertEquals('EC1245', $customer_order['no_commande_origine']);
        $this->assertEquals('ecranlounge.com', $customer_order['creation_origine']);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order['date_creation']);
        $this->assertEquals('traitement', $customer_order['flux']);
        $this->assertEquals('lvr_attente', $customer_order['V_statut_traitement']);
        $this->assertEquals('*************', $customer_order['ip']);
        $this->assertEquals('N', $customer_order['rappel_client']);
        $this->assertNull($customer_order['depot_emport']);
        $this->assertEquals('non', $customer_order['detaxe_export']);
        $this->assertEquals(0, $customer_order['cmd_intragroupe']);
        $this->assertEquals(314559, $customer_order['id_prospect']);
        $this->assertNull($customer_order['id_devis']);
        $this->assertNull($customer_order['quote_id']);
        $this->assertEquals('', $customer_order['commentaire_facture']);
        $this->assertNull($customer_order['promotion_id']);
        $this->assertEquals(50, $customer_order['id_transporteur']);
        $this->assertEquals(65, $customer_order['id_pdt_transporteur']);
        $this->assertNull($customer_order['tpt_option_code']);
        $this->assertNull($customer_order['warehouse_id']);
        $this->assertEquals(2, $customer_order['sales_channel_id']);

        // Products assertions
        $customer_order_products = $this->fetchCustomerOrderProducts($customer_order_id);
        $this->assertCount(3, $customer_order_products);

        // Shipment method assertions
        $this->assertEquals($customer_order_id, $customer_order_products[2]->id_commande);
        $this->assertEquals(Product::SHIPMENT_PRODUCT_ID, $customer_order_products[2]->id_produit);
        $this->assertEquals(1, $customer_order_products[2]->quantite);
        $this->assertEquals(4.99, $customer_order_products[2]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[2]->tva);
        $this->assertEquals(0.0, $customer_order_products[2]->prix_achat);
        $this->assertEquals(
            ProductV2Entity::SHIPPING_COST_PRODUCT_DESCRIPTION,
            $customer_order_products[2]->description
        );
        $this->assertNull($customer_order_products[2]->remise_type);
        $this->assertEquals(0, $customer_order_products[2]->remise_montant);
        $this->assertNull($customer_order_products[2]->remise_description);
        $this->assertEquals(0, $customer_order_products[2]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[2]->prix_sorecop);
        $this->assertNull($customer_order_products[2]->groupe_type);
        $this->assertNull($customer_order_products[2]->groupe_description);

        $customer_order_payments = $this->fetchCustomerOrderPayments($customer_order_id);

        // Payments assertions
        $this->assertEquals($customer_order_id, $customer_order_payments[0]->id_commande);
        $this->assertEquals(74, $customer_order_payments[0]->id_paiement);
        $this->assertEquals(1, $customer_order_payments[0]->id_unique);
        $this->assertEquals('paiement', $customer_order_payments[0]->type);
        $this->assertNull($customer_order_payments[0]->warehouse_id);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order_payments[0]->creation_date);
        $this->assertEquals('backoffice', $customer_order_payments[0]->creation_usr);
        $this->assertEquals(1144.99, $customer_order_payments[0]->creation_montant);
        $this->assertEquals('EC1245-1', $customer_order_payments[0]->creation_justificatif);
        $this->assertEquals('ecranlounge.com', $customer_order_payments[0]->creation_origine);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order_payments[0]->acceptation_date);
        $this->assertEquals('backoffice', $customer_order_payments[0]->acceptation_usr);
        $this->assertEquals(1144.99, $customer_order_payments[0]->acceptation_montant);
        $this->assertEquals('EC1245-1', $customer_order_payments[0]->acceptation_justificatif);

        // Statistics assertions
        $this->assertCount(3, $this->fetchCustomerOrderProductsStatInitial($customer_order_id));

        // Tags assertions
        $this->assertCount(1, $this->fetchCustomerOrderTags($customer_order_id));

        // No task or comment created
        $this->assertCount(0, $this->fetchTask($customer_order_id));
        $this->assertCount(0, $this->fetchComment($customer_order_id));
    }

    /** Tests creating a customer order for EZL with amount mismatch. */
    public function test_create_for_ezl_with_amount_mismatch(): void
    {
        // Create a customer order with amount mismatch
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromEzl();
        $customer_order_payload['original_customer_order_id'] = 'EC1247';
        $customer_order_payload['payments'][0]['amount'] = 0;
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        // Verify that the customer order has been inserted
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $this->assertNotEmpty($customer_order);

        // Verify that a task has been created
        $tasks = $this->fetchTask($customer_order_id);
        $this->assertCount(1, $tasks);
        $this->assertEquals((string) $customer_order_id, $tasks[0]->id_commande);
        $this->assertEquals('20', $tasks[0]->id_type);
        $this->assertEquals('1185', $tasks[0]->id_utilisateur);
        $this->assertEquals('La somme des paiements est différente de la somme des produits.', $tasks[0]->sujet);

        // Verify that a comment has been created
        $comments = $this->fetchComment($customer_order_id);
        $this->assertCount(1, $comments);
        $this->assertEquals((string) $customer_order_id, $comments[0]->id_commande);
        $this->assertEquals('backoffice', $comments[0]->utilisateur);
        $this->assertEquals(
            'La somme des paiements est différente de la somme des produits.',
            $comments[0]->commentaire
        );
    }

    /** Tests creating a customer order for Amazon marketplace. */
    public function test_create_for_market_amazon(): void
    {
        $expected_success_response = <<<JSON
        {
          "status": true,
          "account": {
            "customer_id": ********,
            "email": "<EMAIL>",
            "is_active": false
          }
        }
        JSON;

        RpcClientServiceMock::savedResult(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_GET_OR_CREATE_METHOD,
            $expected_success_response
        );

        // Create customer order for Amazon marketplace
        $order = $this->getSerializer()->denormalize(
            CustomerOrderPayload::getValidPayloadFromAmazonDe(),
            CustomerOrderCreationContextDto::class
        );
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);

        // Verify that the customer order has been inserted
        $this->assertEquals('028-1606154-3786710', $customer_order['no_commande_origine']);
        $this->assertEquals(CustomerOrderOrigin::AMAZON_DE, $customer_order['creation_origine']);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order['date_creation']);
        $this->assertEquals('traitement', $customer_order['flux']);
        $this->assertNull($customer_order['V_statut_traitement']);
        $this->assertEquals('0.0.0.0', $customer_order['ip']);
        $this->assertEquals('N', $customer_order['rappel_client']);
        $this->assertNull($customer_order['depot_emport']);
        $this->assertEquals('non', $customer_order['detaxe_export']);
        $this->assertEquals(0, $customer_order['cmd_intragroupe']);
        $this->assertEquals(********, $customer_order['id_prospect']);
        $this->assertNull($customer_order['id_devis']);
        $this->assertNull($customer_order['quote_id']);
        $this->assertEquals('', $customer_order['commentaire_facture']);
        $this->assertNull($customer_order['promotion_id']);
        $this->assertNull($customer_order['id_transporteur']);
        $this->assertNull($customer_order['id_pdt_transporteur']);
        $this->assertNull($customer_order['tpt_option_code']);
        $this->assertNull($customer_order['warehouse_id']);
        $this->assertEquals(6, $customer_order['sales_channel_id']);

        // Billing address assertions
        $this->assertEquals('particulier', $customer_order['cnt_fct_type']);
        $this->assertEquals('<EMAIL>', $customer_order['cnt_fct_email']);
        $this->assertEquals('', $customer_order['cnt_fct_societe']);
        $this->assertEquals('M.', $customer_order['cnt_fct_civilite']);
        $this->assertEquals('TERIEUR', $customer_order['cnt_fct_nom']);
        $this->assertEquals('Alain', $customer_order['cnt_fct_prenom']);
        $this->assertEquals('1 rue des fleurs', $customer_order['cnt_fct_adresse']);
        $this->assertEquals('44100', $customer_order['cnt_fct_code_postal']);
        $this->assertEquals('NANTES', $customer_order['cnt_fct_ville']);
        $this->assertEquals(67, $customer_order['cnt_fct_id_pays']);
        $this->assertEquals('0606060606', $customer_order['cnt_fct_telephone']);
        $this->assertEquals('0606060607', $customer_order['cnt_fct_mobile']);
        $this->assertEquals('', $customer_order['cnt_fct_fax']);
        $this->assertNull($customer_order['cnt_fct_numero_tva']);

        // Shipping address assertions
        $this->assertEquals('particulier', $customer_order['cnt_lvr_type']);
        $this->assertEquals('<EMAIL>', $customer_order['cnt_lvr_email']);
        $this->assertEquals('', $customer_order['cnt_lvr_societe']);
        $this->assertEquals('M.', $customer_order['cnt_lvr_civilite']);
        $this->assertEquals('DURE', $customer_order['cnt_lvr_nom']);
        $this->assertEquals('Laure', $customer_order['cnt_lvr_prenom']);
        $this->assertEquals('111 route de paris', $customer_order['cnt_lvr_adresse']);
        $this->assertEquals('44000', $customer_order['cnt_lvr_code_postal']);
        $this->assertEquals('NANTES', $customer_order['cnt_lvr_ville']);
        $this->assertEquals(67, $customer_order['cnt_lvr_id_pays']);
        $this->assertEquals('0707070707', $customer_order['cnt_lvr_telephone']);
        $this->assertEquals('0707070708', $customer_order['cnt_lvr_mobile']);
        $this->assertEquals('', $customer_order['cnt_lvr_fax']);
        $this->assertNull($customer_order['cnt_lvr_numero_tva']);

        $customer_order_products = $this->fetchCustomerOrderProducts($customer_order_id);

        // Products assertions
        $this->assertEquals($customer_order_id, $customer_order_products[0]->id_commande);
        $this->assertEquals(81123, $customer_order_products[0]->id_produit);
        $this->assertEquals(1, $customer_order_products[0]->quantite);
        $this->assertEquals(40.0, $customer_order_products[0]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[0]->tva);
        $this->assertEquals(176.35, $customer_order_products[0]->prix_achat);
        $this->assertEquals(
            'Paire de supports d\'enceintes B-Tech Mountlogic BT77 Noir',
            $customer_order_products[0]->description
        );
        $this->assertEquals(0, $customer_order_products[0]->remise_montant);
        $this->assertNull($customer_order_products[0]->remise_type);
        $this->assertNull($customer_order_products[0]->remise_description);
        $this->assertEquals(0, $customer_order_products[0]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[0]->prix_sorecop);
        $this->assertNull($customer_order_products[0]->groupe_type);
        $this->assertNull($customer_order_products[0]->groupe_description);

        // Shipment method assertions
        $this->assertEquals($customer_order_id, $customer_order_products[1]->id_commande);
        $this->assertEquals(Product::SHIPMENT_PRODUCT_ID, $customer_order_products[1]->id_produit);
        $this->assertEquals(1, $customer_order_products[1]->quantite);
        $this->assertEquals(4.99, $customer_order_products[1]->prix_vente);
        $this->assertEquals(0.2, $customer_order_products[1]->tva);
        $this->assertEquals(0.0, $customer_order_products[1]->prix_achat);
        $this->assertEquals(
            ProductV2Entity::SHIPPING_COST_PRODUCT_DESCRIPTION,
            $customer_order_products[1]->description
        );
        $this->assertNull($customer_order_products[1]->remise_type);
        $this->assertEquals(0, $customer_order_products[1]->remise_montant);
        $this->assertNull($customer_order_products[1]->remise_description);
        $this->assertEquals(0, $customer_order_products[1]->prix_ecotaxe);
        $this->assertEquals(0, $customer_order_products[1]->prix_sorecop);
        $this->assertNull($customer_order_products[1]->groupe_type);
        $this->assertNull($customer_order_products[1]->groupe_description);

        $customer_order_payments = $this->fetchCustomerOrderPayments($customer_order_id);

        // Payments assertions
        $this->assertEquals($customer_order_id, $customer_order_payments[0]->id_commande);
        $this->assertEquals(95, $customer_order_payments[0]->id_paiement);
        $this->assertEquals(1, $customer_order_payments[0]->id_unique);
        $this->assertEquals('paiement', $customer_order_payments[0]->type);
        $this->assertNull($customer_order_payments[0]->warehouse_id);
        $this->assertEquals('2022-01-01 00:00:00', $customer_order_payments[0]->creation_date);
        $this->assertEquals('backoffice', $customer_order_payments[0]->creation_usr);
        $this->assertEquals(44.99, $customer_order_payments[0]->creation_montant);
        $this->assertEquals('028-1606154-3786710-1', $customer_order_payments[0]->creation_justificatif);
        $this->assertEquals(CustomerOrderOrigin::AMAZON_DE, $customer_order_payments[0]->creation_origine);

        // Statistics assertions
        $this->assertCount(2, $this->fetchCustomerOrderProductsStatInitial($customer_order_id));

        // Tags assertions
        $this->assertCount(1, $this->fetchCustomerOrderTags($customer_order_id));
        $this->assertEquals(
            [
                [
                    'id_commande' => (string) $customer_order_id,
                    'tag_id' => CustomerOrderTag::SOURCE_AMAZON_DE,
                    'meta' => '{}',
                ],
            ],
            $this->fetchCustomerOrderTags($customer_order_id)
        );

        // No task or comment created
        $this->assertCount(0, $this->fetchTask($customer_order_id));
        $this->assertCount(0, $this->fetchComment($customer_order_id));
    }

    /** Tests creating a customer order for Amazon marketplace B2B with tax included. */
    public function test_create_for_market_amazon_b2b_ttc(): void
    {
        $expected_success_response = <<<JSON
        {
          "status": true,
          "account": {
            "customer_id": ********,
            "email": "<EMAIL>",
            "is_active": false
          }
        }
        JSON;

        RpcClientServiceMock::savedResult(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_GET_OR_CREATE_METHOD,
            $expected_success_response
        );

        // Create customer order for Amazon marketplace B2B with tax included
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromAmazonDe();
        $customer_order_payload['original_customer_order_id'] = '028-1606154-3786712';
        $customer_order_payload['is_amazon_business'] = true;

        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;
        $this->assertNotEmpty($this->fetchOneCustomerOrder($result->customer_order_id));

        // Tags assertions
        $this->assertCount(2, $this->fetchCustomerOrderTags($customer_order_id));
        $this->assertEquals(
            [
                [
                    'id_commande' => (string) $customer_order_id,
                    'tag_id' => CustomerOrderTag::AMAZON_BUSINESS,
                    'meta' => '{}',
                ],
                [
                    'id_commande' => (string) $customer_order_id,
                    'tag_id' => CustomerOrderTag::SOURCE_AMAZON_DE,
                    'meta' => '{}',
                ],
            ],
            $this->fetchCustomerOrderTags($customer_order_id)
        );
    }

    /** Tests creating a customer order for Amazon marketplace B2B with tax excluded. */
    public function test_create_for_market_amazon_b2b_ht(): void
    {
        $expected_success_response = <<<JSON
        {
          "status": true,
          "account": {
            "customer_id": ********,
            "email": "<EMAIL>",
            "is_active": false
          }
        }
        JSON;

        RpcClientServiceMock::savedResult(
            BoCmsRpcMethodReferential::SERVER_NAME,
            BoCmsRpcMethodReferential::CUSTOMER_GET_OR_CREATE_METHOD,
            $expected_success_response
        );

        // Create customer order for Amazon marketplace B2B with tax excluded
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromAmazonDe();
        $customer_order_payload['original_customer_order_id'] = '028-1606154-3786713';
        $customer_order_payload['is_amazon_business'] = true;
        $customer_order_payload['is_excluding_tax'] = true;

        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;
        $this->assertNotEmpty($this->fetchOneCustomerOrder($result->customer_order_id));

        // Tags assertions
        $this->assertCount(2, $this->fetchCustomerOrderTags($customer_order_id));
        $this->assertEquals(
            [
                [
                    'id_commande' => (string) $customer_order_id,
                    'tag_id' => CustomerOrderTag::AMAZON_BUSINESS,
                    'meta' => '{"HT": true}',
                ],
                [
                    'id_commande' => (string) $customer_order_id,
                    'tag_id' => CustomerOrderTag::SOURCE_AMAZON_DE,
                    'meta' => '{}',
                ],
            ],
            $this->fetchCustomerOrderTags($customer_order_id)
        );
    }

    /** Tests creating a customer order with SVD gift card. */
    public function test_create_with_svd_gift_card(): void
    {
        // Create customer order with SVD gift card
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithProductAndBundle();
        $card_number = '77345364361619879088';

        // Verify gift card is not burned
        $gift_cards = $this->fetchSvdGiftCard($card_number);
        $this->assertCount(1, $gift_cards);
        $this->assertEquals($card_number, $gift_cards[0]['no_carte']);
        $this->assertNull($gift_cards[0]['utilisation_id_commande']);
        $this->assertNull($gift_cards[0]['utilisation_date']);

        // Add payment methods to the order
        $customer_order_payload['payments'] = [
            [
                'payment_mean' => 'SVDCC',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 100.0,
                'extra_data' => [
                    'number' => $card_number,
                ],
            ],
            [
                'payment_mean' => 'VIR',
                'created_at' => '2022-01-01 00:00:00',
                'amount' => 349.3,
                'extra_data' => [],
            ],
        ];

        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;
        $this->assertNotEmpty($this->fetchOneCustomerOrder($result->customer_order_id));

        // Verify gift card is burned
        $gift_cards = $this->fetchSvdGiftCard($card_number);
        $this->assertCount(1, $gift_cards);
        $this->assertEquals($card_number, $gift_cards[0]['no_carte']);
        $this->assertEquals($customer_order_id, $gift_cards[0]['utilisation_id_commande']);
        $this->assertNotNull($gift_cards[0]['utilisation_date']);
    }

    /** Tests creating a customer order with an unknown customer ID. */
    public function test_create_with_unknown_customer_id(): void
    {
        $unknown_customer_id = 999999999;
        $unknown_customer_email = '<EMAIL>';

        // Verify customer does not exist before
        $customer = $this->fetchCustomer($unknown_customer_id);
        $this->assertFalse($customer);

        // Create customer order using unknown customer_id
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $customer_order_payload['customer_id'] = $unknown_customer_id;
        $customer_order_payload['billing_address']['email'] = $unknown_customer_email;
        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order = $this->fetchOneCustomerOrder($result->customer_order_id);
        $this->assertEquals($unknown_customer_id, $customer_order['id_prospect']);

        // Verify customer has been created
        $customer = $this->fetchCustomer($unknown_customer_id);
        $this->assertNotFalse($customer);
        $this->assertEquals((string) $unknown_customer_id, $customer->id_prospect);
        $this->assertEquals($unknown_customer_email, $customer->cnt_email);
    }

    /** Tests that creating a customer order with an unknown payment method fails. */
    public function test_should_fail_if_payment_method_is_not_found(): void
    {
        // Create customer order with an unknown payment method
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromSiteWithAQuoteAndPromoCode();
        $customer_order_payload['payments'][0]['payment_mean'] = 'INFINITE_STONKS';

        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);

        $this->expectException(\UnexpectedValueException::class);
        $this->expectExceptionMessage(
            'Could not create payment with payment method "INFINITE_STONKS". Does it exist ?'
        );
        $this->getTestedInstance()->create($order);
    }

    /** Tests cloning a customer order. */
    public function test_clone_customer_order(): void
    {
        // Clone customer order
        $customer_order_payload = CustomerOrderPayload::getValidPayloadFromCustomerOrderCloner();

        $order = $this->getSerializer()->denormalize($customer_order_payload, CustomerOrderCreationContextDto::class);
        $result = $this->getTestedInstance()->create($order);
        $customer_order_id = $result->customer_order_id;

        // Verify comment has been created
        $comments = $this->fetchComment($customer_order_id);
        $this->assertCount(1, $comments);
        $this->assertEquals((string) $customer_order_id, $comments[0]->id_commande);
        $this->assertEquals('gege', $comments[0]->utilisateur);
        $this->assertEquals(
            sprintf(
                'Cette commande est le clone de la commande %s.',
                $customer_order_payload['clone_customer_order_id']
            ),
            $comments[0]->commentaire
        );
    }

    /** Gets the PDO instance. */
    protected function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }

    /**
     * @return mixed
     *
     * @throws \Exception
     */
    protected function fetchOneCustomerOrder(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.commande c
        LEFT JOIN backOffice.commande_relay cr ON c.id_commande = cr.id_commande
        WHERE c.id_commande = :customer_order_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['customer_order_id' => $customer_order_id]);
    }

    /**
     * @return mixed
     *
     * @throws \Exception
     */
    protected function fetchCustomerOrderProducts(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.produit_commande
        WHERE id_commande = :customer_order_id
        ORDER BY id_produit DESC
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['customer_order_id' => $customer_order_id]);
    }

    /**
     * @return mixed
     *
     * @throws \Exception
     */
    protected function fetchCustomerOrderPayments(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.paiement_commande
        WHERE id_commande = :customer_order_id
        ORDER BY id_paiement DESC
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['customer_order_id' => $customer_order_id]);
    }

    /**
     * @return mixed
     *
     * @throws \Exception
     */
    protected function fetchTask(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.TC_tache
        WHERE id_commande = :customer_order_id
        ORDER BY id DESC
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['customer_order_id' => $customer_order_id]);
    }

    /**
     * @return mixed
     *
     * @throws \Exception
     */
    protected function fetchComment(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.commentaire_commande
        WHERE id_commande = :customer_order_id
        ORDER BY id DESC
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['customer_order_id' => $customer_order_id]);
    }

    /**
     * @return mixed
     *
     * @throws \Exception
     */
    protected function fetchCustomerOrderProductsStatInitial(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM  data_warehouse.customer_order_product_initial
        WHERE customer_order_id = :customer_order_id
        ORDER BY product_id DESC
        SQL;

        return $this->getPdo()->fetchObjects($sql, ['customer_order_id' => $customer_order_id]);
    }

    /**
     * @return array
     *
     * @throws \Exception
     */
    protected function fetchCustomerOrderTags(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM  backOffice.commande_tag
        WHERE id_commande = :customer_order_id
        SQL;

        return $this->getPdo()->fetchAll($sql, ['customer_order_id' => $customer_order_id]);
    }

    /**
     * @return array|false
     *
     * @throws \Exception
     */
    protected function fetchCustomerOrderChronoPreciseAppointment(int $customer_order_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM  backOffice.BO_CMD_chrono_precise
        WHERE id_commande = :customer_order_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['customer_order_id' => $customer_order_id]);
    }

    protected function fetchSvdGiftCard(string $card_number): array
    {
        $sql = <<<SQL
        SELECT *
        FROM  backOffice.PMT_carte_cadeau
        WHERE no_carte = :card_number
        SQL;

        return $this->getPdo()->fetchAll($sql, ['card_number' => $card_number]);
    }

    protected function acceptCustomerOrderPayments(int $customer_order_id)
    {
        $sql = <<<SQL
        UPDATE backOffice.paiement_commande
        SET acceptation_date = creation_date,
            acceptation_usr = 'backoffice',
            acceptation_montant = creation_montant,
            acceptation_justificatif = creation_justificatif
        WHERE id_commande = :customer_order_id
        SQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => $customer_order_id]);

        $sql = <<<SQL
        UPDATE backOffice.commande
        SET V_statut_traitement = backOffice.CMD_statut_traitement(:customer_order_id)
        WHERE id_commande = :customer_order_id
        SQL;

        $this->getPdo()->fetchAffected($sql, ['customer_order_id' => $customer_order_id]);
    }

    /** @return false|object */
    protected function fetchCustomer(int $customer_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM  backOffice.prospect
        WHERE id_prospect = :customer_id
        SQL;

        $customer = $this->getPdo()->fetchOne($sql, ['customer_id' => $customer_id]);

        return $customer ? (object) $customer : false;
    }
}
