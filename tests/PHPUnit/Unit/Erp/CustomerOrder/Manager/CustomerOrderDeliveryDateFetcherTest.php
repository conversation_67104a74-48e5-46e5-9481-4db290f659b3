<?php

namespace PHPUnit\Unit\Erp\CustomerOrder\Manager;

use PHPUnit\Framework\TestCase;
use SonVideo\Erp\Article\Mysql\Repository\ArticleRepository;
use SonVideo\Erp\Carrier\Dto\ShipmentMethod\EligibilityEnvelopeDto;
use SonVideo\Erp\Carrier\Manager\Eligibility\ShipmentMethodResolver;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderCreationContextEntity;
use SonVideo\Erp\CustomerOrder\Entity\CreationContext\CustomerOrderProductCreationContextEntity;
use SonVideo\Erp\CustomerOrder\Manager\CustomerOrderDeliveryDateFetcher as TestedClass;
use SonVideo\Erp\Referential\Product;

class CustomerOrderDeliveryDateFetcherTest extends TestCase
{
    private TestedClass $tested_instance;
    private ShipmentMethodResolver $shipment_method_resolver_mock;
    private ArticleRepository $article_repository_mock;

    protected function setUp(): void
    {
        $this->shipment_method_resolver_mock = $this->createMock(ShipmentMethodResolver::class);
        $this->article_repository_mock = $this->createMock(ArticleRepository::class);

        $this->tested_instance = new TestedClass($this->shipment_method_resolver_mock, $this->article_repository_mock);
    }

    public function test_fetch_delivery_date_returns_null_when_no_articles(): void
    {
        $context = $this->createCustomerOrderCreationContext([]);

        $result = $this->tested_instance->fetchDeliveryDate($context, 'FR');

        $this->assertNull($result);
    }

    public function test_fetch_delivery_date_returns_null_when_max_shipping_delay_is_null(): void
    {
        $articles = [
            $this->createProductContext(1, Product::TYPE_ARTICLE),
            $this->createProductContext(2, Product::TYPE_ARTICLE),
        ];
        $context = $this->createCustomerOrderCreationContext($articles);

        $this->article_repository_mock
            ->expects($this->once())
            ->method('getMaxShippingDelay')
            ->with([1, 2])
            ->willReturn(null);

        $result = $this->tested_instance->fetchDeliveryDate($context, 'FR');

        $this->assertNull($result);
    }

    public function test_fetch_delivery_date_returns_null_when_resolver_returns_null_delay(): void
    {
        $articles = [$this->createProductContext(1, Product::TYPE_ARTICLE)];
        $context = $this->createCustomerOrderCreationContext($articles);

        $this->article_repository_mock
            ->expects($this->once())
            ->method('getMaxShippingDelay')
            ->with([1])
            ->willReturn(5);

        $this->shipment_method_resolver_mock
            ->expects($this->once())
            ->method('getDelay')
            ->with(123, $this->isInstanceOf(EligibilityEnvelopeDto::class))
            ->willReturn(null);

        $result = $this->tested_instance->fetchDeliveryDate($context, 'FR');

        $this->assertNull($result);
    }

    public function test_fetch_delivery_date_returns_correct_date_when_conditions_are_met(): void
    {
        $articles = [
            $this->createProductContext(1, Product::TYPE_ARTICLE),
            $this->createProductContext(2, Product::TYPE_ARTICLE),
        ];
        $context = $this->createCustomerOrderCreationContext($articles);

        $this->article_repository_mock
            ->expects($this->once())
            ->method('getMaxShippingDelay')
            ->with([1, 2])
            ->willReturn(3);

        $this->shipment_method_resolver_mock
            ->expects($this->once())
            ->method('getDelay')
            ->with(123, $this->isInstanceOf(EligibilityEnvelopeDto::class))
            ->willReturn(7);

        $result = $this->tested_instance->fetchDeliveryDate($context, 'FR');

        $this->assertInstanceOf(\DateTimeInterface::class, $result);
        $expected_date = (new \DateTime())->modify('+7 days');
        $this->assertEquals($expected_date->format('Y-m-d'), $result->format('Y-m-d'));
    }

    public function test_fetch_delivery_date_filters_only_articles(): void
    {
        $products = [
            $this->createProductContext(1, Product::TYPE_ARTICLE),
            $this->createProductContext(2, Product::TYPE_GENERIQUE), // Should be filtered out
            $this->createProductContext(3, Product::TYPE_PACKAGE),
            $this->createProductContext(4, 'other_type'), // Should be filtered out
        ];
        $context = $this->createCustomerOrderCreationContext($products);

        $this->article_repository_mock
            ->expects($this->once())
            ->method('getMaxShippingDelay')
            ->with([1, 3]) // Only articles (and packages) should be passed
            ->willReturn(2);

        $this->shipment_method_resolver_mock->method('getDelay')->willReturn(5);

        $result = $this->tested_instance->fetchDeliveryDate($context, 'FR');

        $this->assertInstanceOf(\DateTimeInterface::class, $result);
    }

    public function test_fetch_delivery_date_builds_envelope_correctly_with_tax_excluded(): void
    {
        $articles = [$this->createProductContext(1, Product::TYPE_ARTICLE)];
        $context = $this->createCustomerOrderCreationContext($articles);
        $context->is_excluding_tax = 'oui'; // Tax excluded

        $this->article_repository_mock->method('getMaxShippingDelay')->willReturn(3);

        $this->shipment_method_resolver_mock
            ->expects($this->once())
            ->method('getDelay')
            ->with(
                123,
                $this->callback(function (EligibilityEnvelopeDto $envelope) {
                    // Verify envelope structure
                    $this->assertCount(1, $envelope->items);
                    $this->assertEquals(3, $envelope->shipping_delay);

                    // Verify item structure for tax excluded
                    $item = $envelope->items[0];
                    $this->assertEquals('SKU-1', $item->sku);
                    $this->assertEquals(2, $item->quantity);
                    $this->assertEquals(100.0, $item->price);
                    $this->assertEquals(200.0, $item->total_price);
                    // Tax excluded: price_vat_excluded should be selling_price_tax_included
                    $this->assertEquals(100.0, $item->price_vat_excluded);

                    // Verify shipping address
                    $address = $envelope->shipping_address;
                    $this->assertEquals('M.', $address->title);
                    $this->assertEquals('John', $address->firstname);
                    $this->assertEquals('Doe', $address->lastname);
                    $this->assertEquals('FR', $address->country_code);

                    return true;
                })
            )
            ->willReturn(5);

        $result = $this->tested_instance->fetchDeliveryDate($context, 'FR');

        $this->assertInstanceOf(\DateTimeInterface::class, $result);
    }

    public function test_fetch_delivery_date_builds_envelope_correctly_with_tax_included(): void
    {
        $articles = [$this->createProductContext(1, Product::TYPE_ARTICLE)];
        $context = $this->createCustomerOrderCreationContext($articles);
        $context->is_excluding_tax = 'non'; // Tax included

        $this->article_repository_mock->method('getMaxShippingDelay')->willReturn(2);

        $this->shipment_method_resolver_mock
            ->expects($this->once())
            ->method('getDelay')
            ->with(
                123,
                $this->callback(function (EligibilityEnvelopeDto $envelope) {
                    // Verify item structure for tax included
                    $item = $envelope->items[0];
                    // Tax included: price_vat_excluded should be calculated
                    $expected_price_vat_excluded = 100.0 / (1 + 0.2); // 83.33
                    $this->assertEquals($expected_price_vat_excluded, $item->price_vat_excluded, '', 0.01);

                    return true;
                })
            )
            ->willReturn(4);

        $result = $this->tested_instance->fetchDeliveryDate($context, 'FR');

        $this->assertInstanceOf(\DateTimeInterface::class, $result);
    }

    private function createCustomerOrderCreationContext(array $products): CustomerOrderCreationContextEntity
    {
        $context = new CustomerOrderCreationContextEntity();
        $context->shipment_method_id = 123;
        $context->products = $products;
        $context->is_excluding_tax = 'oui';
        $context->shipping_address_civility = 'M.';
        $context->shipping_address_firstname = 'John';
        $context->shipping_address_lastname = 'Doe';
        $context->shipping_address_cellphone = '0123456789';
        $context->shipping_address_city = 'Paris';
        $context->shipping_address_postal_code = '75001';
        $context->shipping_address_address = '123 Rue de la Paix';

        return $context;
    }

    private function createProductContext(int $product_id, string $type): CustomerOrderProductCreationContextEntity
    {
        $product = new CustomerOrderProductCreationContextEntity();
        $product->product_id = $product_id;
        $product->type = $type;
        $product->sku = "SKU-{$product_id}";
        $product->quantity = 2;
        $product->selling_price_tax_included = 100.0;
        $product->vat = 0.2;

        return $product;
    }
}
