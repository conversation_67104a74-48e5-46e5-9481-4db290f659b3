<?php

namespace PHPUnit\Unit\Erp\AntiFraud\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Sql\Helper\Pager;
use App\Sql\Query\QueryBuilder;
use Psr\Log\LoggerInterface;
use SonVideo\Erp\AntiFraud\Entity\AntiFraudCustomerOrder;
use SonVideo\Erp\AntiFraud\Entity\AntiFraudCustomerOrderPayment;
use SonVideo\Erp\AntiFraud\Entity\AntiFraudFormattedReason;
use SonVideo\Erp\AntiFraud\Entity\ArticleForAntiFraudModule;
use SonVideo\Erp\AntiFraud\Manager\AntiFraudModule;
use SonVideo\Erp\AntiFraud\Manager\ArticleAntiFraudStatusChecker;
use SonVideo\Erp\AntiFraud\Mysql\Repository\AntiFraudWriteRepository;
use SonVideo\Erp\Country\Mysql\Repository\CountryRepository;
use SonVideo\Erp\CustomerOrder\Referential\CustomerOrderOrigin;
use SonVideo\Erp\CustomerOrderPayment\Mysql\Entity\CustomerOrderPaymentNotAcceptedEntity;
use SonVideo\Erp\Referential\AntiFraudReason;
use SonVideo\Erp\Referential\AntiFraudStatus;
use SonVideo\Erp\Referential\Country;
use SonVideo\Erp\Referential\Payment\PaymentId;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class AntiFraudModuleTest extends KernelTestCase
{
    private SerializerInterface $serializer;
    private QueryBuilder $query_builder;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->serializer = self::$container->get(SerializerInterface::class);
        $this->query_builder = self::$container->get(QueryBuilder::class);
    }

    /** @return CustomerOrderPaymentNotAcceptedEntity[] */
    private function getPaymentToVerify(array $payment = []): array
    {
        return $this->serializer->denormalize(
            [
                array_merge(
                    [
                        'customer_order_payment_id' => 1,
                        'payment_id' => PaymentId::CREDIT_CARD_OGONE,
                        'operation_id' => 'uuid',
                        'customer_order_id' => 222,
                        'auto_status' => 'accepte',
                        'created_proof' => 'ok',
                        'card_warranty_type' => '3DS',
                        'card_origin' => 'FR',
                        'customer_blacklist' => false,
                        'customer_order_origin' => CustomerOrderOrigin::SITE,
                        'billing_address_country_id' => Country::FRANCE_ID,
                        'shipping_address_country_id' => Country::FRANCE_ID,
                        'shipping_address_country_code' => 'FR',
                        'products' => [],
                    ],
                    $payment
                ),
            ],
            CustomerOrderPaymentNotAcceptedEntity::class . '[]'
        );
    }

    private function getTestedInstance(
        AntiFraudWriteRepository $anti_fraud_write_repository,
        LoggerInterface $logger
    ): AntiFraudModule {
        $pager = $this->createMock(Pager::class);
        $pager
            ->expects($this->once())
            ->method('getResults')
            ->willReturn([
                (object) [
                    'country_id' => 67,
                    'name' => 'FRANCE',
                    'country_code' => 'FR',
                    'group' => 'Europe',
                    'display_order' => 1,
                    'postal_code_info' => 'foo',
                ],
            ]);

        $country_repository = $this->createMock(CountryRepository::class);
        $country_repository
            ->expects($this->once())
            ->method('findAllPaginated')
            ->willReturn($pager);

        $anti_fraud_status_checker = $this->createMock(ArticleAntiFraudStatusChecker::class);

        $tested_instance = new AntiFraudModule(
            $anti_fraud_write_repository,
            $country_repository,
            $this->query_builder,
            $this->serializer,
            $anti_fraud_status_checker
        );

        $tested_instance->setLogger($logger);

        return $tested_instance;
    }

    public function test_it_should_be_rejected_with_internal_rules(): void
    {
        // CUSTOMER_IS_BLACKLISTED
        $payments = $this->getPaymentToVerify([
            'customer_blacklist' => true,
        ]);

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrder')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrder) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::REJECTED === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::CUSTOMER_IS_BLACKLISTED);
                })
            );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->exactly(2))->method('notice');

        $kept_payments = $this->getTestedInstance($anti_fraud_write_repository, $logger)->check($payments);
        $this->assertCount(0, $kept_payments);

        // SHIPPING_AND_BILLING_ADDRESS_ARE_DIFFERENT
        $payments = $this->getPaymentToVerify([
            'shipping_address_country_id' => 666,
        ]);

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrder')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrder) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::REJECTED === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::SHIPPING_AND_BILLING_COUNTRIES_ARE_DIFFERENT);
                })
            );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->exactly(2))->method('notice');

        $kept_payments = $this->getTestedInstance($anti_fraud_write_repository, $logger)->check($payments);
        $this->assertCount(0, $kept_payments);

        // PAYMENT_NOT_SUPPORTED
        $payments = $this->getPaymentToVerify([
            'payment_id' => 999999,
        ]);

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrderPayment')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrderPayment) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::NON_ELIGIBLE === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::PAYMENT_NOT_SUPPORTED);
                })
            );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->exactly(2))->method('notice');

        $kept_payments = $this->getTestedInstance($anti_fraud_write_repository, $logger)->check($payments);
        $this->assertCount(0, $kept_payments);

        // MISSING_3D_SECURE
        $payments = $this->getPaymentToVerify([
            'card_warranty_type' => null,
        ]);

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrderPayment')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrderPayment) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::REJECTED === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::MISSING_3D_SECURE);
                })
            );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->exactly(2))->method('notice');

        $kept_payments = $this->getTestedInstance($anti_fraud_write_repository, $logger)->check($payments);
        $this->assertCount(0, $kept_payments);

        // NOT_IN_SCHENGEN_AREA
        $payments = $this->getPaymentToVerify([
            'card_origin' => 'US',
        ]);

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrderPayment')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrderPayment) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::REJECTED === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::CREDIT_CARD_ORIGIN_NOT_IN_SCHENGEN_AREA);
                })
            );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->exactly(2))->method('notice');

        $kept_payments = $this->getTestedInstance($anti_fraud_write_repository, $logger)->check($payments);
        $this->assertCount(0, $kept_payments);

        // SHIPPING_COUNTRY_IS_NOT_IN_SCHENGEN_AREA
        $payments = $this->getPaymentToVerify([
            'shipping_address_country_code' => 'US',
        ]);

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrder')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrder) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::REJECTED === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::SHIPPING_COUNTRY_IS_NOT_IN_SCHENGEN_AREA);
                })
            );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->exactly(2))->method('notice');

        $kept_payments = $this->getTestedInstance($anti_fraud_write_repository, $logger)->check($payments);
        $this->assertCount(0, $kept_payments);

        // AUTO_STATUS_IS_NOT_SUPPORTED
        $payments = $this->getPaymentToVerify([
            'auto_status' => null,
        ]);

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrderPayment')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrderPayment) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::REJECTED === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::AUTO_STATUS_IS_NOT_SUPPORTED);
                })
            );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->exactly(2))->method('notice');

        $kept_payments = $this->getTestedInstance($anti_fraud_write_repository, $logger)->check($payments);
        $this->assertCount(0, $kept_payments);
    }

    public function test_it_should_be_rejected_with_external_rules(): void
    {
        // HAS_INVALID_ARTICLES
        $payments = $this->getPaymentToVerify([
            'products' => [
                ArticleForAntiFraudModule::from([
                    'product_id' => 123,
                    'sku' => 'BAR',
                    'brand_id' => 1,
                    'subcategory_id' => 1,
                    'selling_price' => '100',
                ]),
            ],
        ]);

        $pager = $this->createMock(Pager::class);
        $pager
            ->expects($this->once())
            ->method('getResults')
            ->willReturn([
                (object) [
                    'country_id' => 67,
                    'name' => 'FRANCE',
                    'country_code' => 'FR',
                    'group' => 'Europe',
                    'display_order' => 1,
                    'postal_code_info' => 'foo',
                ],
            ]);

        $country_repository = $this->createMock(CountryRepository::class);
        $country_repository
            ->expects($this->once())
            ->method('findAllPaginated')
            ->willReturn($pager);

        $anti_fraud_status_checker = $this->createMock(ArticleAntiFraudStatusChecker::class);
        $anti_fraud_status_checker
            ->expects($this->once())
            ->method('check')
            ->willReturn(AntiFraudFormattedReason::create(AntiFraudReason::HAS_INVALID_ARTICLES));

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrder')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrder) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::REJECTED === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::HAS_INVALID_ARTICLES);
                })
            );

        $tested_instance = new AntiFraudModule(
            $anti_fraud_write_repository,
            $country_repository,
            $this->query_builder,
            $this->serializer,
            $anti_fraud_status_checker
        );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->exactly(2))->method('notice');

        $tested_instance->setLogger($logger);

        $kept_payments = $tested_instance->check($payments);
        $this->assertCount(0, $kept_payments);
    }

    public function test_it_should_not_be_rejected(): void
    {
        // IS_A_MARKETPLACE_CUSTOMER_ORDER
        $payments = $this->getPaymentToVerify([
            'customer_order_origin' => CustomerOrderOrigin::AMAZON_FR,
        ]);

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrder')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrder) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::PASSED === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::IS_A_MARKETPLACE_CUSTOMER_ORDER);
                })
            );

        $kept_payments = $this->getTestedInstance(
            $anti_fraud_write_repository,
            $this->createMock(LoggerInterface::class)
        )->check($payments);
        $this->assertCount(1, $kept_payments);

        // IS_A_GIFT_CARD_PAYMENT
        $payments = $this->getPaymentToVerify([
            'payment_id' => PaymentId::SVD_GIFT_CARD,
        ]);

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrderPayment')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrderPayment) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::PASSED === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::IS_A_GIFT_CARD_PAYMENT);
                })
            );

        $kept_payments = $this->getTestedInstance(
            $anti_fraud_write_repository,
            $this->createMock(LoggerInterface::class)
        )->check($payments);
        $this->assertCount(1, $kept_payments);

        // ALL_RULES_PASSES
        $payments = $this->getPaymentToVerify();

        $anti_fraud_write_repository = $this->createMock(AntiFraudWriteRepository::class);
        $anti_fraud_write_repository
            ->expects($this->once())
            ->method('upsertForCustomerOrderPayment')
            ->with(
                $this->callback(function ($arg) {
                    if (!$arg instanceof AntiFraudCustomerOrderPayment) {
                        return false;
                    }

                    $arr = $arg->toArray();

                    return AntiFraudStatus::PASSED === $arr['status'] &&
                        false !== strpos($arr['reason'], AntiFraudReason::ALL_RULES_PASSES);
                })
            );

        $logger = $this->createMock(LoggerInterface::class);
        $logger->expects($this->exactly(2))->method('notice');

        $kept_payments = $this->getTestedInstance($anti_fraud_write_repository, $logger)->check($payments);
        $this->assertCount(1, $kept_payments);
    }
}
