<?php

namespace PHPUnit\Unit\Erp\SupplierOrderProduct\Manager;

use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\SupplierOrderProduct\Manager\SupplierOrderProductManager as TestedClass;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SupplierOrderProductManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'wms/supplier_order/post_stock_entry.sql',
            'wms/supplier_order/stock_entry_kpi.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** @throws \Exception */
    protected function getTestedInstance(): TestedClass
    {
        return static::$container->get(TestedClass::class);
    }

    /** @throws \Exception */
    protected function getPdo(): LegacyPdo
    {
        return static::$container->get(LegacyPdo::class);
    }

    public function test_make_stock_entry(): void
    {
        $customer_order_product_lines = $this->fetchCustomerOrderProductLines(81080);

        // Assert initial state
        $lines_with_delivery_date = array_filter($customer_order_product_lines, function ($line) {
            return !is_null($line['effective_supplier_order_delivery_date']);
        });

        $this->assertCount(1, $lines_with_delivery_date, 'Should have exactly one line with delivery date');

        $lines_without_delivery_date = array_filter($customer_order_product_lines, function ($line) {
            return is_null($line['effective_supplier_order_delivery_date']);
        });

        $this->assertCount(4, $lines_without_delivery_date, 'Should have exactly 4 lines without delivery date');

        $product = $this->getProductStock(81080);
        $this->assertEquals(0, $product['quantity'], 'Product quantity should be 0 before stock entry');

        $stock_entry_quantity = 4;
        $this->getTestedInstance()->makeStockEntry(1628, 1, null, 81080, $stock_entry_quantity, 1);

        // Check updated state
        $customer_order_product_lines = $this->fetchCustomerOrderProductLines(81080);

        $running_total = 0;
        foreach ($customer_order_product_lines as $line) {
            if (
                $running_total + $line['quantity'] <= $stock_entry_quantity &&
                in_array(
                    $line['customer_order_product_id'],
                    array_column($lines_without_delivery_date, 'customer_order_product_id')
                )
            ) {
                // This line should have been updated with a delivery date
                $running_total += $line['quantity'];
                $this->assertEquals(
                    (new \DateTime())->format('Y-m-d'),
                    $line['effective_supplier_order_delivery_date']
                );
            } else {
                // Unless it already had one, this line should not have a delivery date
                if (
                    !in_array(
                        $line['customer_order_product_id'],
                        array_column($lines_with_delivery_date, 'customer_order_product_id')
                    )
                ) {
                    $this->assertNull($line['effective_supplier_order_delivery_date']);
                }
            }
        }
    }

    private function fetchCustomerOrderProductLines(int $product_id): array
    {
        $sql = <<<SQL
        SELECT
            pc.id_commande AS customer_order_id,
            pc.id AS customer_order_product_id,
            pc.id_produit AS product_id,
            pc.quantite AS quantity,
            c.date_creation AS creation_date,
            pc.effective_supplier_order_delivery_date AS effective_supplier_order_delivery_date,
            backOffice.CMD_is_paid(c.flux, c.V_statut_traitement) AS is_paid
        FROM produit_commande pc
        INNER JOIN commande c ON c.id_commande = pc.id_commande
        WHERE pc.id_produit=:product_id
        ORDER BY is_paid DESC, creation_date DESC;
        SQL;

        return $this->getPdo()->fetchAll($sql, ['product_id' => $product_id]);
    }

    private function getProductStock(int $product_id): array
    {
        $sql = <<<SQL
        SELECT
            id_produit AS product_id,
            V_quantite_stock AS quantity
        FROM backOffice.article
        WHERE id_produit=:product_id;
        SQL;

        return $this->getPdo()->fetchOne($sql, ['product_id' => $product_id]);
    }
}
