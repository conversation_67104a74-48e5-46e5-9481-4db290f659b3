<?php

namespace PHPUnit\Unit\Erp\Mailing\Manager\SendGiftCard;

use App\Tests\Mock\RpcClientServiceMock;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\Mailing\Exception\EmailRequestPayloadException;
use Son<PERSON>ideo\Erp\Mailing\Manager\SendGiftCard\SendGiftCardEmailDispatcher;
use SonVideo\Erp\Referential\Rpc\HeraldRpcMethodReferential;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class SendGiftCardEmailDispatcherTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    protected function getTestedInstance(): SendGiftCardEmailDispatcher
    {
        return self::$container->get(SendGiftCardEmailDispatcher::class);
    }

    public function test_dispatch_with_invalid_payload(): void
    {
        $invalid_request_payload = <<<JSON
        {
          "to": null,
          "from": {
            "name": "",
            "email": null
          },
          "subject": "[TEST] Votre carte cadeau",
          "context": {
            "expired_at": null,
            "amount": null,
            "card_number": null
          },
          "_rel": null
        }
        JSON;

        $email_dispatcher = $this->getTestedInstance();

        $exception = null;
        try {
            $email_dispatcher->dispatch(json_decode($invalid_request_payload, true));
        } catch (EmailRequestPayloadException $e) {
            $exception = $e;
        }

        $this->assertInstanceOf(EmailRequestPayloadException::class, $exception);
        $this->assertStringContainsString('[to]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[from][name]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[from][email]: This value should not be blank.', $exception->getMessage());
        $this->assertStringContainsString('[cc]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString(
            '[context][expired_at]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][expired_at]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][amount]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][amount]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][card_number]: This value should not be blank.',
            $exception->getMessage()
        );
        $this->assertStringContainsString(
            '[context][card_number]: This value should not be null.',
            $exception->getMessage()
        );
        $this->assertStringContainsString('[_sent_by]: This field is missing.', $exception->getMessage());
        $this->assertStringContainsString('[_rel]: This value should not be null.', $exception->getMessage());
    }

    public function test_dispatch_with_valid_payload_without_rel(): void
    {
        // @info This request is usable in your REST client to test if sending an email with mailjet is working properly
        $valid_request_payload = <<<JSON
        {
          "to": "<EMAIL>",
          "cc": [],
          "from": {
            "name": "[DEV] Son-Vidéo.com",
            "email": "<EMAIL>"
          },
          "subject": "[TEST] Votre carte cadeau",
          "context": {
            "expired_at": "24/02/2023",
            "amount": "1700",
            "card_number": "48353315207104095831"
          },
          "_sent_by": 1234
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertInstanceOf(LoggableSystemEvent::class, $result);
    }

    public function test_dispatch_with_valid_payload_with_rel_and_no_loggable_event(): void
    {
        $valid_request_payload = <<<JSON
        {
          "to": "<EMAIL>",
          "cc": [],
          "from": {
            "name": "[DEV] Son-Vidéo.com",
            "email": "<EMAIL>"
          },
          "subject": "[TEST] Votre carte cadeau",
          "context": {
            "expired_at": "24/02/2023",
            "amount": "1700",
            "card_number": "48353315207104095831"
          },
          "_sent_by": 1234,
          "_rel": {
            "customer_order": 1234
          },
          "_loggable_event": false
        }
        JSON;

        $expected_success_response = <<<JSON
        {
          "success": true,
          "reason": "OK",
          "data": {
            "Messages": [
              {
                "Status": "success",
                "CustomID": "",
                "To": [
                  {
                    "Email": "<EMAIL>",
                    "MessageUUID": "8ee8086a-6451-4b46-bc82-4561c3feb2e5",
                    "MessageID": 576460762539150950,
                    "MessageHref": "https://api.mailjet.com/v3/REST/message/576460762539150950"
                  }
                ],
                "Cc": [],
                "Bcc": []
              }
            ]
          },
          "_in_test_mode": false
        }
        JSON;

        RpcClientServiceMock::savedResult(
            HeraldRpcMethodReferential::SERVER_NAME,
            HeraldRpcMethodReferential::SEND_EMAIL_METHOD,
            $expected_success_response
        );

        $email_dispatcher = $this->getTestedInstance();

        $result = $email_dispatcher->dispatch(json_decode($valid_request_payload, true));

        $this->assertNull($result);
    }
}
