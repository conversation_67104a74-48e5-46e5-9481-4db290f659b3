<?php

declare(strict_types=1);

namespace PHPUnit\Unit\Erp\DataWarehouse\Manager\SynchronizableTopic;

use App\Adapter\Serializer\SerializerInterface;
use App\Database\Orm\PgDataWarehouse\DataSchema\Repository\CustomerOrderPaymentStatusRepository;
use Psr\Log\NullLogger;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\AbstractSynchronizableTopicContent;
use SonVideo\Erp\DataWarehouse\Entity\SynchronizableTopic\CustomerOrderPaymentStatusUpsertTopicContent;
use SonVideo\Erp\DataWarehouse\Manager\SynchronizableTopic\CustomerOrderPaymentStatusUpsertTopicSynchronizer;
use SonVideo\Erp\Referential\DataWarehouse\SynchronizableTopicName;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class CustomerOrderPaymentStatusUpsertTopicSynchronizerTest extends KernelTestCase
{
    private CustomerOrderPaymentStatusUpsertTopicSynchronizer $synchronizer;
    private CustomerOrderPaymentStatusRepository $customer_order_payment_status_repository;
    private SerializerInterface $serializer;

    protected function setUp(): void
    {
        self::bootKernel();

        $this->serializer = self::$container->get(SerializerInterface::class);
        $this->customer_order_payment_status_repository = $this->createMock(
            CustomerOrderPaymentStatusRepository::class
        );

        $this->synchronizer = new CustomerOrderPaymentStatusUpsertTopicSynchronizer(
            $this->customer_order_payment_status_repository
        );
        $this->synchronizer->setSerializer($this->serializer);
        $this->synchronizer->setLogger(new NullLogger());
    }

    /** Teste que la méthode canHandle retourne true pour le topic correct */
    public function test_can_handle_returns_true_for_correct_topic(): void
    {
        $this->assertTrue(
            $this->synchronizer->canHandle(SynchronizableTopicName::CUSTOMER_ORDER_PAYMENT_STATUS_UPSERT)
        );
    }

    /** Teste que la méthode canHandle retourne false pour un topic incorrect */
    public function test_can_handle_returns_false_for_incorrect_topic(): void
    {
        $this->assertFalse($this->synchronizer->canHandle('some_other_topic'));
    }

    /** Teste que la méthode synchronize appelle correctement upsertOne avec les données appropriées */
    public function test_synchronize_calls_upsert_one_with_correct_data(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_PAYMENT_STATUS_UPSERT,
                'customer_order_payment_id' => 456,
                'name' => 'paid',
                'created_at' => '2023-01-01T12:00:00+00:00',
            ],
            AbstractSynchronizableTopicContent::class
        );

        $this->assertInstanceOf(CustomerOrderPaymentStatusUpsertTopicContent::class, $synchronizable_topic);

        // Configure pomm model mock to expect upsertOne call with CustomerOrderPaymentStatus instance
        $this->customer_order_payment_status_repository
            ->expects($this->once())
            ->method('upsert')
            ->with(
                $this->callback(function ($arg) use ($synchronizable_topic) {
                    self::assertEquals(
                        $arg,
                        self::$container->get(SerializerInterface::class)->normalize($synchronizable_topic)
                    );

                    return true;
                })
            );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }

    /** Teste que la méthode synchronize lance une exception en cas d'erreur */
    public function test_synchronize_throws_exception_on_error(): void
    {
        // Create a real SynchronizableTopic instance
        $synchronizable_topic = $this->serializer->denormalize(
            [
                'synchronizable_topic_id' => 123,
                'topic' => SynchronizableTopicName::CUSTOMER_ORDER_PAYMENT_STATUS_UPSERT,
                'customer_order_payment_id' => 456,
                'name' => 'paid',
                'created_at' => '2023-01-01T12:00:00+00:00',
            ],
            AbstractSynchronizableTopicContent::class
        );

        // Configure pomm model mock to throw exception
        $this->customer_order_payment_status_repository
            ->method('upsert')
            ->willThrowException(new \Exception('Database error'));

        // Expect exception to be thrown
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Failed to synchronize topic "' .
                SynchronizableTopicName::CUSTOMER_ORDER_PAYMENT_STATUS_UPSERT .
                '" with id 123'
        );

        // Execute the method under test
        $this->synchronizer->synchronize($synchronizable_topic);
    }
}
