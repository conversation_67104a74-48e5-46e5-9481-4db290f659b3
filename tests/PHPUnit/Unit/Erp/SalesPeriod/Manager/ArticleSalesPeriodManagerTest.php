<?php

namespace PHPUnit\Unit\Erp\SalesPeriod\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\InternalErrorException;
use App\Exception\InternalServerErrorException;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Utils\Database\MySqlDatabase;
use SonVideo\Erp\Account\Mysql\Repository\AccountQueryRepository;
use SonVideo\Erp\SalesPeriod\Manager\ArticleSalesPeriodManager;
use SonVideo\Erp\User\Entity\UserEntity;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class ArticleSalesPeriodManagerTest extends KernelTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'sales_period/article_sales_period.sql',
        ]);
    }

    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Gets the tested instance. */
    protected function getTestedInstance(): ArticleSalesPeriodManager
    {
        return self::$container->get(ArticleSalesPeriodManager::class);
    }

    /** Gets the serializer. */
    protected function getSerializer(): SerializerInterface
    {
        return self::$container->get(SerializerInterface::class);
    }

    /**
     * Tests the create method.
     *
     * @throws InternalServerErrorException
     * @throws NotFoundException
     * @throws ExceptionInterface
     * @throws InternalErrorException
     */
    public function test_create(): void
    {
        $article_sales_period_manager = $this->getTestedInstance();

        // Create article sale period and log event
        $payload = [
            'is_active' => true,
            'sales_period_id' => 3,
            'selling_price' => 379.0,
            'article_id' => 81123,
        ];

        $initial_selling_price = $this->findArticleSellingPrice($payload['article_id']);
        $this->assertEquals(389, $initial_selling_price);

        $article_sales = $article_sales_period_manager->create($payload, $this->getUser());
        $this->assertEquals(5, $article_sales);

        $selling_price = $this->findArticleSellingPrice($payload['article_id']);
        $this->assertEquals(379, $selling_price);

        $system_event_article_sale_period = $this->fetchLastSystemEvents(81123, 'article.create.article_sale_period');

        $this->assertIsArray($system_event_article_sale_period);
        $this->assertEquals('article.create.article_sale_period', $system_event_article_sale_period['name']);

        $event_payload_article_sale_period = json_decode(
            $system_event_article_sale_period['payload'],
            true,
            512,
            JSON_THROW_ON_ERROR
        );

        $this->assertEquals(81123, $event_payload_article_sale_period['_rel']['article']);

        $article_sales_period = $event_payload_article_sale_period['data'];
        $this->assertCount(5, $article_sales_period);

        $this->assertTrue($article_sales_period['is_active']);
        $this->assertEquals(81123, $article_sales_period['article_id']);
        $this->assertEquals(379, $article_sales_period['selling_price']);
        $this->assertEquals(3, $article_sales_period['sales_period_id']);
        $this->assertEquals(389, $article_sales_period['initial_selling_price']);

        $this->assertEquals(
            [
                'user_id' => 1,
                'lastname' => 'Admin',
                'username' => 'admin',
                'firstname' => 'Seigneur',
            ],
            $event_payload_article_sale_period['meta']['created_by']
        );

        $system_event_selling_price = $this->fetchLastSystemEvents(81123, 'article.update.selling_price');

        $this->assertIsArray($system_event_selling_price);
        $this->assertEquals('article.update.selling_price', $system_event_selling_price['name']);

        $this->assertEquals(
            [
                'selling_price' => ['old' => 389, 'new' => 379],
            ],
            json_decode($system_event_selling_price['payload'], true, 512, JSON_THROW_ON_ERROR)['data']
        );
    }

    /** Tests the update method. */
    public function test_update(): void
    {
        // Log event correctly when updated
        $payload = [
            'article_sale_id' => 4,
            'is_active' => true,
            'selling_price' => 145.5,
            'article_id' => 72216,
        ];

        $this->getTestedInstance()->update($payload, $this->getUser());
        $system_event = $this->fetchLastSystemEvents(72216, 'article.update.article_sale_period');

        $this->assertEquals('article.update.article_sale_period', $system_event['name']);
        $this->assertEquals(
            [
                'is_active' => ['old' => false, 'new' => true],
                'selling_price' => ['old' => 150, 'new' => 145.5],
                'article_sale_id' => ['old' => null, 'new' => 4],
            ],
            json_decode($system_event['payload'], true, 512, JSON_THROW_ON_ERROR)['data']
        );
    }

    /** Gets the user for testing. */
    private function getUser(): UserEntity
    {
        return self::$container->get(AccountQueryRepository::class)->getUser('admin');
    }

    /** Fetches the last system events for a given main ID and name. */
    private function fetchLastSystemEvents($main_id, $name): array
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.system_event
        WHERE main_id = :main_id
        AND name = :name
        ORDER BY event_id DESC
        LIMIT 1
        SQL;

        return $this->getPdo()->fetchOne($sql, ['main_id' => $main_id, 'name' => $name]);
    }

    /** Finds the selling price of an article by ID. */
    private function findArticleSellingPrice($article_id): int
    {
        $sql = <<<SQL
        SELECT prix_vente
        FROM backOffice.article
        WHERE id_produit = :article_id
        LIMIT 1
        SQL;

        return (int) $this->getPdo()->fetchValue($sql, ['article_id' => $article_id]);
    }

    /**
     * Gets the PDO instance.
     *
     * @throws \Exception
     */
    private function getPdo(): LegacyPdo
    {
        return self::$container->get(LegacyPdo::class);
    }
}
