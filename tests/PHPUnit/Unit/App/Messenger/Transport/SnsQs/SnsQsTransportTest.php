<?php

namespace App\Tests\PHPUnit\Unit\App\Messenger\Transport\SnsQs;

use App\Adapter\Serializer\SerializerInterface;
use App\Messenger\Routing\RoutingConfiguration;
use App\Messenger\Transport\SnsQs\SnsQsTransport;
use App\Messenger\Transport\Stamp\TransportMessageReceiptHandleStamp;
use App\Tests\Mock\Aws\Sns\SnsClientMock;
use App\Tests\Mock\Aws\Sqs\SqsClientMock;
use App\Tests\PHPUnit\TestCase;
use Aws\Result;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Stamp\RedeliveryStamp;
use Symfony\Component\Messenger\Stamp\TransportMessageIdStamp;

class SnsQsTransportTest extends TestCase
{
    private SnsClientMock $sns_client;

    private SqsClientMock $sqs_client;
    /** @var SerializerInterface&MockObject */
    private SerializerInterface $serializer;
    /** @var RoutingConfiguration&MockObject */
    private RoutingConfiguration $routing_configuration;
    private \stdClass $test_message;
    private array $options;

    protected function setUp(): void
    {
        $this->sns_client = new SnsClientMock();
        $this->sqs_client = new SqsClientMock();
        $this->serializer = $this->createMock(SerializerInterface::class);
        $this->routing_configuration = $this->createMock(RoutingConfiguration::class);

        $this->test_message = new \stdClass();
        $this->options = [
            'queue_name' => 'test_queue',
            'topic_name' => 'test_topic',
            'is_fifo' => true,
            'group_id' => 'test_group',
        ];

        SqsClientMock::setMockResponse('getQueueUrl', new Result(['QueueUrl' => 'test_queue_url']));
    }

    public function test_send(): void
    {
        $transport = new SnsQsTransport(
            $this->sns_client,
            $this->sqs_client,
            $this->serializer,
            $this->routing_configuration,
            $this->options
        );

        $envelope = new Envelope($this->test_message);
        $this->serializer->method('serialize')->willReturn('{"test":"data"}');

        SnsClientMock::setMockResponse('createTopic', new Result(['TopicArn' => 'test:arn']));
        SnsClientMock::setMockResponse('publish', new Result(['MessageId' => 'test_message_id']));

        $result = $transport->send($envelope);

        $this->assertInstanceOf(Envelope::class, $result);
        $this->assertInstanceOf(TransportMessageIdStamp::class, $result->last(TransportMessageIdStamp::class));
        /** @var TransportMessageIdStamp $stamp */
        $stamp = $result->last(TransportMessageIdStamp::class);
        $this->assertEquals('test_message_id', $stamp->getId());
    }

    public function test_send_with_fifo(): void
    {
        $options_fifo = array_merge($this->options, ['is_fifo' => true]);

        $transport = new SnsQsTransport(
            $this->sns_client,
            $this->sqs_client,
            $this->serializer,
            $this->routing_configuration,
            $options_fifo
        );

        $envelope = new Envelope($this->test_message);
        $this->serializer->method('serialize')->willReturn('{"test":"fifo_data"}');

        SnsClientMock::setMockResponse('createTopic', new Result(['TopicArn' => 'test:arn:fifo']));
        SnsClientMock::setMockResponse('publish', function ($args) {
            if (!isset($args[0]['MessageGroupId']) || !isset($args[0]['MessageDeduplicationId'])) {
                $this->fail('Les paramètres FIFO requis sont manquants');
            }

            return new Result(['MessageId' => 'fifo_message_id']);
        });

        $result = $transport->send($envelope);

        $this->assertInstanceOf(Envelope::class, $result);
        $this->assertInstanceOf(TransportMessageIdStamp::class, $result->last(TransportMessageIdStamp::class));
        /** @var TransportMessageIdStamp $stamp */
        $stamp = $result->last(TransportMessageIdStamp::class);
        $this->assertEquals('fifo_message_id', $stamp->getId());
    }

    public function test_get(): void
    {
        $message_data = [
            'Messages' => [
                [
                    'MessageId' => 'test_message_id',
                    'ReceiptHandle' => 'test_receipt_handle',
                    'Body' => json_encode(['Message' => '{"test":"data"}']),
                    'Attributes' => [
                        'MessageGroupId' => 'test_group',
                        'ApproximateReceiveCount' => '1',
                    ],
                ],
            ],
        ];

        $transport = new SnsQsTransport(
            $this->sns_client,
            $this->sqs_client,
            $this->serializer,
            $this->routing_configuration,
            $this->options
        );

        SqsClientMock::setMockResponse('receiveMessage', new Result($message_data));
        $this->routing_configuration->method('getMessageClassForGroupId')->willReturn(\stdClass::class);
        $this->serializer->method('deserialize')->willReturn($this->test_message);

        $generator = $transport->get();
        $envelope = $generator->current();

        $this->assertInstanceOf(Envelope::class, $envelope);
        $this->assertInstanceOf(RedeliveryStamp::class, $envelope->last(RedeliveryStamp::class));
        /** @var RedeliveryStamp $stamp */
        $stamp = $envelope->last(RedeliveryStamp::class);
        $this->assertEquals(1, $stamp->getRetryCount());
        $this->assertNull($generator->next());
    }

    public function test_ack(): void
    {
        $transport = new SnsQsTransport(
            $this->sns_client,
            $this->sqs_client,
            $this->serializer,
            $this->routing_configuration,
            $this->options
        );

        $envelope = new Envelope($this->test_message, [new TransportMessageReceiptHandleStamp('test_receipt_handle')]);

        SqsClientMock::setMockResponse('deleteMessage', new Result([]));

        $this->assertNull($transport->ack($envelope));
    }

    public function test_reject(): void
    {
        $options_non_fifo = array_merge($this->options, ['is_fifo' => false]);

        $transport = new SnsQsTransport(
            $this->sns_client,
            $this->sqs_client,
            $this->serializer,
            $this->routing_configuration,
            $options_non_fifo
        );

        $envelope = new Envelope($this->test_message, [
            new TransportMessageReceiptHandleStamp('test_receipt_handle'),
            new RedeliveryStamp(1),
        ]);

        SqsClientMock::setMockResponse('changeMessageVisibility', new Result([]));

        $this->assertNull($transport->reject($envelope));
    }

    public function test_reject_with_max_retries(): void
    {
        $transport = new SnsQsTransport(
            $this->sns_client,
            $this->sqs_client,
            $this->serializer,
            $this->routing_configuration,
            $this->options
        );

        $envelope = new Envelope($this->test_message, [
            new TransportMessageReceiptHandleStamp('test_receipt_handle'),
            new RedeliveryStamp(3),
        ]);

        SqsClientMock::setMockResponse('deleteMessage', new Result([]));

        $this->expectException(\Symfony\Component\Messenger\Exception\TransportException::class);
        $this->expectExceptionMessage('Message rejected after 3 retries');

        $transport->reject($envelope);
    }

    public function test_create_queue(): void
    {
        $transport = new SnsQsTransport(
            $this->sns_client,
            $this->sqs_client,
            $this->serializer,
            $this->routing_configuration,
            $this->options
        );

        SqsClientMock::setMockResponse('getQueueUrl', function () {
            static $called = false;
            if (!$called) {
                $called = true;
                throw new \Exception('Queue does not exist');
            }

            return new Result(['QueueUrl' => 'test_queue_url']);
        });
        SqsClientMock::setMockResponse('createQueue', new Result(['QueueUrl' => 'test_queue_url']));
        SqsClientMock::setMockResponse(
            'getQueueAttributes',
            new Result(['Attributes' => ['QueueArn' => 'test:queue:arn']])
        );
        SqsClientMock::setMockResponse('setQueueAttributes', new Result([]));

        SnsClientMock::setMockResponse('createTopic', new Result(['TopicArn' => 'test:topic:arn']));
        SnsClientMock::setMockResponse('subscribe', new Result([]));
        SnsClientMock::setMockResponse('publish', new Result(['MessageId' => 'test_message_id']));

        $envelope = new Envelope($this->test_message);
        $this->serializer->method('serialize')->willReturn('{"test":"data"}');

        $this->assertInstanceOf(Envelope::class, $transport->send($envelope));
    }

    public function test_reject_fifo_message(): void
    {
        $options_fifo = array_merge($this->options, ['is_fifo' => true]);

        $transport = new SnsQsTransport(
            $this->sns_client,
            $this->sqs_client,
            $this->serializer,
            $this->routing_configuration,
            $options_fifo
        );

        $envelope = new Envelope($this->test_message, [
            new TransportMessageReceiptHandleStamp('test_receipt_handle'),
            new RedeliveryStamp(1),
        ]);

        $this->serializer->method('serialize')->willReturn('{"test":"data"}');
        SqsClientMock::setMockResponse('sendMessage', new Result(['MessageId' => 'republished_message_id']));
        SqsClientMock::setMockResponse('deleteMessage', new Result([]));

        $this->assertNull($transport->reject($envelope));
    }

    public function test_get_with_message_attributes_retry_count(): void
    {
        $message_data = [
            'Messages' => [
                [
                    'MessageId' => 'test_message_id',
                    'ReceiptHandle' => 'test_receipt_handle',
                    'Body' => json_encode(['Message' => '{"test":"data"}']),
                    'MessageAttributes' => [
                        'ApproximateReceiveCount' => [
                            'StringValue' => '2',
                            'DataType' => 'Number',
                        ],
                    ],
                    'Attributes' => [
                        'MessageGroupId' => 'test_group',
                        'ApproximateReceiveCount' => '1',
                    ],
                ],
            ],
        ];

        $transport = new SnsQsTransport(
            $this->sns_client,
            $this->sqs_client,
            $this->serializer,
            $this->routing_configuration,
            $this->options
        );

        SqsClientMock::setMockResponse('receiveMessage', new Result($message_data));
        $this->routing_configuration->method('getMessageClassForGroupId')->willReturn(\stdClass::class);
        $this->serializer->method('deserialize')->willReturn($this->test_message);

        $generator = $transport->get();
        $envelope = $generator->current();

        $this->assertInstanceOf(Envelope::class, $envelope);
        /** @var RedeliveryStamp $stamp */
        $stamp = $envelope->last(RedeliveryStamp::class);
        $this->assertEquals(2, $stamp->getRetryCount());
    }
}
