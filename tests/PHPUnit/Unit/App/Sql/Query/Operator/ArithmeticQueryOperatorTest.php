<?php

namespace PHPUnit\Unit\App\Sql\Query\Operator;

use App\Sql\Query\Operator\ArithmeticQueryOperator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class ArithmeticQueryOperatorTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of ArithmeticQueryOperator. */
    protected function getTestedInstance(): ArithmeticQueryOperator
    {
        return self::$container->get(ArithmeticQueryOperator::class);
    }

    /** Tests the handle method with various inputs. */
    public function test_handle(): void
    {
        // Test with equal operator
        $result = $this->getTestedInstance()->handle('test', '_eq', 2);
        $this->assertSame('test = :test', $result);

        // Test with not equal operator
        $result = $this->getTestedInstance()->handle('test', '_neq', 3);
        $this->assertSame('test != :test', $result);

        // Test with equal operator and true value
        $result = $this->getTestedInstance()->handle('test', '_eq', true);
        $this->assertSame('test IS TRUE', $result);

        // Test with equal operator and false value
        $result = $this->getTestedInstance()->handle('test', '_eq', false);
        $this->assertSame('test IS FALSE', $result);

        // Test with greater than operator
        $result = $this->getTestedInstance()->handle('test', '_gt', 12);
        $this->assertSame('test > :test', $result);

        // Test with greater than or equal operator
        $result = $this->getTestedInstance()->handle('test', '_gte', 13);
        $this->assertSame('test >= :test', $result);

        // Test with less than operator
        $result = $this->getTestedInstance()->handle('test', '_lt', 12);
        $this->assertSame('test < :test', $result);

        // Test with less than or equal operator
        $result = $this->getTestedInstance()->handle('test', '_lte', 13);
        $this->assertSame('test <= :test', $result);

        // Test with custom parameter name
        $result = $this->getTestedInstance()->handle('test', '_lte', 13, 'custom_parameter_name');
        $this->assertSame('test <= :custom_parameter_name', $result);
    }

    /** Tests the handle method with invalid input. */
    public function test_handle_with_invalid_input(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage(
            'The Arithmetic query operator does not accept objects, arrays or booleans as value.'
        );
        $this->getTestedInstance()->handle('test', '_neq', true);
    }
}
