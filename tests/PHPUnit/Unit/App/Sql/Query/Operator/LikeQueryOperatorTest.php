<?php

namespace PHPUnit\Unit\App\Sql\Query\Operator;

use App\Sql\Query\Operator\LikeQueryOperator;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class LikeQueryOperatorTest extends KernelTestCase
{
    protected function setUp(): void
    {
        self::bootKernel();
    }

    /** Creates a test instance of LikeQueryOperator. */
    protected function getTestedInstance(): LikeQueryOperator
    {
        return self::$container->get(LikeQueryOperator::class);
    }

    /** Tests the handle method with various inputs. */
    public function test_handle(): void
    {
        // Test with LIKE operator
        $result = $this->getTestedInstance()->handle('test', '_like', 2);
        $this->assertSame('test LIKE :test', $result);

        // Test with NOT LIKE operator
        $result = $this->getTestedInstance()->handle('test', '_nlike', 3);
        $this->assertSame('test NOT LIKE :test', $result);

        // Test with custom parameter name
        $result = $this->getTestedInstance()->handle('test', '_nlike', 3, 'custom_parameter_name');
        $this->assertSame('test NOT LIKE :custom_parameter_name', $result);

        // Test with case insensitive LIKE operator
        $result = $this->getTestedInstance()->handle('test', '_ilike', 2);
        $this->assertSame('test COLLATE latin1_general_ci LIKE :test', $result);

        // Test with case insensitive NOT LIKE operator
        $result = $this->getTestedInstance()->handle('test', '_nilike', 3);
        $this->assertSame('test COLLATE latin1_general_ci NOT LIKE :test', $result);
    }

    /** Tests the handle method with invalid input. */
    public function test_handle_with_invalid_input(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('The LIKE query operator only accepts string or integer.');
        $this->getTestedInstance()->handle('test', '_neq', true);
    }
}
