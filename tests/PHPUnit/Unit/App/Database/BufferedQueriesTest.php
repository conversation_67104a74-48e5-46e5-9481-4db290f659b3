<?php

namespace PHPUnit\Unit\App\Database;

use App\Database\BufferedQueries as TestedTrait;
use App\Database\ConstQueriesInterface;
use PommProject\Foundation\Converter\ConverterHolder;
use PommProject\Foundation\Converter\ConverterPooler;
use PommProject\Foundation\Converter\PgNumber;
use PommProject\Foundation\Converter\PgString;
use PommProject\Foundation\Session\Connection;
use PommProject\ModelManager\Model\FlexibleEntity;
use PommProject\ModelManager\Model\Model;
use PommProject\ModelManager\Model\RowStructure;
use PommProject\ModelManager\Session;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class BufferedQueriesTest extends KernelTestCase
{
    /** Creates a model instance for testing. */
    private function getModel(): Model
    {
        $entity = new class() extends FlexibleEntity {};

        $structure = new class() extends RowStructure {
            public function __construct()
            {
                $this->setRelation('schema.foo_table')
                    ->setPrimaryKey(['foo_id'])
                    ->addField('foo_id', 'int4')
                    ->addField('foo', 'string')
                    ->addField('bar', 'int4');
            }
        };

        $model = new class($structure, $entity) extends Model {
            use TestedTrait;

            public function __construct(RowStructure $structure, FlexibleEntity $entity)
            {
                $this->structure = $structure;
                $this->flexible_entity_class = get_class($entity);
            }

            public function getBuffer()
            {
                return $this->buffer;
            }

            public function getLastQuery()
            {
                return $this->getSession()->getConnection()->lastQuery;
            }
        };

        $connexion = new class() extends Connection {
            public $lastQuery;

            public function __construct()
            {
            }

            protected function getHandler()
            {
                return null;
            }

            public function escapeIdentifier($string)
            {
                return $string;
            }

            public function sendQueryWithParameters($query, array $parameters = []): void
            {
                $this->lastQuery = [
                    'sql' => $query,
                    'params' => $parameters,
                ];
            }
        };

        $converter_holder = new ConverterHolder();
        $converter_holder
            ->registerConverter('Number', new PgNumber(), ['int4'], false)
            ->registerConverter('String', new PgString(), ['string'], false);

        $session = new Session($connexion);
        $session->registerClientPooler(new ConverterPooler($converter_holder));

        $model->initialize($session);

        return $model;
    }

    /** Tests inserting multiple rows. */
    public function test_insert_multiples_row(): void
    {
        $to_insert = [
            [
                'foo' => 'baz',
                'bar' => '1',
            ],
            [
                'foo' => 'quz',
                'bar' => '45',
            ],
        ];

        $expected = [];

        $model = $this->getModel();

        foreach ($to_insert as $row) {
            $model->bufferize($row);

            ksort($row);
            $expected[] = $row;

            $this->assertEquals($expected, $model->getBuffer());
        }

        $expected_params = [0 => null];
        array_walk_recursive($expected, function ($a) use (&$expected_params): void {
            $expected_params[] = $a;
        });
        unset($expected_params[0]);

        $model->flush(ConstQueriesInterface::INSERT);

        $this->assertEquals(
            [
                'sql' => 'INSERT INTO schema.foo_table (bar, foo) VALUES ($1, $2), ($3, $4)',
                'params' => $expected_params,
            ],
            $model->getLastQuery()
        );
    }

    /** Tests upserting multiple rows. */
    public function test_upsert_multiples_row(): void
    {
        $to_insert = [
            [
                'foo' => 'baz',
                'bar' => '1',
            ],
            [
                'foo' => 'quz',
                'bar' => '45',
            ],
        ];

        $expected = [];

        $model = $this->getModel();

        foreach ($to_insert as $row) {
            $model->bufferize($row);

            ksort($row);
            $expected[] = $row;

            $this->assertEquals($expected, $model->getBuffer());
        }

        $expected_params = [0 => null];
        array_walk_recursive($expected, function ($a) use (&$expected_params): void {
            $expected_params[] = $a;
        });
        unset($expected_params[0]);

        $model->flush(ConstQueriesInterface::UPSERT);

        $this->assertEquals(
            [
                'sql' => 'INSERT INTO schema.foo_table (bar, foo) VALUES ($1, $2), ($3, $4) ON CONFLICT (foo_id) DO UPDATE SET bar = EXCLUDED.bar, foo = EXCLUDED.foo',
                'params' => $expected_params,
            ],
            $model->getLastQuery()
        );
    }

    /** Tests upserting multiple rows with different structures. */
    public function test_upsert_multiples_with_different_row(): void
    {
        $to_insert = [
            [
                'foo' => 'baz',
                'bar' => '1',
            ],
            [
                'foo_id' => '1',
                'bar' => '45',
            ],
        ];

        $model = $this->getModel();

        $model->bufferize($to_insert[0]);

        $this->expectException(\PommProject\Foundation\Exception\PommException::class);
        $model->bufferize($to_insert[1]);
    }
}
