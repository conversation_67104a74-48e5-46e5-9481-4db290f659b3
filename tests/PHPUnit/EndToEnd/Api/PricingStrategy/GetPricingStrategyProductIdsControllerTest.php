<?php

namespace PHPUnit\EndToEnd\Api\PricingStrategy;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class GetPricingStrategyProductIdsControllerTest extends WebTestCase
{
    private KernelBrowser $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'pricing_strategy/pricing_strategy.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/pricing-strategy/5/product-ids',
            [],
            [],
            ['HTTP_CONTENT_TYPE' => 'application/json']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/pricing-strategy/5/product-ids',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer unknown-token',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_successful_response(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/pricing-strategy/5/product-ids',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals('success', $response_data['status']);
        self::assertIsArray($response_data['data']['product_ids']);

        // Vérifier que les IDs des produits attendus sont présents dans la réponse
        // Ces IDs doivent correspondre aux produits associés à la stratégie 5 dans les fixtures
        $expected_product_ids = [81078, 81123]; // IDs des produits ARCAMRBLINKNR et LBCLD25BP
        foreach ($expected_product_ids as $product_id) {
            self::assertContains($product_id, $response_data['data']['product_ids']);
        }

        // Vérifier que le nombre de produits retournés est correct
        self::assertCount(count($expected_product_ids), $response_data['data']['product_ids']);
    }
}
