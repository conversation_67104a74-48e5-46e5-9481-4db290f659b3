<?php

namespace PHPUnit\EndToEnd\Api\PricingStrategy;

use App\Tests\Utils\ArrayHelper;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class GetPricingStrategyControllerTest extends WebTestCase
{
    private KernelBrowser $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'pricing_strategy/pricing_strategy.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/pricing-strategy/5',
            [],
            [],
            ['HTTP_CONTENT_TYPE' => 'application/json']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/pricing-strategy/5',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer unknown-token',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_existant_strategy(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/pricing-strategy/666',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }

    public function test_successful_response(): void
    {
        $this->client->request(
            'GET',
            '/api/v1/pricing-strategy/5',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals('success', $response_data['status']);

        $expected_pricing_strategy = [
            'pricing_strategy_id' => 5,
            'name' => 'strat de chokbar',
            'starts_at' => '2024-06-01 10:00:00',
            'ends_at' => '2032-06-02 10:00:00',
            'activation_status' => 'CREATED',
            'weekdays_increment_amount' => 0,
            'weekdays_min_margin_rate' => 5,
            'weekend_increment_amount' => 0,
            'updated_at' => '2024-06-01 08:35:00',
            'sales_channels' => [
                [
                    'label' => 'amazon.fr',
                    'sales_channel_id' => 4,
                ],
                [
                    'label' => 'son-video.com',
                    'sales_channel_id' => 1,
                ],
                [
                    'label' => 'son-video.pro',
                    'sales_channel_id' => 2,
                ],
            ],
            'competitors' => [
                [
                    'competitor_code' => 'DARTY',
                ],
                [
                    'competitor_code' => 'BOULANGER',
                ],
                [
                    'competitor_code' => 'HOMECINESOLUTION',
                ],
            ],
            'products' => [
                [
                    'pricing_strategy_id' => 5,
                    'sku' => 'ARCAMRBLINKNR',
                    'article_id' => 81078,
                    'image' => '/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
                    'article_name' => 'Arcam rBlink',
                    'selling_price' => 249,
                    'stock' => 3,
                    'category' => 'Distributeurs et transmetteurs',
                    'margin' => 0.58,
                    'margin_rate' => 0.366,
                    'margin_tax_excluded' => 75.8,
                    'last_scrapping_date' => '2024-06-01 08:35:00',
                    'purchase_price' => 131.58,
                    'sorecop' => 0,
                    'ecotax' => 0.15,
                    'promo_budget_amount' => 0,
                    'unconditional_discount' => 0,
                    'weighted_cost_tax_excluded' => 131.58,
                    'status' => 'oui',
                    'delay' => 0,
                    'pvgc' => 249,
                    'new_prices' => [],
                    'is_margin_rate_to_low' => false,
                    'lowest_competitor' => null,
                ],
                [
                    'pricing_strategy_id' => 5,
                    'sku' => 'LBCLD25BP',
                    'article_id' => 81123,
                    'image' => '/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg',
                    'article_name' => 'La Boite Concept Pieds Noir laqué pour station HiFi LD120 / LD130',
                    'selling_price' => 389,
                    'stock' => 2,
                    'category' => 'Meubles et supports',
                    'margin' => 0.84,
                    'margin_rate' => 0.456,
                    'margin_tax_excluded' => 147.82,
                    'last_scrapping_date' => null,
                    'purchase_price' => 195,
                    'sorecop' => 0,
                    'ecotax' => 0,
                    'promo_budget_amount' => 0,
                    'unconditional_discount' => 0,
                    'weighted_cost_tax_excluded' => 176.35,
                    'status' => 'oui',
                    'delay' => 0,
                    'pvgc' => 389,
                    'new_prices' => [],
                    'is_margin_rate_to_low' => false,
                    'lowest_competitor' => null,
                ],
            ],
            'count_products' => 2,
            'weekend_min_margin_rate' => 5,
            'site' => null,
            'type' => 'competitor_codes',
        ];

        self::assertEquals(
            $expected_pricing_strategy,
            ArrayHelper::removeKeysFrom($response_data['data']['pricing_strategy'], ['created_at'])
        );
    }
}
