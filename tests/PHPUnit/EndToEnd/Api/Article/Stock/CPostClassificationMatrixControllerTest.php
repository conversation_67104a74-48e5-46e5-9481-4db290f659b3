<?php

namespace App\Tests\PHPUnit\EndToEnd\Api\Article\Stock;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class CPostClassificationMatrixControllerTest extends WebTestCase
{
    private KernelBrowser $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'classification_matrix/classification_matrix.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function setUp(): void
    {
        parent::setUp();
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/article/classification-matrix',
            [],
            [],
            ['HTTP_CONTENT_TYPE' => 'application/json']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/article/classification-matrix',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer unknown-token',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_successful_response(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/article/classification-matrix',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
                'HTTP_CONTENT_TYPE' => 'application/json',
            ],
            '{"where": {}}'
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals('success', $response_data['status']);
        self::assertEquals(9, $response_data['data']['_pager']['total']);
        self::assertCount(9, $response_data['data']['classification_matrix']);

        $classification_matrix = [
            'stock_classification_matrix_id' => 1,
            'name' => 'AX',
            'revenue_class' => 'A',
            'variability_class' => 'X',
            'service_rate' => 97
        ];

        self::assertEquals($classification_matrix, $response_data['data']['classification_matrix'][0]);
    }

    public function test_filtering_by_service_rate(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/article/classification-matrix',
            [],
            [],
            [
                'CONTENT_TYPE' => 'application/json',
                'HTTP_AUTHORIZATION' => 'Bearer user2-token-without-permission',
            ],
            '{"where":{"_and":[{"service_rate":{"_eq":80}}]}}'
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        self::assertEquals('success', $response_data['status']);
        self::assertCount(1, $response_data['data']['classification_matrix']);

        $classification_matrix = [
            'stock_classification_matrix_id' => 8,
            'name' => 'CY',
            'revenue_class' => 'C',
            'variability_class' => 'Y',
            'service_rate' => 80
        ];

        self::assertEquals(
            $classification_matrix,
            $response_data['data']['classification_matrix'][0]
        );
    }
}
