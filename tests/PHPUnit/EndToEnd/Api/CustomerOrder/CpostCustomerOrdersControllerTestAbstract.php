<?php

namespace PHPUnit\EndToEnd\Api\CustomerOrder;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractCPostBasicWebTestCase;
use Symfony\Component\HttpFoundation\Response;

class CpostCustomerOrdersControllerTestAbstract extends AbstractCPostBasicWebTestCase
{
    protected const URL_ENDPOINT = '/api/v1/customer-orders';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'sales_channel/sales_channels.sql', 'customer_order.sql']);
    }

    public function test_get_list_of_customer_orders_with_a_valid_authorization(): void
    {
        $this->request->post(static::URL_ENDPOINT);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        // Check response content
        $this->assertEquals('success', $response_data['status']);
        $this->assertEquals(3, $response_data['data']['_pager']['total']);
    }

    public function test_get_list_of_customer_orders_with_limit_and_order_by(): void
    {
        $json = <<<JSON
        {
            "limit": 3,
            "order_by": "customer_order_id"
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $response_data = json_decode($this->client->getResponse()->getContent(), true);
        $customer_orders = $response_data['data']['customer_orders'];
        $first_order = $customer_orders[0];
        $third_order = $customer_orders[2];

        // Check response content
        $this->assertEquals('success', $response_data['status']);
        $this->assertEquals(3, $response_data['data']['_pager']['total']);
        $this->assertEquals(3, $response_data['data']['_pager']['limit']);

        // Check customer orders
        $this->assertCount(3, $customer_orders);
        $this->assertCount(23, $first_order);
        $this->assertEquals(1, $first_order['customer_order_id']);
        $this->assertArrayHasKey('created_at', $first_order);
        $this->assertEquals('En attente', $first_order['statuses'][0]);
        $this->assertEquals(249, $first_order['amount_all_tax_included']);
        $this->assertEquals('son-video.com', $first_order['origin']);
        $this->assertStringContainsString('1', $first_order['original_customer_order_id']);
        $this->assertEquals('Nantes', $first_order['store_label']);
        $this->assertEquals('Colissimo', $first_order['carrier_name']);
        $this->assertNull($first_order['payment_fraud_detection']);
        $this->assertNull($first_order['source']);
        $this->assertNull($first_order['source_group']);

        // Check payments
        $this->assertCount(0, $first_order['payments']);

        // Check amazon origin
        $this->assertEquals(3, $third_order['customer_order_id']);
        $this->assertEquals('amazon.de', $third_order['origin']);
        $this->assertStringContainsString('3', $third_order['original_customer_order_id']);
    }

    public function test_get_list_of_customer_orders_filtered_by_id_and_check_response(): void
    {
        $json = <<<JSON
        {
            "where": {
                "customer_id": {
                    "_eq": 1
                }
            }
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        // Check response content
        $this->assertEquals('success', $response_data['status']);
        $this->assertEquals(3, $response_data['data']['_pager']['total']);
    }

    public function test_get_list_of_customer_orders_filtered_by_id_and_additional_dependencies(): void
    {
        $json = <<<JSON
        {
            "where": {
                "customer_order_id": {
                    "_eq": 2
                }
            },
            "included_dependencies": [
                "articles",
                "delivery_notes",
                "payments",
                "invoices",
                "last_internal_comment",
                "last_customer_comment"
            ]
        }
        JSON;

        $this->request->post(static::URL_ENDPOINT, $json);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());

        $response_data = json_decode($this->client->getResponse()->getContent(), true);

        // Check response content
        $this->assertEquals('success', $response_data['status']);
        $this->assertEquals(1, $response_data['data']['_pager']['total']);

        // Store the customer order in a variable for reuse
        $customer_order = $response_data['data']['customer_orders'][0];

        $this->assertEquals(2, $customer_order['customer_order_id']);
        $this->assertArrayHasKey('created_at', $customer_order);
        $this->assertEquals('En attente', $customer_order['statuses'][0]);
        $this->assertEquals(249, $customer_order['amount_all_tax_included']);
        $this->assertEquals('son-video.com', $customer_order['origin']);
        $this->assertStringContainsString('2', $customer_order['original_customer_order_id']);
        $this->assertNull($customer_order['store_label']);
        $this->assertEquals('Emport dépôt', $customer_order['carrier_name']);
        $this->assertNull($customer_order['payment_fraud_detection']);
        $this->assertNull($customer_order['source']);
        $this->assertNull($customer_order['source_group']);

        // Check payments
        $this->assertCount(2, $customer_order['payments']);

        // Check first payment
        $first_payment = $customer_order['payments'][0];
        $this->assertStringContainsString('2', $first_payment['customer_order_id']);
        $this->assertStringContainsString('11', $first_payment['payment_id']);
        $this->assertStringContainsString('CTPE', $first_payment['payment_mean']);
        $this->assertStringContainsString('CTPE', $first_payment['payment_name']);
        $this->assertStringContainsString('249', $first_payment['amount']);
        $this->assertStringContainsString('2018-09-11 14:36:23', $first_payment['created_at']);
        $this->assertStringContainsString('2019-10-08 19:15:40', $first_payment['accepted_at']);
        $this->assertNull($first_payment['auto_status']);
        $this->assertNull($first_payment['auto_status_detail']);
        $this->assertNull($first_payment['auto_warranty']);
        $this->assertStringContainsString('canceled', $first_payment['status']);

        // Check second payment
        $second_payment = $customer_order['payments'][1];
        $this->assertStringContainsString('2', $second_payment['customer_order_id']);
        $this->assertStringContainsString('11', $second_payment['payment_id']);
        $this->assertStringContainsString('CTPE', $second_payment['payment_mean']);
        $this->assertStringContainsString('CTPE', $second_payment['payment_name']);
        $this->assertStringContainsString('249', $second_payment['amount']);
        $this->assertStringContainsString('2018-09-11 14:36:23', $second_payment['created_at']);
        $this->assertStringContainsString('2018-09-11 14:36:23', $second_payment['accepted_at']);
        $this->assertNull($second_payment['auto_status']);
        $this->assertNull($second_payment['auto_status_detail']);
        $this->assertNull($second_payment['auto_warranty']);
        $this->assertStringContainsString('accepted', $second_payment['status']);

        // Check articles
        $articles = $customer_order['articles'];
        $this->assertCount(1, $articles);
        $article = $articles[0];
        $this->assertStringContainsString('2', $article['customer_order_id']);
        $this->assertStringContainsString('81078', $article['article_id']);
        $this->assertStringContainsString('ARCAMRBLINKNR', $article['sku']);
        $this->assertStringContainsString('1', $article['quantity']);
        $this->assertStringContainsString('Arcam rBlink', $article['short_description']);
        $this->assertStringContainsString('3', $article['available_quantity']);

        // Check delivery notes
        $delivery_notes = $customer_order['delivery_notes'];
        $this->assertCount(1, $delivery_notes);
        $delivery_note = $delivery_notes[0];
        $this->assertStringContainsString('2', $delivery_note['customer_order_id']);
        $this->assertStringContainsString('123', $delivery_note['delivery_note_id']);
        $this->assertStringContainsString('5', $delivery_note['carrier_id']);
        $this->assertStringContainsString('Emport dépôt', $delivery_note['carrier_name']);
        $this->assertStringContainsString('31', $delivery_note['shipment_method_id']);
        $this->assertStringContainsString('Emport Dépôt', $delivery_note['shipment_method_name']);
        $this->assertStringContainsString('2019-08-29 20:15:09', $delivery_note['validated_at']);
        $this->assertTrue($delivery_note['is_prepared']);
        $this->assertNull($delivery_note['parcel_tracking_number']);
        $this->assertNull($delivery_note['store_pickup_started_at']);
        $this->assertStringContainsString('CREATED', $delivery_note['workflow_status']);

        // Check invoices
        $invoices = $customer_order['invoices'];
        $this->assertCount(1, $invoices);
        $invoice = $invoices[0];
        $this->assertStringContainsString('2', $invoice['customer_order_id']);
        $this->assertStringContainsString('456', $invoice['invoice_id']);
        $this->assertStringContainsString('facture', $invoice['invoice_type']);
        $this->assertStringContainsString('123', $invoice['delivery_note_id']);
        $this->assertStringContainsString('2018-04-18 15:09:03', $invoice['created_at']);

        // Check last internal comment
        $internal_comments = $customer_order['last_internal_comment'];
        $this->assertCount(1, $internal_comments);
        $internal_comment = $internal_comments[0];
        $this->assertStringContainsString('2', $internal_comment['customer_order_id']);
        $this->assertStringContainsString('Seigneur ADMIN', $internal_comment['created_by']);
        $this->assertStringContainsString('2021-04-01 19:54:37', $internal_comment['created_at']);
        $this->assertStringContainsString('ASDF', $internal_comment['message']);

        // Check last customer comment
        $customer_comments = $customer_order['last_customer_comment'];
        $this->assertCount(1, $customer_comments);
        $customer_comment = $customer_comments[0];
        $this->assertStringContainsString('2', $customer_comment['customer_order_id']);
        $this->assertStringContainsString('2021-04-03 00:35:38', $customer_comment['created_at']);
        $this->assertStringContainsString('BAR', $customer_comment['message']);
    }
}
