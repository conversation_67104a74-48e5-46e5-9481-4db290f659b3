<?php

namespace PHPUnit\EndToEnd\Api\CustomerOrder;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\File\FilesystemHelper;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class PostCustomerOrderAffiliationConsolidationControllerTest extends WebTestCase
{
    private $client;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql']);
        MySqlDatabase::loadSpecificFixtures(['sales_channel/sales_channels.sql']);
        MySqlDatabase::loadSpecificFixtures(['customer_order.sql']);
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    public function test_without_authorization(): void
    {
        $this->client->request('POST', '/api/v1/customer-order/affiliation-consolidation', [], [], []);

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_with_non_valid_authorization(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/customer-order/affiliation-consolidation',
            [],
            [],
            ['HTTP_AUTHORIZATION' => 'Bearer unknown-token']
        );

        self::assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    public function test_check_response_when_post_unknown_file_path(): void
    {
        $this->client->request(
            'POST',
            '/api/v1/customer-order/affiliation-consolidation',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ],
            <<<JSON
            {
                "file_path": "unknown"
            }
            JSON
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals(
            [
                'status' => 'error',
                'message' => 'File does not exist.',
                'code' => 400,
                'data' => [],
            ],
            $response_content
        );
    }

    public function test_check_response_when_post_a_invalid_file_type(): void
    {
        self::$container->get(FilesystemHelper::class)->moveFile('default', '080920221425-test.jpg', 'test.jpg');

        $this->client->request(
            'POST',
            '/api/v1/customer-order/affiliation-consolidation',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ],
            <<<JSON
            {
                "file_path": "080920221425-test.jpg"
            }
            JSON
        );

        self::assertResponseStatusCodeSame(Response::HTTP_BAD_REQUEST);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals(
            [
                'status' => 'error',
                'message' => 'File must be a CSV.',
                'code' => 400,
                'data' => [],
            ],
            $response_content
        );
    }

    public function test_check_response_when_post_a_valid_file_path(): void
    {
        self::$container
            ->get(FilesystemHelper::class)
            ->moveFile('default', '080920221425-affiliation.csv', 'affiliation.csv');

        $this->client->request(
            'POST',
            '/api/v1/customer-order/affiliation-consolidation',
            [],
            [],
            [
                'HTTP_AUTHORIZATION' => 'Bearer user1-token-admin',
                'CONTENT_TYPE' => 'application/json',
            ],
            <<<JSON
            {
                "file_path": "080920221425-affiliation.csv"
            }
            JSON
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        $response_content = json_decode($this->client->getResponse()->getContent(), true);
        self::assertEquals(
            [
                'status' => 'success',
                'data' => "\"Commande ID\",Statut,\"Détail statut\",Origine,Clef\n1,\"En cours de traitement\",\"lvr_attente,facturation\",son-video.com,\n2,\"En cours de traitement\",\"lvr_attente,facturation,facture\",backoffice.sonvideopro.com,\n3,\"En cours de traitement\",,amazon.de,\n",
            ],
            $response_content
        );
    }
}
