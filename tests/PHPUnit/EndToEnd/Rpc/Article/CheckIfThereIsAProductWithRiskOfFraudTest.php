<?php

namespace PHPUnit\EndToEnd\Rpc\Article;

use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use App\Tests\Utils\PHPUnit\EndToEnd\AbstractRpcEndpointTest;
use Symfony\Component\HttpFoundation\Response;

class CheckIfThereIsAProductWithRiskOfFraudTest extends AbstractRpcEndpointTest
{
    protected const RPC_METHOD = 'article:check_if_there_is_a_product_with_risk_of_fraud';

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();

        // Load fixtures based on the @clear-database tag in the scenario
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures(['users.sql', 'sales_channel/sales_channels.sql', 'customer_order.sql']);

        PgDatabase::reloadFixtures();
    }

    public function test_rpc_response(): void
    {
        $this->sendRpcRequest(self::RPC_METHOD, [['81123', '128416', 'BWCCM74']]);

        $this->assertEquals(Response::HTTP_OK, $this->client->getResponse()->getStatusCode());
        self::assertResponseHeaderSame('Content-Type', 'application/json');

        $response_data = $this->getResponseData();
        $this->assertEquals($response_data['result'], ['81123' => false, '128416' => false, 'BWCCM74' => false]);
    }
}
