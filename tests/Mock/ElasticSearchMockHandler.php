<?php

namespace App\Tests\Mock;

use GuzzleHttp\Ring\Client\MockHandler;

class ElasticSearchMockHandler extends MockHandler
{
    public function __construct()
    {
        parent::__construct([
            'status' => 200,
            'transfer_stats' => [
                'total_time' => 100,
            ],
            'body' => fopen(__DIR__ . '/body.json', 'r+'),
            'effective_url' => 'localhost',
        ]);
    }
}
