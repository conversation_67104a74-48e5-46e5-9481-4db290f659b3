<?php
/*
 * This file is part of erp-server package.
 *
 * (c) 2020 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock\Erp\Client;

use App\Contract\ErpV1ApiClientInterface;

/**
 * Class ErpV1ApiClient.
 */
class ErpV1ApiClient implements ErpV1ApiClientInterface
{
    /** {@inheritDoc} */
    public function callWithToken($method, string $token, array $params, ?callable $check_http_code = null): \stdClass
    {
        $result = new \stdClass();
        $result->success = ['ok'];

        return $result;
    }
}
