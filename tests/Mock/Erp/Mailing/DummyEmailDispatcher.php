<?php
/*
 * This file is part of ERP Server package.
 *
 * (c) 2021 Son-Video Distribution
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace App\Tests\Mock\Erp\Mailing;

use SonVideo\Erp\Mailing\Contract\EmailDispatcherInterface;
use SonVideo\Erp\System\ValueObject\LoggableSystemEvent;

class DummyEmailDispatcher implements EmailDispatcherInterface
{
    private const BUSINESS_KEY = 'dummy';

    /** {@inheritDoc} */
    public function canHandle(string $key): bool
    {
        return static::BUSINESS_KEY === $key;
    }

    /** {@inheritDoc} */
    public function dispatch(array $data): ?LoggableSystemEvent
    {
        return null;
    }
}
