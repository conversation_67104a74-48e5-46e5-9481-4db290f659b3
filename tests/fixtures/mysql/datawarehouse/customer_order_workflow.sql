


INSERT INTO backOffice.paiement (id_paiement, moyen, actif, operation, avoir, paiement, description, surcout, attente_paiement, creation_distante, autorisation_distante, annulation_distante, demande_remise_distante, remboursement_distant, remise_directe, declaration_impaye, interrogation_etat_distante, statut_source, garantie_source, justificatif_creation, justificatif_creation_source, justificatif_creation_type, justificatif_acceptation, justificatif_acceptation_source, justificatif_acceptation_type, justificatif_acceptation_motif, justificatif_remise, justificatif_remise_source, justificatif_remise_type, justificatif_remise_motif, bon_remise, bon_remise_motif, compte_bancaire, journal, emport_depot)
VALUES
  (11, 'CTPE', 'Y', 'paiement', 'N', 'CTPE', 'Carte terminal de paiement', 0.00, 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'manuel', 'manuel', 'N', 'manuel', 'ID Trans. Client', 'Y', 'manuel', 'No transaction', '^\\d{1,64}$', 'Y', 'manuel', 'No remise', '^\\d{1,64}$', 'N', '', '512000', 'BQ', 'Y')
;

INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste, cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport, transport_offre_speciale)
VALUES
(67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1)
;

 INSERT INTO backOffice.CTG_TXN_domaine (id, domaine, meta_description) 
 VALUES 
    (10, 'Domaine 1', '')
;

INSERT INTO backOffice.CTG_TXN_categorie (id_categorie, categorie, dft_domaine_id)
VALUES
    (10, 'Catégorie 1.1', 10)
;

INSERT INTO backOffice.warranty_type (type, label, description)
VALUES
    ('NON', 'Aucune', '-')
;

INSERT INTO backOffice.CTG_TXN_souscategorie (id, souscategorie, dft_categorie_id)
VALUES
    (10, 'Soucategorie 1.1.1', 10)
;

INSERT INTO backOffice.produit (id_produit, reference, id_souscategorie, V_id_categorie, V_id_domaine)
VALUES
    (100, 'PROD100', 10, 10, 10)
;

INSERT INTO fournisseur(id_fournisseur, fournisseur, marque_disponible , marque_en_vente, commentaire)
VALUES 
    (10, 'Fournisseur 1', '', '', '')
;

INSERT INTO marque(id_marque, marque, histoire, url_source_doc, url_source_image, specialite, produit_de_reference, public, type_distribution, avis, a_savoir, keyword, garanti, meta_description)
VALUES
    (100, 'Marque 1', '', '', '', '', '', '', '', '', '', '', '', '')
;

INSERT INTO backOffice.couleur (id_couleur, code) 
    VALUES (1, 'OSEF')
;

INSERT INTO article (id_produit, modele, id_marque, id_fournisseur, description_panier, description_courte)
VALUES
     (100, 'Produit dispo', 100, 10, '', '')
;

INSERT INTO backOffice.prospect (id_prospect, cnt_email)
VALUES
  (1, '<EMAIL>')
;

INSERT INTO backOffice.commande (id_commande, no_commande_origine, date_creation, flux, V_statut_traitement, V_montant_ttc, V_trcns_montant_accepte, id_prospect, cnt_fct_email, commentaire_facture, sales_channel_id)
VALUES

    (1, '-100', '2023-08-01', 'traitement', 'anomalie', 1000, 900, 1, '<EMAIL>', '', 1),
    (2, '0', '2023-08-01', 'traitement', '', 1000, 0, 1, '<EMAIL>', '', 1),
    (200, '200', '2023-08-01', 'traitement', '', 1000, 1000, 1, '<EMAIL>', '', 1),    
    (600, '600', '2023-08-01', 'cloture', '', 1000, 1000, 1, '<EMAIL>', '', 1)

;

INSERT INTO backOffice.produit_commande (id, id_commande, id_bon_livraison, id_produit, quantite, prix_achat, prix_vente, description)
VALUES
    (1, 1, NULL, 100, 1, -100, -100, ''), -- erreur
    (2, 2, NULL, 100, 1, 1, 10, ''), -- ouverte
    (200, 200, NULL, 100, 1, 100, 1000, ''), -- preparable
    (600, 600, NULL, 100, 1, 100, 1000, '') -- fermée
;

INSERT INTO backOffice.paiement_commande (id_commande, id_paiement, acceptation_montant, acceptation_date) 
VALUES 
  (1, 11, 0, NOW()),
  (2, 11, 5, NOW()),
  (200, 11, 1000, NOW()),
  (600, 11, 1000, NOW())
;
