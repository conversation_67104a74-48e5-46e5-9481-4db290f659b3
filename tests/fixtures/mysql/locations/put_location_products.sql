INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste, cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport, transport_offre_speciale)
VALUES
    (67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1)
;
INSERT INTO backOffice.CTG_TXN_domaine (id, trigger_actif, trigger_actif2, domaine, espace, domaine_btq, rang, url_page, menu, comprendre_pour_choisir, meta_description, presentation)
VALUES
    (6, 1, 0, 'Accessoires', 0, 'Accessoires', 8, '/Rayons/Accessoires/index.html', 1, 1, '', ''),
    (13, 1, 0, 'Indéfini', 0, 'Indéfini', 15, null, 0, 1, '', null)
;
INSERT INTO backOffice.CTG_TXN_categorie (id_categorie, trigger_actif, trigger_actif2, neteven_couleur, neteven_poids, neteven_televiseur, neteven_type1, categorie, url_categorie, dft_domaine_id, garantie_5, videoprojecteur, diagonale, id_bbac_categorie, port_facture, port_facture_tva, export, section, url_section, typologie, critere_section_sommaire, export_amazon, keyword_amazon, categorie_amazon, hors_gabarit, hors_gabarit_poids_seuil, deb_nomenclature, url_page, pixmania_segment_id, mesure_diagonale, mesure_longueur, code_type_produit_presto, code_douanier)
 VALUES 
  (70, 1, 0, 1, 0, 0, 1, 'Services', '', 6, 'non', 'non', 'non', 1, 0.00, 0.200, 'non', '', '', '', 'marque', '0', '', '', 'N', null, '', null, null, 0, 0, '610', '0000000000'),
  (96, 1, 0, 1, 0, 0, 1, 'Distributeurs et transmetteurs', '', 6, 'non', 'non', 'non', 11, 9.90, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'N', null, '85229080', '/Rayons/HomeCinema/Telecommandes/RelaisCGV.html', 3050, 0, 0, '610', '85229080'),
  (144, 1, 0, 1, 1, 0, 1, 'Santé', '', 6, 'non', 'non', 'non', 1, 0.00, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'N', null, null, '/sante-connectee.html', null, 0, 0, '610', '0000000000'),
  (1, 1, 0, 1, 0, 0, 1, 'Indéfinie', '', 13, 'non', 'non', 'non', 1, 9.90, 0.200, 'non', '', '', '', 'marque', '0', '', '', 'N', null, '', null, null, 0, 0, '610', '0000000000')
;

INSERT INTO backOffice.warranty_type (type, label, description)
VALUES
    ('NON', 'Aucune', 'A utiliser pour forcer le fait de ne pas avoir de garantie et ne pas utiliser la règle parente'),
    ('SON', 'Son', 'Garantie pour les produits appartenant à la famille du son'),
    ('VIDEO', 'Vidéo', 'Garantie pour les produits de diffusion vidéo')
;
INSERT INTO backOffice.CTG_TXN_souscategorie (id, trigger_actif, trigger_actif2, souscategorie, url_page, port_facture, port_facture_tva, dft_categorie_id, reevoo, rue_du_commerce, url_nav, id_domaine, id_domaine_2, id_categorie_ebay, id_categorie_boutique_ebay, pixmania_segment_id, hors_gabarit, illustration, redoute_nomenclature_node)
VALUES
    (258, 1, 1, 'Récepteurs Bluetooth', '/systeme-audio-sans-fil/recepteur-bluetooth.html', 3.99, 0.200, 96, 0, null, null, null, null, '79323', '1', 9630, 0, 'http://www.son-video.com/images/dynamic/Distributeurs_et_transmetteurs/articles/Focal/FOCALAPTXUWREC/Focal-Universal-Wireless-APTX-Receiver_P_140.jpg', null),
    (56, 1, 1, 'Câbles d''enceintes', '/Rayons/Cables/EspaceCable/CablesA_Enceintes.html', 5.99, 0.200, 144, 1, 'MC-4702', '/Parts/Nav/NavR_Cable_Enceintes.html', 6, 6, '137917', '1', 118, 0, 'http://www.son-video.com/images/dynamic/Cables_audio/composes/NORSTCL40010M/NorStone-CL400-Classic-2-x-4-mm2-10-m-_P_180.jpg', ''),
    (296, 1, 0, 'Catalogues et parutions', null, 0.00, 0.200, 70, 0, null, null, null, null, null, null, null, 0, 'http://www.son-video.com/images/', null),
    (184, 1, 1, 'Indéfinie', null, 9.99, 0.200, 1, 1, null, null, null, null, null, null, null, 0, 'http://www.son-video.com/images/', '')
;
INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type, derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva, V_taux_marge, V_taux_marque, V_marge)
VALUES
    (1, 1, 154357533114364, 81078, 'ARCAMRBLINKNR', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
    (1, 1, 449698107735904, 143088, 'QEDQE6119', 'article', '2019-11-16 13:22:03', 56, 144, 6, 0.200, 1.206, 0.547, 6.37),
    (1, 1, 536515430115643, 143169, 'SVCATAPREMHIVER', 'generique', '2019-12-11 12:27:51', 296, 70, 6, 0.200, null, null, -0.01),
    (1, 1, 536515430115644, 1, 'INSTOCKBPRODUCT', 'article', '2019-12-11 12:27:51', 258, 96, 6, 0.200, 0.576, 0.366, 75.80),
    (1, 1, 124307721969027, 139789, 'CARTECADEAUSVD1120', 'article', '2020-01-29 22:10:48', 184, 1, 13, 0.200, null, 1.000, 933.33)
;
INSERT INTO backOffice.fournisseur (semaphore, id_fournisseur, fournisseur, status, taux_escompte, id_paiement_fournisseur, id_delai_paiement_fournisseur, remise_sur_tarif, en_compte, encours_maximum, encours_consomme, marque_disponible, marque_en_vente, siege_contact, siege_telephone, siege_mobile, siege_societe, siege_email, siege_fax, siege_site, siege_ville, siege_code_postal, siege_pays, siege_adresse, siege_adresse1, commercial_contact, commercial_telephone, commercial_mobile, commercial_email, comptabilite_contact, comptabilite_telephone, comptabilite_mobile, comptabilite_email, technique_contact, technique_telephone, technique_mobile, technique_email, commentaire, id_pays_origine, franco, V_delai_lvr_moyen, frais_port, numero_compte, login, pass, fermeture, SIREN, siret, intracom, ape, hors_delai_auto, reliquat_attente_auto)
VALUES
    (943050784937153, 1, 'Indefini', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
    (943050784937153, 162, 'PPL', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
    (943050784937153, 400, 'LA BOITE CONCEPT', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN', '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0)
;
INSERT INTO backOffice.marque (semaphore, id_marque, marque, logo, status, importateur, histoire, url_source_doc, url_source_image, specialite, produit_de_reference, gamme_qualite, public, type_distribution, avis, a_savoir, id_pays, tarif_base_prix_achat_tarif, prix_achat_tarif_prix_vente, etiquetage, en_compte, id_marque_pixmania, V_nb_avis, V_moyenne_avis, V_nb_recommandation, id_redoute, keyword, garanti, meta_description)
VALUES
    (864984545730971, 262, 'Arcam', 'http://www.son-video.com/images/static/marques/Arcam.gif', 'oui', 'Cabasse', '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2, 4.000, 1, 0, '', '', ''),
    (415502164899839, 156, 'QED', 'http://www.son-video.com/images/static/marques/QED.gif', 'oui', 'GT Audio', '', '', '', '', '', 5, '', '', '', '', 162, 0.000, 0.000, 'non', 'true', 22777, 284, 4.725, 271, 0, '', '', ''),
    (244836806547676, 1219, 'SVD Boutique', null, 'oui', '', '', '', '', '', '', 0, '', '', '', '', 40, 0.000, 0.000, 'non', 'true', null, 0, null, 0, null, '', '', ''),
    (455644551157620, 819, 'Carte Cadeau', '', 'oui', '', '', '', '', '', '', 0, '', '', '', '', 5, 0.000, 0.000, 'non', 'true', null, 0, null, 0, 0, '', '', '')
    ;
INSERT INTO backOffice.couleur (semaphore, id_couleur, code, couleur, url_image, rang, id_parent, parent, updated_at)
VALUES
    (914281290876010, 1, 'XXX', 'indéfinie', '', 0, 0, 0, '2019-03-01 10:35:41'),
    (725043410179246, 5, 'NR', 'Noir', 'http://www.son-video.com/images/static/Coloris/Noir.gif', 48, 5, 1, '2019-03-01 10:35:41')
;
INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status, prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente, prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids, poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur, id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur, V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit, V_qte_dispo_resa, V_delai_lvr, etat_statut, etat_devalorisation, etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert, V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page, url_image, comparateur, recherche, description_panier, description_courte, diagonale, description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max, prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine, code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage, compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants, fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre, rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost, is_main, is_auto_picked)
 VALUES 
  ('2019-09-03 01:06:38', 1, 0, 81078, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1, 0),
  ('2019-11-17 00:34:01', 1, 0, 143088, '2019-11-16', 'todo', 5.28, null, 0.00, 14.00, 14.00, 0.02, 0.00, 0.00, 0.460, 'Y', 1, 1, 'N', null, 'QE6119', 162, 156, 'Performance Audio 40i (1 m)', 'performance', 1, 1.00, 0, 25, 'a', null, 0, null, null, null, null, null, 0, 0, 0, 0, 0, 99, '', 'http://www.son-video.com/Rayons/Cable-Enceinte/QED-Performance-Audio-40.html', '', 'oui', 'oui', 'Câble d''enceinte QED Performance Audio 40i, longueur 1 m', 'QED Performance Audio 40i (1 m)', 0, '', 0.000, 0.000, 0, 0, 0.00, 0.00, null, 67, null, null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 1, 0),
  ('2019-12-11 12:27:51', 1, 1, 143169, '2017-11-20', 'oui', 0.01, null, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.800, 'N', 1, 1, 'Y', null, null, 162, 1219, 'Catalogue 2020', 'Catalogue 2020', 1, 0.00, 1, 0, 'a', null, 1, null, null, null, null, null, 0, 0, 0, 0, 0, 0, '', '', '', 'non', 'oui', 'Catalogue 2020 Son-Vidéo.com, finition premium', 'Catalogue 2020 Son-Vidéo.com, finition premium', 0, null, 0.000, 0.000, 0, 0, 0.00, 0.01, '2017-12-15', null, '85189000', null, 0.00, 0.000, 0.00, 0.00, 0, 0, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 0, 0),
  ('2019-09-03 01:06:38', 1, 0, 1, '2013-02-20', 'last', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 1, 2, 'b', 81078, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1, 0),
  ('2020-01-30 00:34:00', 1, 0, 139789, '2019-07-23', 'oui', 0.00, 0.00, 0.00, 1120.00, 0.00, 0.00, 0.00, 0.00, 0.000, 'Y', 1, 1, 'N', null, null, 1, 819, '1120 euros', null, 1, 0.00, 900, 0, 'a', null, 900, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, '', '', '', 'non', 'non', '', '', 0, '', 0.000, 0.000, 0, 0, 0.00, 1120.00, null, null, null, null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 1, 1)
;
INSERT INTO backOffice.transporteur (semaphore, id_transporteur, code, transporteur, liste, is_expressiste, ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai, prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
VALUES
    (20030702191152, 2, 'COLSV', 'Colissimo', 'oui', 0, 9, 1, 'colissimo suivi national ; étiquette à double code barre (SEI)', '', 'FR', 0.000, 50.000, '48 heures', 0.000, 0, 'http://www.coliposte.net/gp/services/main.jsp?m=10003005&colispart=', '', 5),
    (20030702191206, 5, 'EMPT', 'Emport dépôt', 'oui', 0, 1, 1, '', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 1)
;
INSERT INTO backOffice.sf_guard_user (id, username, algorithm, salt, password, created_at, last_login, is_active, is_super_admin)
VALUES
    (1000, 'backoffice', 'none', '', '', now(), now(), 1, 1)
;
INSERT INTO backOffice.sf_guard_user_profile (id, societe, site, usr_societe, titre, civilite, nom, prenom, email, poste, poste_sda, employe, signature)
VALUES
(1000, 'Son Video Distribution', 'Champigny', 1, 'Systéme', 'M.', 'Admin', 'AI', '<EMAIL>', '666', 1, 1, 'La matrice')
;


INSERT INTO backOffice.prospect (semaphore, id_prospect, identifiant, mot_passe, mot_passe_crypte, date_creation, date_modification, prescripteur, origine, origine_date, cnt_type, cnt_email, cnt_societe, cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal, cnt_ville, cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax, cnt_numero_tva, cnt_lvr_type, cnt_lvr_email, cnt_lvr_societe, cnt_lvr_civilite, cnt_lvr_nom, cnt_lvr_prenom, cnt_lvr_adresse, cnt_lvr_code_postal, cnt_lvr_ville, cnt_lvr_id_pays, cnt_lvr_telephone, cnt_lvr_telephone_bureau, cnt_lvr_mobile, cnt_lvr_fax, cnt_lvr_numero_tva, site_web, blacklist, date_naissance, profession_id, envoi_email, email, type, societe, civilite, nom, prenom, envoi_identifiants, encours_interne, encours_sfac, id_mode_paiement, classification, acceptation_relicat, franco, RIB, nom_banque, ville_banque, BIC, IBAN, atradius, siren, incoterm, is_premium, passion, installation, style_musique, genre_cinema, musiques_preferees, cinemas_preferes, npai)
VALUES
    (0, 1500003, null, 'xxxxxxxx', '0b0cfc07fca81c956ab9181d8576f4a8', '2018-12-24 20:15:03', '2019-09-03 08:32:21', null, null, null, 'particulier', '<EMAIL>', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', '4 CHEMIN TRAVERS DES CAILLOUX', '95530', 'LA FRETTE SUR SEINE', 67, '', '', '0689700197', '', null, 'particulier', '<EMAIL>', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', '4 CHEMIN TRAVERS DES CAILLOUX', '95530', 'LA FRETTE SUR SEINE', 67, '', '', '0689700197', '', null, null, 0, null, null, 1, '<EMAIL>', 'particulier', '', 'M.', 'VAN RENTERGHEM', 'CHRISTOPHER', 0, 0, 0, null, null, 0, null, null, null, null, null, null, 'Pas soumis', null, null, 0, null, null, null, null, null, null, 0)
;
INSERT INTO backOffice.commande (semaphore, trigger_actif, trigger_actif2, id_commande, no_commande_origine, creation_origine, date_creation, id_boutique, vendeur, flux, V_statut_traitement, en_attente_de_livraison, facturation_mode, ip, ip_pays, validite_fianet, rappel_client, emport_depot, lvr_particulier, lvr_assurance, detaxe_export, cmd_intragroupe, id_prospect, id_devis, commentaire_facture, expedition_diff_date, cloture_date, cloture_usr, tradedoubler_id, nombre_visite, clef, date_export_status, compteur_paiement, cnt_fct_type, cnt_fct_email, cnt_fct_societe, cnt_fct_civilite, cnt_fct_nom, cnt_fct_prenom, cnt_fct_adresse, cnt_fct_code_postal, cnt_fct_ville, cnt_fct_id_pays, cnt_fct_telephone, cnt_fct_telephone_bureau, cnt_fct_mobile, cnt_fct_fax, cnt_fct_numero_tva, cnt_fct_no_tva_validite, cnt_lvr_type, cnt_lvr_email, cnt_lvr_societe, cnt_lvr_civilite, cnt_lvr_nom, cnt_lvr_prenom, cnt_lvr_adresse, cnt_lvr_code_postal, cnt_lvr_ville, cnt_lvr_id_pays, cnt_lvr_telephone, cnt_lvr_telephone_bureau, cnt_lvr_mobile, cnt_lvr_fax, cnt_lvr_numero_tva, V_montant_ttc, V_montant_ht, V_trcns_montant, V_trcns_montant_accepte, V_trcns_montant_remise, V_pmts_montant, V_pmts_montant_accepte, V_pmts_montant_remise, V_rmbts_montant, V_rmbts_montant_accepte, V_rmbts_montant_remise, V_date_lvr_prevue_max, email_confirmation, sms_confirmation, promotion_id, entete_svd, emport_depot_paris, rdv_socol, id_transporteur, id_pdt_transporteur, tpt_option_code, atradius, relance_compta, id_commande_mere, refacturation, depot_emport, sms_emport, last_modified_at, sales_channel_id)
VALUES
    (894434378384086, 1, 1, 1712826, '1724028', 'backoffice.sonvideopro.com', '2019-09-26 11:14:13', null, null, 'traitement', 'trcn_acceptation_directe', 0, 'bl', '195.7.108.10', null, 'attente', 'N', 'N', 'Y', 'N', 'non', 0, 1500003, null, '', null, null, null, null, 0, null, '1990-01-01', 3, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, null, 'particulier', '<EMAIL>', '', 'M.', 'Cartier', 'Raymond', '3 impasse de la volpette', '21800', 'SAINT GAUDENS', 67, '0561887801', '', '0561887801', '', null, 708.99, 590.83, 708.99, 0.00, 0.00, 708.99, 0.00, 0.00, 0.00, 0.00, 0.00, null, null, null, null, 1, 0, 0, 2, 1, null, 0, 0, null, 0, null, null, '2019-09-26 11:18:33', 1)
;

INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible, generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible, expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone, email, id_user, description, surcout_emport, product_emport, nom_transfert, nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre, horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code, tpe_id, abreviation)
VALUES
    (1, 'Champigny', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi', 'de 9h &agrave; 18h30', '', 1, 1, '03', 6100907, 'Cha'),
    (2, 'Autre', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi', 'de 9h &agrave; 18h30', '', 1, 1, '02', 6100907, 'Au')
;
INSERT INTO backOffice.WMS_area_type (area_type_id, label)
VALUES
    (4, 'stock')
;
INSERT INTO backOffice.WMS_area (area_id, area_type_id, warehouse_id, code, label)
VALUES
    (1, 4, 1, '03.04', 'Petit stock haut'),
    (2, 4, 2, '02.01', 'Stock unique')
;
INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
VALUES
    (1, '03.01.a.01.01.01', 1, '03.01.A$01.00.01', 1),
    (2, '03.01.a.01.01.02', 1, '03.01.A$01.00.02', 1),
    (3, '02.01.a.01', 2, '02.01.A$01', 1)
;
INSERT INTO backOffice.bon_livraison (semaphore, id_bon_livraison, id_depot, id_commande, id_transfert, id_facture, is_petit_stock, montant_contre_remboursement, date_creation, utilisateur_creation, status, detaxe_export, id_transporteur, id_pdt_transporteur, date_export_transporteur, utilisateur_export_transporteur, date_validation, utilisateur_validation, email_validation, sms_validation, numero_enquete, date_declaration_perte, utilisateur_declaration_perte, motif_reexpedition, impression_expedition, impression_date, cnt_type, cnt_email, cnt_societe, cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal, cnt_ville, cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax, cnt_numero_tva, lvr_particulier, lvr_assurance, contact, sav, entete_svd, scan_depart_c, scan_arrive_p, scan_depart_p, scan_retour_c, numero_cni, numero_kbis, numero_dbc)
VALUES
   (125191468862064, 4263254, 1, 1712826, null, 1211490, 0, null, '2019-08-29 04:05:37', 'backoffice', 'expedie', 'non', 2, 1, '2019-08-29 11:41:10', 'backoffice', '2019-08-29 20:15:09', 'backoffice', null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null),
   (125191468862065, 1111111, 1, 1712826, null, null, 0, null, '2019-08-29 04:05:38', 'backoffice', 'au depart', 'non', 2, 1, '2019-08-29 11:41:10', 'backoffice', '2019-08-29 20:15:09', 'backoffice', null, null, null, null, null, null, 'oui', '2019-08-29 07:16:29', 'particulier', '<EMAIL>', '', 'M.', 'Abegg', 'Mark', '23 rue richard lenoir', '75011', 'Paris', 67, '0695679667', '', '', '', null, 'Y', 'N', 0, 0, 1, null, null, null, null, null, null, null)
;
INSERT INTO backOffice.WMS_move_mission_type (move_mission_type_id, code, label)
VALUES
    (1, 'arrangement', 'rangement')
;
INSERT INTO backOffice.WMS_move_mission (move_mission_id, move_mission_type_id, created_at, assigned_to, assigned_at, started_at, ended_at, canceled_at, canceled_by, product_id, quantity)
VALUES
    (1, 1, '2019-11-27 10:38:59', 1000, '2019-11-27 12:47:27', null, null, null, null, 81078, 1)
;

-- Fixtures data
INSERT INTO backOffice.WMS_product_location (location_id, product_id, delivery_ticket_id, move_mission_id, quantity)
VALUES
    (1, 81078, null, null, 9),
    (1, 81078, 4263254, null, 3),
    (1, 81078, null, 1, 1),
    (1, 143169, null, null, 1),
    (1, 143088, null, null, 2),
    (2, 143088, null, null, 1),
    (1, 1, null, null, 1)
;

INSERT INTO backOffice.mouvement_stock (type, id_produit, id_emplacement, quantite, commentaire, utilisateur, move_mission_id, trigger_actif)
VALUES
    ('entree', 81078, 1, 9, 'initialisation du stock pour les tests', 'backoffice', null, 0),
    ('entree', 81078, 1, 3, 'initialisation du stock pour les tests', 'backoffice', null, 0),
    ('entree', 81078, 1, 1, 'initialisation du stock pour les tests', 'backoffice', 1, 1),
    ('entree', 143169, 1, 1, 'initialisation du stock pour les tests', 'backoffice', null, 1),
    ('entree', 143088, 1, 2, 'initialisation du stock pour les tests', 'backoffice', null, 0),
    ('entree', 143088, 2, 1, 'initialisation du stock pour les tests', 'backoffice', null, 1),
    ('entree', 1, 1, 1, 'initialisation du stock pour les tests', 'backoffice', null, 1)
;

-- other test dependencies
 INSERT INTO backOffice.produit_bon_livraison (id, id_bon_livraison, id_produit, id_unique, description, quantite, prix_vente, tva, remise_type, remise_montant, remise_description, duree_garantie_ext, prix_garantie_ext, tva_garantie_ext, commission_garantie_ext, tva_commission_garantie_ext, vendeur_garantie_ext, duree_garantie_vc, prix_garantie_vc, commission_garantie_vc, tva_commission_garantie_vc, vendeur_garantie_vc, poids, nombre_colis)
 VALUES
    (3190815, 1111111, 143088, 1, 'description', 3, 22.00, 0.200, null, 0.00, null, null, 0.00, 0.200, 0.00, 0.200, null, null, 0.00, 0.00, 0.200, null, 0.220, 1)
 ;
