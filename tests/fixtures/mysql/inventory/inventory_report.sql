INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type, derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva, V_taux_marge, V_taux_marque, V_marge)
VALUES
    (1, 1, 154357533114364, 81079, 'AAAAAAAAA', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576, 0.366, 75.80)
;

INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status, prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente, prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids, poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur, id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur, V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit, V_qte_dispo_resa, V_delai_lvr, etat_statut, etat_devalorisation, etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert, V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page, url_image, comparateur, recherche, description_panier, description_courte, diagonale, description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max, prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine, code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage, compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants, fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre, rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost, is_main)
 VALUES 
  ('2019-09-03 01:06:38', 1, 0, 81079, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'TEST', 162, 262, 'test', 'test', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', '', '', 'oui', 'oui', '', '', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '0', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1)
;


INSERT INTO backOffice.BO_INV_initial_state (inventory_id, product_id, location_id, quantity)
VALUES
    (1, 81078, 2, 4),
    (1, 81078, 3, 4),
    (1, 81079, 3, 1)
;

INSERT INTO backOffice.BO_INV_collecte_article (id, BO_INV_collecte_id, id_produit, id_emplacement, sf_guard_user_id, quantite, quantite_stock, date_actualisation, date_collecte)
VALUES
  (1, 1, 81078, 1, 1, 4, 8, '2020-01-10 13:24:09', '2020-01-10 13:24:09'),
  (2, 1, 81078, 2, 1, 3, 8, '2020-01-10 13:24:09', '2020-01-10 13:24:09')
;

INSERT INTO backOffice.BO_INV_differential (inventory_id, product_id, brand_id, buy_price, expected_quantity, expected_total_value, counted_quantity, counted_total_value, computed_stock_difference, computed_total_value_difference, is_validated_manually)
VALUES
  (1, 81078, 262, 131.58, 8, 1052.64, 7, 0.00, -1, -131.58, 0),
  (1, 81079, 262, 131.58, 1, 1052.64, 0, 0.00, -1, -131.58, 0)
;

INSERT INTO backOffice.WMS_area (area_id, area_type_id, warehouse_id, code, label)
VALUES
    (3, 4, 2, 'bleu', 'ici'),
    (4, 4, 2, 'rouge', 'là bas')
;

INSERT INTO backOffice.WMS_location (location_id, code, area_id, label, is_active)
VALUES
    (4, '03.05.a.01.01.01', 2, '03.05.A$01.01.01', 1),
    (5, '03.04.a.01.01.01', 2, '03.04.A$01.01.01', 1),
    (6, 'bleu.1', 3, 'bleu clair', 1),
    (7, 'rouge.9', 4, 'rouge foncé', 1)
;

INSERT INTO backOffice.BO_INV_zone_location (zone_id, location_id)
VALUES
    (1, 4),
    (1, 5),
    (1, 6)
;

-- Partial inventory
INSERT INTO backOffice.BO_INV_inventaire (id, inv_date, inv_date_validation, id_depot, collecte_active_id, inv_id_utilisateur_validation, statut, type, name)
VALUES
    (3, '2019-08-08 11:38:32', null, 1, 2, 1000, 'created', 'partial', null)
;

INSERT INTO backOffice.BO_INV_zone_inventory (inventory_id, zone_id)
    VALUES (3, 1);

INSERT INTO backOffice.BO_INV_inventory_location (inventory_id, location_id, scanned_empty_at)
VALUES
    (3, 1, null),
    (3, 4, '2019-08-08 11:38:32'),
    (3, 5, null)
;

INSERT INTO backOffice.BO_INV_collecte (id, BO_INV_inventaire_id, collecte_type, numero)
VALUES
    (3, 3, 'global', 1),
    (4, 3, 'produit', 2)
;

INSERT INTO backOffice.BO_INV_differential (inventory_id, product_id, brand_id, buy_price, expected_quantity, expected_total_value, counted_quantity, counted_total_value, computed_stock_difference, computed_total_value_difference, is_validated_manually)
VALUES
    (3, 81078, 262, 131.58, 0, 1052.64, 2, 0.00, 2, -131.58, 0)
;

INSERT INTO backOffice.BO_INV_collecte_article (id, BO_INV_collecte_id, id_produit, id_emplacement, sf_guard_user_id, quantite, quantite_stock, date_actualisation, date_collecte)
VALUES
    (3, 3, 81078, 1, 1, 2, 0, '2020-01-10 13:24:09', '2020-01-10 13:24:09')
;

UPDATE backOffice.BO_INV_inventaire SET statut = 'closed' WHERE id = 2;
