-- dependencies
INSERT INTO backOffice.pays (id_pays, pays, code_2_lettres, code_3_lettres, code_numerique, groupe, livraison, liste,
                             cee, dom_tom, ordre_affichage, code_postal_motif, code_postal_commentaire, transport,
                             transport_offre_speciale)
VALUES (67, 'FRANCE', 'FR', 'FRA', 250, 'France', 'oui', 'oui', 'oui', null, 1, '^[0-9]{5}$', '5 chiffres', 1, 1)
;
INSERT INTO backOffice.BO_SYS_tva (id, nom_tva, date_debut_application, date_fin_application, taux_tva, id_pays)
VALUES (5, 'standard', '2014-01-01', null, 0.2000, 67)
;
INSERT INTO backOffice.prospect (semaphore, id_prospect, identifiant, mot_passe, mot_passe_crypte, date_creation,
                                 date_modification, prescripteur, origine, origine_date, cnt_type, cnt_email,
                                 cnt_societe, cnt_civilite, cnt_nom, cnt_prenom, cnt_adresse, cnt_code_postal,
                                 cnt_ville, cnt_id_pays, cnt_telephone, cnt_telephone_bureau, cnt_mobile, cnt_fax,
                                 cnt_numero_tva, cnt_lvr_type, cnt_lvr_email, cnt_lvr_societe, cnt_lvr_civilite,
                                 cnt_lvr_nom, cnt_lvr_prenom, cnt_lvr_adresse, cnt_lvr_code_postal, cnt_lvr_ville,
                                 cnt_lvr_id_pays, cnt_lvr_telephone, cnt_lvr_telephone_bureau, cnt_lvr_mobile,
                                 cnt_lvr_fax, cnt_lvr_numero_tva, site_web, blacklist, date_naissance, profession_id,
                                 envoi_email, email, type, societe, civilite, nom, prenom, envoi_identifiants,
                                 encours_interne, encours_sfac, id_mode_paiement, classification, acceptation_relicat,
                                 franco, RIB, nom_banque, ville_banque, BIC, IBAN, atradius, siren, incoterm,
                                 is_premium, passion, installation, style_musique, genre_cinema, musiques_preferees,
                                 cinemas_preferes, npai)
VALUES (0, 1500001, null, 'xxxxxxxx', '0b0cfc07fca81c956ab9181d8576f4a8', '2018-12-24 19:57:58', '2019-09-03 08:32:21',
        null, null, null, 'particulier', '<EMAIL>', '', 'M.', 'Poncelet', 'Claude', '', '', '', 67, '',
        '', '', '', null, 'particulier', '<EMAIL>', '', 'M.', '', '', '', '', '', 67, '', '', '', '',
        null, null, 0, null, null, 1, '<EMAIL>', 'particulier', '', 'M.', 'Poncelet', 'Claude', 0, 0, 0,
        null, null, 0, null, null, null, null, null, null, 'Pas soumis', null, null, 0, null, null, null, null, null, null, 0),
       (0, 1500002, null, 'xxxxxxxx', '0b0cfc07fca81c956ab9181d8576f4a8', '2018-12-24 20:06:02', '2019-09-03 08:32:21',
        null, null, null, 'particulier', '<EMAIL>', '', 'M.', 'Boutet', 'Brice', '18 Ouest rue du Brouaz 8eme etg
interphone n°3', '74100', 'ANNEMASSE', 67, '', '', '', '', null, 'particulier', '<EMAIL>', '', 'M.',
        'Boutet', 'Brice', '18 Ouest rue du Brouaz 8eme etg
interphone n°3', '74100', 'ANNEMASSE', 67, '', '', '', '', null, null, 0, null, null, 1, '<EMAIL>',
        'particulier', '', 'M.', 'Boutet', 'Brice', 0, 0, 0, null, null, 0, null, null, null, null, null, null, 'Pas soumis', null,
        null, 0, null, null, null, null, null, null, 0),
       (0, 1500003, null, 'xxxxxxxx', '0b0cfc07fca81c956ab9181d8576f4a8', '2018-12-24 20:15:03', '2019-09-03 08:32:21',
        null, null, null, 'particulier', '<EMAIL>', '', 'M.', 'VAN RENTERGHEM',
        'CHRISTOPHER', '4 CHEMIN TRAVERS DES CAILLOUX', '95530', 'LA FRETTE SUR SEINE', 67, '', '', '0689700197', '',
        null, 'particulier', '<EMAIL>', '', 'M.', 'VAN RENTERGHEM',
        'CHRISTOPHER', '4 CHEMIN TRAVERS DES CAILLOUX', '95530', 'LA FRETTE SUR SEINE', 67, '', '', '0689700197', '',
        null, null, 0, null, null, 1, '<EMAIL>', 'particulier', '', 'M.',
        'VAN RENTERGHEM', 'CHRISTOPHER', 0, 0, 0, null, null, 0, null, null, null, null, null, null, 'Pas soumis', null, null, 0,
        null, null, null, null, null, null, 0),
       (0, 1500004, null, 'xxxxxxxx', '0b0cfc07fca81c956ab9181d8576f4a8', '2018-12-24 20:16:45', '2019-09-03 08:32:21',
        null, null, null, 'particulier', '<EMAIL>', '', 'M.', 'BOUTROUX', 'Vincent', '', '', '', 67,
        '', '', '', '', null, 'particulier', '<EMAIL>', '', 'M.', '', '', '', '', '', 67, '', '', '',
        '', null, null, 0, null, null, 1, '<EMAIL>', 'particulier', '', 'M.', 'BOUTROUX', 'Vincent',
        0, 0, 0, null, null, 0, null, null, null, null, null, null, 'Pas soumis', null, null, 0, null, null, null, null, null,
        null, 0),
       (0, 1500005, null, 'xxxxxxxx', '0b0cfc07fca81c956ab9181d8576f4a8', '2018-12-24 20:24:21', '2019-09-03 08:32:21',
        null, null, null, 'particulier', '<EMAIL>', '', 'M.', 'Balssa', 'Etienne', '', '', '', 67, '',
        '', '', '', null, 'particulier', '<EMAIL>', '', 'M.', '', '', '', '', '', 67, '', '', '', '',
        null, null, 0, null, null, 1, '<EMAIL>', 'particulier', '', 'M.', 'Balssa', 'Etienne', 0, 0, 0,
        null, null, 0, null, null, null, null, null, null, 'Pas soumis', null, null, 0, null, null, null, null, null, null, 0)
;

INSERT INTO backOffice.CTG_TXN_domaine (id, trigger_actif, trigger_actif2, domaine, espace, domaine_btq, rang, url_page,
                                        menu, comprendre_pour_choisir, meta_description, presentation)
VALUES (3, 1, 0, 'Haute-fidélité', 0, 'Audio Vidéo', 2, '/Conseil/Hifi/Hifi.html', 1, 1, '', null),
       (6, 1, 0, 'Accessoires', 0, 'Accessoires', 8, '/Rayons/Accessoires/index.html', 1, 1, '', ''),
       (13, 1, 0, 'Indéfini', 0, 'Indéfini', 15, null, 0, 1, '', null),
       (15, 1, 0, 'Enceintes', 0, 'tmp', 3, '/Enceintes', 1, 1, '', '')
;
INSERT INTO backOffice.warranty_type (type, label, description)
VALUES
    ('NON', 'Aucune', 'A utiliser pour forcer le fait de ne pas avoir de garantie et ne pas utiliser la règle parente'),
    ('SON', 'Son', 'Garantie pour les produits appartenant à la famille du son'),
    ('VIDEO', 'Vidéo', 'Garantie pour les produits de diffusion vidéo')
;
INSERT INTO backOffice.CTG_TXN_categorie (id_categorie, trigger_actif, trigger_actif2, neteven_couleur, neteven_poids, neteven_televiseur, neteven_type1, categorie, url_categorie, dft_domaine_id, garantie_5, videoprojecteur, diagonale, id_bbac_categorie, port_facture, port_facture_tva, export, section, url_section, typologie, critere_section_sommaire, export_amazon, keyword_amazon, categorie_amazon, hors_gabarit, hors_gabarit_poids_seuil, deb_nomenclature, url_page, pixmania_segment_id, mesure_diagonale, mesure_longueur, code_type_produit_presto, code_douanier)
 VALUES 
  (59, 1, 0, 1, 0, 0, 1, 'Meubles et supports', '', 6, 'non', 'non', 'non', 1, 15.00, 0.200, 'oui', '', '', '', 'marque', '1', 'home cinema', 'TV, DVD, Home Cinéma', 'Y', 0, '85299041', '/Rayons/Accessoires/MeubleHifi.html', 3760, 0, 0, '610', '8529904900'),
  (96, 1, 0, 1, 0, 0, 1, 'Distributeurs et transmetteurs', '', 6, 'non', 'non', 'non', 11, 9.90, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'N', null, '85229080', '/Rayons/HomeCinema/Telecommandes/RelaisCGV.html', 3050, 0, 0, '610', '85229080'),
  (137, 1, 1, 1, 1, 0, 1, 'Enceintes', '', 15, 'oui', 'non', 'non', 1, 0.00, 0.200, 'oui', '', '', '', 'marque', '0', null, null, 'Y', 17, null, '/Rayons/Hifi/Enceintes/CatEnceintes1.html', null, 0, 0, '613', '85182200'),
  (18, 1, 0, 1, 0, 0, 1, 'Câbles audio', '', 6, 'non', 'non', 'non', 1, 7.90, 0.200, 'oui', '', '', '', 'marque', '1', 'câble', 'MP3, Audio portable, Hi-fi', 'N', null, '85444991', '/Rayons/Cables-audio.html', 118, 0, 1, '610', '85444991')
;
INSERT INTO backOffice.CTG_TXN_souscategorie (id, trigger_actif, trigger_actif2, souscategorie, url_page, port_facture,
                                              port_facture_tva, dft_categorie_id, reevoo, rue_du_commerce, url_nav,
                                              id_domaine, id_domaine_2, id_categorie_ebay, id_categorie_boutique_ebay,
                                              pixmania_segment_id, hors_gabarit, illustration,
                                              redoute_nomenclature_node)
VALUES (95, 1, 1, 'Enceintes encastrables', '/Rayons/HomeCinema/EnceintesAV/Inwall.html', 7.99, 0.200, 137, 1,
        'MC-11226', '/Parts/Nav/NavR_EnceintesEncastrees.html', 3, 13, '93382', '3', 106, 0,
        'http://www.son-video.com/images/dynamic/Enceintes_encastrables/articles/Artsound/ARTSFL101/Artsound-FL101_P_140.jpg',
        ''),
       (144, 1, 1, 'Pieds d''enceintes', '/Rayons/Accessoires/PiedsEnceinte.html', 9.99, 0.200, 59, 1, 'MC-5335',
        '/Parts/Nav/NavR_Pied_Enceinte.html', 3, 13, '137923', '3', 3764, 0,
        'http://www.son-video.com/images/dynamic/Supports/articles/NorStone/NORSTSTYLUM2NR/NorStone-Stylum-2-Noir_P_180.jpg',
        ''),
       (258, 1, 1, 'Récepteurs Bluetooth', '/systeme-audio-sans-fil/recepteur-bluetooth.html', 3.99, 0.200, 96, 0, null,
        null, null, null, '79323', '1', 9630, 0,
        'http://www.son-video.com/images/dynamic/Distributeurs_et_transmetteurs/articles/Focal/FOCALAPTXUWREC/Focal-Universal-Wireless-APTX-Receiver_P_140.jpg',
        null),
       (56, 1, 1, 'Câbles d''enceintes', '/Rayons/Cables/EspaceCable/CablesA_Enceintes.html', 5.99, 0.200, 18, 1,
        'MC-4702', '/Parts/Nav/NavR_Cable_Enceintes.html', 3, 13, '137917', '1', 118, 0,
        'http://www.son-video.com/images/dynamic/Cables_audio/composes/NORSTCL40010M/NorStone-CL400-Classic-2-x-4-mm2-10-m-_P_180.jpg',
        '')
;
INSERT INTO backOffice.produit (trigger_actif, trigger_actif2, semaphore, id_produit, reference, type,
                                derniere_actualisation, id_souscategorie, V_id_categorie, V_id_domaine, tva,
                                V_taux_marge, V_taux_marque, V_marge)
VALUES (1, 1, 154357533114364, 81078, 'ARCAMRBLINKNR', 'article', '2019-08-30 20:14:44', 258, 96, 6, 0.200, 0.576,
        0.366, 75.80),
       (1, 1, 617301053011139, 81123, 'LBCLD25BP', 'article', '2019-07-25 19:02:14', 144, 59, 6, 0.200, 0.838, 0.456,
        147.82),
       (1, 1, 848803876758370, 128416, 'BWCCM74', 'article', '2018-06-19 16:48:33', 95, 137, 15, 0.200, 0.818, 0.450,
        337.50),
       (1, 1, 57384874725141, 13895, 'NORSTCL25025M', 'compose', '2019-09-03 20:04:50', 56, 18, 6, 0.200, 1.214, 0.548,
        20.33)
;

INSERT INTO backOffice.fournisseur (semaphore, id_fournisseur, fournisseur, status, taux_escompte,
                                    id_paiement_fournisseur, id_delai_paiement_fournisseur, remise_sur_tarif, en_compte,
                                    encours_maximum, encours_consomme, marque_disponible, marque_en_vente,
                                    siege_contact, siege_telephone, siege_mobile, siege_societe, siege_email, siege_fax,
                                    siege_site, siege_ville, siege_code_postal, siege_pays, siege_adresse,
                                    siege_adresse1, commercial_contact, commercial_telephone, commercial_mobile,
                                    commercial_email, comptabilite_contact, comptabilite_telephone, comptabilite_mobile,
                                    comptabilite_email, technique_contact, technique_telephone, technique_mobile,
                                    technique_email, commentaire, id_pays_origine, franco, V_delai_lvr_moyen,
                                    frais_port, numero_compte, login, pass, fermeture, SIREN, siret, intracom, ape,
                                    hors_delai_auto, reliquat_attente_auto)
VALUES (943050784937153, 1, 'Indefini', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN',
        '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '',
        '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
       (943050784937153, 162, 'PPL', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN',
        '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '',
        '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0),
       (943050784937153, 400, 'LA BOITE CONCEPT', 'oui', 0, 2, 4, '', 'non', null, null, '', '', 'Richard OSKANIAN',
        '01 01 01 01 01', '', '', '', '+33 1 44 44 44 44', '', '', '', null, '', '', '', '', '', '', '', '', '', '', '',
        '', '', '', '', 67, 0.00, null, '', '', '', '', '', '0', '0', '', '', '0', 0)
;
INSERT INTO backOffice.marque (semaphore, id_marque, marque, logo, status, importateur, histoire, url_source_doc,
                               url_source_image, specialite, produit_de_reference, gamme_qualite, public,
                               type_distribution, avis, a_savoir, id_pays, tarif_base_prix_achat_tarif,
                               prix_achat_tarif_prix_vente, etiquetage, en_compte, id_marque_pixmania, V_nb_avis,
                               V_moyenne_avis, V_nb_recommandation, id_redoute, keyword, garanti, meta_description)
VALUES (864984545730971, 262, 'Arcam', 'http://www.son-video.com/images/static/marques/Arcam.gif', 'oui', 'Cabasse', '',
        '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2, 4.000, 1, 0, '', '', ''),
       (720253785811601, 292, 'B&W', 'http://www.son-video.com/images/static/marques/Bowers_et_Wilkins.gif', 'oui', '',
        '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'false', 27828, 246, 4.736, 239, 0, '', '', ''),
       (485762893674674, 959, 'La Boite Concept', 'http://www.son-video.com/images/static/marques/La-boite-concept.gif',
        'oui', 'La Boite Concept', '', '', '', '', '', 0, '', '', '', '', 67, 0.000, 0.000, 'non', 'true', 701, 2,
        4.000, 1, 0, '', '', ''),
       (940157217849192, 520, 'NorStone', 'http://www.son-video.com/images/static/marques/Norstone.gif', 'oui',
        'Inovadis', '', '', 'Eric', '', '', 5, '', '', '', '', 67, 0.000, 0.000, 'oui', 'true', 2789, 1451, 4.597, 1418,
        0, 'norstone,câble,audio,vidéo,HDMI,meuble,hi-fi,TV,support,pieds,cablage,câblage', '',
        'Tous les produits NorStone : câble enceinte, câble audio vidéo, HDMI, meuble hi-fi, meuble vidéo, meuble TV avec support, support TV mural et pieds d''enceinte.')
;
INSERT INTO backOffice.couleur (semaphore, id_couleur, code, couleur, url_image, rang, id_parent, parent, updated_at)
VALUES (914281290876010, 1, 'XXX', 'indéfinie', '', 0, 0, 0, '2019-03-01 10:35:41'),
       (725043410179246, 5, 'NR', 'Noir', 'http://www.son-video.com/images/static/Coloris/Noir.gif', 48, 5, 1,
        '2019-03-01 10:35:41')
;
INSERT INTO backOffice.article (modif_date, trigger_actif, trigger_actif2, id_produit, date_creation, status, prix_achat_tarif, prix_achat_pondere, prix_achat_dernier, prix_vente, prix_vente_generalement_constate, prix_ecotaxe, prix_sorecop, prix_revendeur, poids, poids_tmp, nombre_colis, conditionnement, vente_lot, code_barre, reference_fournisseur, id_fournisseur, id_marque, modele, modele_constructeur, id_couleur, longueur, V_quantite_stock, V_stock_securite, stock_emplacement, stock_a_id_produit, V_qte_dispo_resa, V_delai_lvr, etat_statut, etat_devalorisation, etat_commentaire, etat_commentaire_public, V_qte_au_depart, V_qte_en_transfert, V_qte_cmd_attente, V_qte_cmd, V_qte_facturee, garantie_constructeur, alerte, url_page, url_image, comparateur, recherche, description_panier, description_courte, diagonale, description_videoprojecteur, zoom_min, zoom_max, distance_min, distance_max, prix_vente_constate, prix_vente_initial, date_lance_a, id_pays_origine, code_douanier, date_embargo, prix_achat_net, marge_arriere, prime_produit, rattrapage, compose, is_monomarque, icomparateur_url, icomparateur, id_item_ebay, amazon_merchants, fnac_id, fnac_url, fnac_image, reference_havre, stock_havre, quantite_havre, cdt_havre, rotation_7_jours, rotation_30_jours, rotation_90_jours, vendu_par, remplace, chronopost, is_main)
 VALUES 
  ('2019-09-03 01:06:38', 1, 0, 81078, '2013-02-20', 'oui', 131.58, 131.58, 0.00, 249.00, 249.00, 0.15, 0.00, 0.00, 0.850, 'N', 1, 1, 'N', null, 'RBLINK', 162, 262, 'rBlink', 'rBlink', 5, 0.00, 3, 2, 'a', null, 3, 0, null, null, null, null, 0, 0, 0, 0, 4, 2, '', 'http://www.son-video.com/Rayons/DAC-Audio-Bluetooth-APTX/Arcam-rBlink.html', 'http://www.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg', 'oui', 'oui', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 0, '', 0.000, 0.000, 0, 0, 0.00, 199.00, '2015-03-24', 67, '85176200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, 24418908, null, null, null, 0, 0.0, 1, 2, 6, 15, 2, 0, 1, 1),
  ('2019-07-26 00:34:00', 1, 0, 81123, '2013-02-21', 'oui', 195.00, 176.35, 0.00, 389.00, 389.00, 0.00, 0.00, 0.00, 8.450, 'N', 1, 1, 'N', null, 'LD-F-25mm-N-P', 400, 959, 'Pieds Noir laqué pour station HiFi LD120 / LD130', 'Boîte Concept Pieds LD120 LD130', 1, 0.00, 2, 0, 'a', null, 2, 0, null, null, null, null, 0, 0, 0, 0, 0, 2, '', 'http://www.son-video.com/Rayons/Enceintes-Multimedia/La-Boite-Concept-pieds-LD120-LD130.html', 'http://www.son-video.com/images/dynamic/Enceintes/articles/La_Boite_Concept/LBCLD25BP/La-Boite-Concept-Pieds-pour-station-HiFi-LD120-Noir-laque_P_180.jpg', 'oui', 'oui', 'Paire de pieds noirs laqués pour station multimédia La Boîte Concept LD120 et LD130', 'Pieds noirs laqués pour La Boîte Concept LD120 / LD130 (la paire)', 0, '', 0.000, 0.000, 0, 0, 0.00, 389.00, '2014-10-14', 67, '85182200', null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, '', 0, 23785814, null, null, null, 0, 0.0, 1, 0, 0, 0, 2, 0, 1, 1),
  ('2018-06-20 00:34:00', 1, 0, 128416, '2018-06-19', 'tmp', 412.50, null, 0.00, 900.00, 900.00, 0.00, 0.00, 0.00, 4.000, 'Y', 1, 1, 'N', null, null, 1, 292, 'BWCCM74', null, 1, 0.00, 0, 0, 'a', null, 0, null, null, null, null, null, 0, 0, 0, 0, 0, 0, '', '', '', 'non', 'non', 'Enceinte encastrable BW CCM 7.4', '', 0, '', 0.000, 0.000, 0, 0, 0.00, 0.00, null, null, null, null, 0.00, 0.000, 0.00, 0.00, 0, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 1, 0, 1, 1),
  ('2019-09-04 00:34:01', 1, 0, 13895, '2003-09-22', 'oui', 16.75, 16.75, 0.00, 45.00, 62.50, 0.50, 0.00, 0.00, 2.000, 'N', 1, 1, 'N', null, null, 1, 520, 'CL250 Classic 2,5 mm2 (25 m)', null, 1, 25.00, 235, 0, 'a', null, 183, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, '', 'http://www.son-video.com/Rayons/Cables/EspaceCable/CablesA-Enceintes-NorStone-CL250-Classic.html', 'http://www.son-video.com/images/dynamic/Cables_d_enceinte/composes/NORSTCL25025M/NorStone-CL250-Classic-2-5-mm2-25-m-_P_180.jpg', 'non', 'oui', 'Câble d''enceintes Norstone Audio CL250 Classic - Conducteur en cuivre OFC, section 2 x 2,5 mm2, gaine transparente et longueur 25 m', 'NorStone CL250 Classic (25 m)', 0, null, 0.000, 0.000, 0, 0, 0.00, 0.00, null, null, null, null, 0.00, 0.000, 0.00, 0.00, 1, 1, null, 1, null, 0, null, null, null, null, 0, 0.0, 1, 0, 0, 0, 2, 0, 1, 1)
;

INSERT INTO backOffice.batch_catalog (article_id, article_url, article_name, common_article_name, common_content_id,
                                      score_average, introduction, basket_description, short_description, domain_id,
                                      domain_name, category_id, category_name, sub_category_id, sub_category_name,
                                      media_largest_uri, media_180_uri, media_300_uri, media_300_square_uri,
                                      media_450_uri, media_450_square_uri, media_600_uri, media_1200_uri, strong_points)
VALUES (81078, 'https://www.son-video.com/article/recepteurs-bluetooth/arcam/rblink', 'Arcam rBlink', 'Arcam rBlink',
        31200, 4.88889, 'introduction du produit', 'Récepteur Audio Bluetooth APTX Arcam rBlink', 'Arcam rBlink', 6,
        'Accessoires', 42, 'Distributeurs et transmetteurs', 180, 'Récepteurs Bluetooth',
        'https://image.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_1200.jpg',
        'https://image.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_180.jpg',
        'https://image.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300.jpg',
        'https://image.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_300_square.jpg',
        'https://image.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_450.jpg',
        'https://image.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_450_square.jpg',
        'https://image.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_600.jpg',
        'https://image.son-video.com/images/dynamic/Lecteurs_reseau_et_USB/articles/Arcam/ARCAMRBLINKNR/Arcam-rBlink_P_1200.jpg',
        'R&eacute;ception Bluetooth compatible apt-X<br />DAC audio haute qualit&eacute;<br />Excellent rapport signal/bruit<br />Fabrication soign&eacute;e')
;

INSERT INTO backOffice.transporteur (semaphore, id_transporteur, code, transporteur, liste, is_expressiste, ordre_picking, bl_max, description, coordonnees, zone, poids_min, poids_max, delai, prix_extra_kg, tarif_regional, url_tracking, commentaire, id_paiement_fournisseur)
  VALUES
    (20030702191152, 2, 'COLSV', 'Colissimo', 'oui', 0, 9, 1, 'colissimo suivi national ; étiquette à double code barre (SEI)', '', 'FR', 0.000, 50.000, '48 heures', 0.000, 0, 'http://www.coliposte.net/gp/services/main.jsp?m=10003005&colispart=', '', 5),
    (20030702191206, 5, 'EMPT', 'Emport dépôt', 'oui', 0, 1, 1, '', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
    (0, 14, 'EDP8', 'Emport Depot Paris 8', 'non', 0, 2, 0, 'Emport Depot Paris 8', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 1),
    (0, 31, 'EDNA', 'Emport Depot Nantes', 'non', 0, 2, 0, 'Emport Depot Nantes', '', '', 0.000, 0.000, '0', 0.000, 0, null, null, 1)
;

INSERT INTO backOffice.BO_TPT_PDT_liste (id, transporteur_id, code_produit, libelle_produit, actif, commentaire, type, mono_colis, spidy_tracking_number_mask)
  VALUES
    (1, 2, '6C', 'Colissimo domicile', 1, 'Livraison sous 48/72h au domicile remis contre signature', 'messagerie', 1, null)
;

INSERT INTO backOffice.BO_STK_depot (id, nom_depot, id_transporteur_emport, expedition_client_possible, generation_transfert_auto, generation_bl_transfert_auto, expedition_UE_possible, expedition_hors_UE_possible, adresse, code_postal, ville, id_pays, telephone, email, id_user, description, surcout_emport, product_emport, nom_transfert, nom_panier, adresse_panier, ville_panier, localisation, horaire, ordre, horaires_jours, horaires_heures, horaires_extra, is_active, is_active_bo, code, tpe_id, abreviation)
  VALUES
    (1, 'Champigny', 5, 1, 1, 1, 1, 1, '314 rue du Pr Paul Milliez', '94506', 'Champigny sur Marne', 67, '0155091779', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible au centre logistique, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Son-Vidéo.com', 'centre logistique', '314 rue du Professeur Paul Milliez', 'Champigny sur Marne', 'https://goo.gl/maps/PvjtEonYbaM2', 'ouvert du lundi au samedi, de 9h à 18h30', 1, 'du lundi au samedi', 'de 9h &agrave; 18h30', '', 1, 1, '03', 6100907, 'Cha'),
    (2, 'Paris 8e', 14, 0, 0, 0, 0, 0, '1 Avenue de Friedland', '75008', 'Paris', 67, '0155091888', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (Paris 8e)', '1 avenue de Friedland', 'Paris 8e', 'https://goo.gl/maps/jvuXoxj8gms', 'ouvert du lundi au vendredi, 10h - 19h', 99, 'du lundi au vendredi', 'de 10h &agrave; 19h', null, 1, 1, '02', 6230679, 'Par8'),
    (3, 'Havre', null, 1, 1, 0, 0, 0, 'RD 910 Le Montcriquet', '76210', 'Saint Jean de la Neuville', 67, '0155091830', '<EMAIL>', 1, null, null, null, null, null, 'RD 910 Le Montcriquet', null, null, null, 2, null, null, null, 1, 1, '01', null, 'Hav'),
    (4, 'Schenker', null, 0, 0, 0, 0, 0, 'Parc du Hode route industrielle BP263', '76430', 'St Vigor d''Ymonville', 67, '0155091080', '<EMAIL>', 1, null, null, null, null, null, 'Parc du Hode route industrielle BP263', null, null, null, 99, null, null, null, 0, 0, '04', null, 'Sch'),
    (5, 'Nantes', 31, 0, 0, 0, 0, 0, '9 place de la Bourse', '44100', 'Nantes', 67, '0249442402', '<EMAIL>', 1, 'D&egrave;s que votre commande sera disponible en magasin, un SMS de confirmation vous sera adress&eacute;. Tout article disponible vous sera r&eacute;serv&eacute; 48h.', null, null, 'Magasin Son-Vidéo.com', 'magasin (Nantes)', '9 place de la Bourse', 'Nantes', 'https://goo.gl/maps/Cucfvro99n22', 'ouvert du mardi au samedi, 10h - 19h', 99, 'du mardi au samedi', 'de 10h &agrave; 19h', null, 1, 1, '05', 6297732, 'Nan')
;



INSERT INTO backOffice.commande (id_commande, id_prospect, cnt_fct_email, cnt_lvr_email, commentaire_facture, no_commande_origine, creation_origine, flux, promotion_id, depot_emport, id_transporteur, id_pdt_transporteur, sales_channel_id, warehouse_id, id_devis)
  VALUES
    (1, 1500001, '<EMAIL>', '<EMAIL>', 'test', '1', 'son-video.com', 'annulation', null, null, 2, 1, 1, 5, 1),
    (2, 1500001, '<EMAIL>', '<EMAIL>', 'test', '2', 'son-video.com', 'traitement', null, null, 2, 1, 1, 5, 1)
;

INSERT INTO backOffice.quote_subtype (type, label)
VALUES
    ('CLASSIQUE', 'Classique'),
    ('INTRAGROUP', 'Intra-groupe');
