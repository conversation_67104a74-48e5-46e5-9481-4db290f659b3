<?php

namespace SonVideo\Erp\Tests\Unit\SubCategory\Manager;

use App\Adapter\Serializer\SerializerInterface;
use App\Exception\InternalErrorException;
use App\Exception\NotFoundException;
use App\Sql\LegacyPdo;
use App\Tests\Unit\Test;
use App\Tests\Utils\Database\MySqlDatabase;
use App\Tests\Utils\Database\PgDatabase;
use SonVideo\Erp\SubCategory\Dto\UpdateContext\SubcategoryContextDto;
use SonVideo\Erp\SubCategory\Manager\SubcategoryUpdater as TestedClass;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

class SubcategoryUpdater extends Test
{
    public function setUp(): void
    {
        MySqlDatabase::clearDatabases();
        MySqlDatabase::loadSpecificFixtures([
            'users.sql',
            'sales_channel/sales_channels.sql',
            'category/categories.sql',
        ]);

        PgDatabase::reloadFixtures();
    }

    protected function getTestedInstance(): TestedClass
    {
        return $this->getContainer()->get(TestedClass::class);
    }

    /** @throws \Exception */
    protected function getSerializer(): SerializerInterface
    {
        return $this->getContainer()->get(SerializerInterface::class);
    }

    /**
     * @throws NotFoundException
     * @throws ExceptionInterface
     * @throws \JsonException
     * @throws InternalErrorException
     * @throws \Exception
     */
    public function test_update(): void
    {
        $subcategory_data = [
            'subcategory_id' => 95,
            'name' => 'Enceintes de tunning',
            'parent_category_id' => 96,
            'warranty_type' => 'NON',
            'outsize' => true,
            'bbac_subtype_id' => 33,
            'charged_delivery' => 8.0,
            'subcategory_type' => 'IMAGE',
            'marketplace_categories' => '[
                {
                    id: 368,
                    marketplace_id: 11
                }
            ]',
            'seller_commission_config' => json_encode(['ldw_exclude' => true, 'cultural_product' => true]),
            'custom_code' => '00011000',
            'ecotax_code' => '75368',
            'sales_channels' => [
                [
                    'subcategory_id' => 95,
                    'sales_channel_id' => 3,
                    'sales_channel' => 'amazon.fr',
                    'commission_rate' => 13.5,
                    'commission_rate_origin' => 'subcategory',
                ],
                [
                    'subcategory_id' => 95,
                    'sales_channel_id' => 4,
                    'sales_channel' => 'amazon.it',
                    'commission_rate' => 18,
                    'commission_rate_origin' => 'subcategory',
                ],
            ],
        ];

        /** @var SubcategoryContextDto $dto_success */
        $dto_success = $this->getSerializer()->denormalize($subcategory_data, SubcategoryContextDto::class);
        $subcategory_updater = $this->getTestedInstance();

        $this->assert('Update subcategory success');
        $subcategory_id = 95;

        $subcategory_before_update = $this->fetchSubcategory($subcategory_id);
        $this->array($subcategory_before_update)
            ->string($subcategory_before_update['souscategorie'])
            ->isEqualTo('Enceintes encastrables')
            ->string($subcategory_before_update['code_douanier'])
            ->isEqualTo('12345678')
            ->string($subcategory_before_update['code_ecotaxe'])
            ->isEqualTo('01010');

        $this->given($subcategory_updater->update($dto_success));

        $subcategory_after_update = $this->fetchSubcategory($subcategory_id);
        $this->array($subcategory_after_update)
            ->string($subcategory_after_update['souscategorie'])
            ->isEqualTo($dto_success->name)
            ->string($subcategory_after_update['code_douanier'])
            ->isEqualTo($dto_success->custom_code)
            ->string($subcategory_after_update['code_ecotaxe'])
            ->isEqualTo($dto_success->ecotax_code);

        $this->assert('Update subcategory with unknown warranty type');
        $dto = $dto_success;
        $dto->warranty_type = 'YATCH';
        $this->exception(function () use ($subcategory_updater, $dto) {
            $subcategory_updater->update($dto);
        })
            ->isInstanceOf(InternalErrorException::class)
            ->message->contains('Integrity constraint violation');

        $this->assert('Update subcategory with empty warranty type');
        $dto = $dto_success;
        $dto->warranty_type = '';
        $this->exception(function () use ($subcategory_updater, $dto) {
            $subcategory_updater->update($dto);
        })
            ->isInstanceOf(InternalErrorException::class)
            ->hasMessage('Invalid parameters');

        $this->assert('Update subcategory with null warranty type');
        $dto = $dto_success;
        $dto->warranty_type = null;
        $this->exception(function () use ($subcategory_updater, $dto) {
            $subcategory_updater->update($dto);
        })
            ->isInstanceOf(InternalErrorException::class)
            ->hasMessage('Invalid parameters');

        $this->assert('Update subcategory with null charged_delivery');
        $dto = $dto_success;
        $dto->charged_delivery = null;
        $this->exception(function () use ($subcategory_updater, $dto) {
            $subcategory_updater->update($dto);
        })
            ->isInstanceOf(InternalErrorException::class)
            ->hasMessage('Invalid parameters');
    }

    protected function getPdo(): LegacyPdo
    {
        return $this->getContainer()->get(LegacyPdo::class);
    }

    protected function fetchSubcategory(int $subcategory_id)
    {
        $sql = <<<SQL
        SELECT *
        FROM backOffice.CTG_TXN_souscategorie
        WHERE id = :subcategory_id
        SQL;

        return $this->getPdo()->fetchOne($sql, ['subcategory_id' => $subcategory_id]);
    }
}
