### Serializer

On utilise un `Adapter` qui couvre les fonctionnalités du serializer de symfony.

Celui-ci permet d'utiliser de la configuration spécifique à notre projet tout en gardant une interface passe-plat avec
le Serializer de symfony.

#### Exemple d'utilisation via injection de dépendance du constructeur

```php
namespace App\MyNamespace;

// ...
use App\Adapter\Serializer\SerializerInterface;

class MyClass
{
    public function __construct(SerializerInterface $serializer)
    {
        $this->serializer = $serializer;
    }

    public function myMethod(): void
    {
            // À partir d'une string JSON (Exemple: Controller)
            $dto = $this->serializer->deserialize('[{"foo":"bar"}]', MyDto::class, 'json');
            
            // À partir d'un array PHP (Exemple, requete BDD)
            $dto = $this->serializer->denormalize(['foo' => 'bar'], MyDto::class);
    }
}
```

#### Exemple d'utilisation via injection de dépendance interface + trait

```php
namespace App\MyNamespace;

// ...
use App\Adapter\Serializer\SerializerAwareInterface;
use App\Adapter\Serializer\SerializerAwareTrait;

class MyClass  implements SerializerAwareInterface
{
    use SerializerAwareTrait;

    public function myMethod(): void
    {
            // À partir d'une string JSON (Exemple: Controller)
            $dto = $this->serializer->deserialize('[{"foo":"bar"}]', MyDto::class, 'json');
            
            // À partir d'un array PHP (Exemple, requete BDD)
            $dto = $this->serializer->denormalize(['foo' => 'bar'], MyDto::class);
    }
}
```

### Exemple de décodage de JSON dans un DTO/Entity

```php
namespace App\MyNamespace;

// ...
use App\DataLoader\Type\JsonType;

class MyDto
{
    /**
     * Pour decoder une string JSON dans un array
     * 
     * @var array|JsonType 
     */
    public array $foo;

    /**
     * Pour decoder une string JSON dans une entité (Via interface)
     * 
     * @var MySecondDto
     */
    public array $toy;

    /**
     * Pour decoder un array JSON dans un array d'entité (Via interface)
     * 
     * @var MySecondDto[] 
     */
    public array $toys;
}
```

```php
namespace App\MyNamespace;

// ...
use App\Adapter\Serializer\Type\JsonDenormalizableInterface;

/**
 * La classe qui doit accepter du JSON doit implémenter l'interface `JsonStringAwareInterface`
 */
class MySecondDto implements JsonDenormalizableInterface
{
    public int $id;
}
```
